worker_processes auto;

events {
    worker_connections 1024;
}

http {
    # 定义上游服务，即我们的 yuxugong 应用集群
    # 'yuxugong' 是 docker-compose.yml 中定义的服务名
    # Docker 的内部 DNS 会自动将 yuxugong 解析到所有 yuxugong 容器的 IP 地址
    upstream yuxugong_backend {
        # Docker 会自动做负载均衡，无需列出具体IP
        # 5000 是 yuxugong 容器内部监听的端口
        server yuxugong:5000;
    }

    # 定义 arq-ui 上游服务
    upstream arq_ui_backend {
        # arq-ui 容器内部监听的端口是 8000
        server arq-ui:8000;
    }

    # HTTP (80) 强制跳转到 HTTPS (443) - 主应用域名
    server {
        listen 80;
        server_name yuxugong.staging.ai-smart-thing.cloud; # <-- 修改为你的域名
        return 301 https://$host$request_uri;
    }

    # ARQ-UI 专用域名 HTTP 服务配置 (无 HTTPS)
    server {
        listen 80;
        server_name yuxugong-arq.staging.ai-smart-thing.cloud;

        location / {
            # 将所有请求代理到 arq-ui 服务
            proxy_pass http://arq_ui_backend/;

            # 设置代理头信息
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 处理 WebSocket 连接（如果 arq-ui 使用 WebSocket）
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";

            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
    }

    # HTTPS 服务配置
    server {
        listen 443 ssl;
        server_name yuxugong.staging.ai-smart-thing.cloud; # <-- 修改为你的域名

        # SSL 证书配置
        ssl_certificate /etc/nginx/certs/yuxugong.staging.ai-smart-thing.cloud.pem; # <-- 修改为你的证书文件名
        ssl_certificate_key /etc/nginx/certs/yuxugong.staging.ai-smart-thing.cloud.key; # <-- 修改为你的私钥文件名

        # SSL 优化配置 (可选)
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;

        location / {
            # 将所有请求代理到上游的 yuxugong 服务集群
            proxy_pass http://yuxugong_backend;

            # 设置一些必要的代理头信息
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}