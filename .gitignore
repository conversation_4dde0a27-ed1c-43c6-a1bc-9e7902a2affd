# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Testing
.coverage
.pytest_cache/
htmlcov/
.tox/
.coverage.*
coverage.xml
*.cover

# MyPy
.mypy_cache/
.dmypy.json
dmypy.json

# Logs
*.log
logs/
log/

# Local development
.env.local
.env.*.local
docker-compose.override.yml 