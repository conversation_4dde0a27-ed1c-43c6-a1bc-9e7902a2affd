# docker-compose.prod.yml (生产环境专用)
version: '3.8'

services:
  yuxugong:
    deploy:
      replicas: 1 # 生产环境跑 1 个实例
      update_config:
        parallelism: 1
        delay: 10s
        order: start-first # 滚动更新
        failure_action: rollback
      resources:
        limits:
          memory: 3G
        reservations:
          memory: 1G

  nginx:
    volumes:
      # 挂载生产环境的 nginx 配置
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro

  arq-ui:
    environment:
      - REDIS_HOST=r-2zes07dtcbmyl6yq9a.redis.rds.aliyuncs.com
      - REDIS_PASSWORD=
      - REDIS_PORT=6379
      - REDIS_SSL=False
      - REDIS_DB=2
      - QUEUE_NAME=prod_queue
      - TZ=Asia/Shanghai
    deploy:
      replicas: 1 # 生产环境运行 1 个 arq-ui 实例
      update_config:
        parallelism: 1
        delay: 10s
        order: start-first # 滚动更新
        failure_action: rollback
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.1'