from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI, APIRouter
from src.api.chat_api import router as chat_router
from src.api.bazi_api import router as bazi_router
from src.api.user_api import router as user_router
from src.api.order_info_api import router as order_info_router
from src.api.product_api import router as product_router
from src.api.workflow_api import router as workflow_router
from src.api.app_api import router as app_router
from src.api.apple_callback_api import router as apple_callback_router
from src.api.purchase_api import router as purchase_router
from src.api.activity_api import router as activity_router
from src.common.session import init_db
from src.client.apple.apple_client import apple_client

from src.utils.log_util import logger
import uvicorn
from multiprocessing import freeze_support


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Lifecycle manager for the FastAPI application"""
    # Startup
    await init_db()
    logger.info("Database initialized")

    yield

    # Shutdown
    logger.info("Application shutting down")
    await apple_client.close()
    logger.info("Apple client connections closed")


app = FastAPI(
    title="YuXuGong API",
    description="API for YuXuGong application",
    version="1.0.0",
    lifespan=lifespan,
)

# Create a global router with v1 prefix
api_router = APIRouter(prefix="/v1")

# Include all routers under the global router
api_router.include_router(chat_router)
api_router.include_router(bazi_router)
api_router.include_router(user_router)
api_router.include_router(order_info_router)
api_router.include_router(product_router)
api_router.include_router(workflow_router)
api_router.include_router(app_router)
api_router.include_router(apple_callback_router)
api_router.include_router(purchase_router)
api_router.include_router(activity_router)
# Include the global router in the app
app.include_router(api_router)


if __name__ == "__main__":
    freeze_support()
    uvicorn.run(app, host="0.0.0.0", port=5001)
