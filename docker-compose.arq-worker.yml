version: '3.8'

networks:
  arq-worker-network:
    driver: bridge

services:
  arq-worker:
    image: crpi-2794svloicid00on-vpc.cn-beijing.personal.cr.aliyuncs.com/ai-smart-thing/yuxugong:${TAG}
    restart: unless-stopped
    stop_grace_period: 60s
    environment:
      - env=${ENV}
      - ENV=${ENV}
      - TZ=Asia/Shanghai
    volumes:
      - ./logs:/app/logs
    command: bash /app/deploy/start_arq_worker.sh
    healthcheck:
      # ARQ worker health check - check if the process is running
      test: ["CMD", "pgrep", "-f", "arq.*WorkerSettings"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - arq-worker-network
