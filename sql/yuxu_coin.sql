CREATE TABLE IF NOT EXISTS user_wallets (
  id INT AUTO_INCREMENT PRIMARY KEY,
  wallet_id VARCHAR(36) UNIQUE NOT NULL COMMENT '钱包ID',
  user_id VARCHAR(36) UNIQUE NOT NULL COMMENT '用户ID，关联users表',
  balance INT NOT NULL DEFAULT 0 COMMENT '可用玉虚币余额',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户虚拟货币钱包表';

CREATE TABLE IF NOT EXISTS yuxucoin_transactions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  transaction_log_id VARCHAR(36) UNIQUE NOT NULL COMMENT '交易流水唯一ID',
  wallet_id VARCHAR(36) NOT NULL COMMENT '钱包ID',
  user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
  transaction_type VARCHAR(50) NOT NULL COMMENT '交易类型: RECHARGE, CONSUME, REFUND, REWARD, ADJUSTMENT, FREEZE, UNFREEZE',
  amount INT NOT NULL COMMENT '交易金额，正数为增加，负数为减少',
  balance_before INT NOT NULL COMMENT '交易前可用余额',
  balance_after INT NOT NULL COMMENT '交易后可用余额',
  related_entity_type VARCHAR(50) COMMENT '关联业务类型 (e.g., order, activity)',
  related_entity_id VARCHAR(255) COMMENT '关联业务ID (e.g., 订单ID, 活动ID)',
  description VARCHAR(255) COMMENT '交易描述，活动description，订单产品，账户注销',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_wallet_id (wallet_id),
  INDEX idx_user_id (user_id),
  INDEX idx_transaction_type (transaction_type),
  INDEX idx_related_entity (related_entity_type, related_entity_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='玉虚币交易流水表';