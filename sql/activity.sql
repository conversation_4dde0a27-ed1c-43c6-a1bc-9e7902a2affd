CREATE TABLE IF NOT EXISTS activity (
  id INT AUTO_INCREMENT PRIMARY KEY,
  activity_id CHAR(36) UNIQUE NOT NULL COMMENT 'UUID主键，活动唯一ID',
  activity_name VARCHAR(100) NOT NULL COMMENT '活动名称，e.g., "新用户注册好礼"',
  description TEXT COMMENT '活动详细描述',
  activity_type VARCHAR(50) COMMENT '活动类型 REGISTER_GIFT FIRST_RECHARGE_BONUS',
  start_time DATETIME NOT NULL COMMENT '活动开始时间',
  end_time DATETIME COMMENT '活动结束时间，NULL表示永不结束',
  status VARCHAR(50) NOT NULL DEFAULT 'PLANNED' COMMENT '活动状态: PLANNED, ACTIVE, EXPIRED, ARCHIVED',
  rules VARCHAR(256) COMMENT '活动规则配置 (JSON格式，高度灵活)',
  rewards VARCHAR(256) NOT NULL COMMENT '活动奖励配置 (JSON格式)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_activity_type (activity_type),
  INDEX idx_status_time (status, start_time, end_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动定义表';

CREATE TABLE IF NOT EXISTS activity_record (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  user_activity_id VARCHAR(36) UNIQUE NOT NULL COMMENT 'UUID主键',
  user_id VARCHAR(36) NOT NULL COMMENT '参与活动的用户ID',
  activity_id VARCHAR(36) NOT NULL COMMENT '参与的活动ID',
  status VARCHAR(50) NOT NULL COMMENT '参与结果: SUCCESS, FAILED',
  reward_details VARCHAR(256) COMMENT '实际发放的奖励详情，用于审计',
  notes VARCHAR(256) COMMENT '备注，e.g., "用户注册成功，自动发放奖励"',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_activity (user_id, activity_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户活动参与记录表';