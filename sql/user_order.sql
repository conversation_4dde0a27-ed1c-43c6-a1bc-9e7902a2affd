CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id CHAR(36) UNIQUE NOT NULL COMMENT 'UUID主键',
  apple_user_id CHAR(36) UNIQUE NOT NULL COMMENT 'Apple用户ID',
  phone_number VARCHAR(20),
  account_id VARCHAR(10) UNIQUE NULL COMMENT '用户账户ID，客户端展示用',
  username VARCHAR(50) NOT NULL,
  email VARCHAR(100),
  is_active TINYINT(1) DEFAULT 1 COMMENT '0 禁用 1 启用',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS user_profiles (
  id INT AUTO_INCREMENT PRIMARY KEY,
  profile_id CHAR(36) UNIQUE NOT NULL COMMENT 'UUID主键',
  user_id CHAR(36) NOT NULL COMMENT '用户ID',
  birth_year VARCHAR(4),
  birth_month VARCHAR(2),
  birth_day VARCHAR(2),
  birth_hour VARCHAR(2),
  birth_minutes VARCHAR(2),
  gender TINYINT,
  name VARCHAR(50) NOT NULL,
  role_group VARCHAR(50) NOT NULL DEFAULT 'other',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_profile_id (profile_id),
  INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `products` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` varchar(255) NOT NULL COMMENT 'uuid',
  `apple_product_id` varchar(255) NULL COMMENT 'Apple产品ID',
  `product_name` varchar(255) NOT NULL,
  `description` text,
  `product_type` VARCHAR(255) NOT NULL COMMENT 'consumable, non_consumable, subscription',
  `product_group` VARCHAR(32) NOT NULL DEFAULT 'REPORT' COMMENT '产品分组YUXU_COIN REPORT',
  `payment_method` VARCHAR(512) NOT NULL COMMENT '支付方式 & 价格',
  `price` decimal(10,2) unsigned NOT NULL,
  `currency` char(3) NOT NULL COMMENT 'ISO货币代码',
  `localized_title` varchar(255) NOT NULL,
  `localized_desc` text NOT NULL,
  `is_active` tinyint(1) DEFAULT '1' COMMENT '0下架 1在售',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `type` smallint unsigned NOT NULL DEFAULT '0' COMMENT '0基础类型产品 1打包销售类型产品',
  PRIMARY KEY (`id`),
  UNIQUE KEY `product_id` (`product_id`),
  UNIQUE KEY `apple_product_id` (`apple_product_id`),
  KEY `idx_product_type` (`product_type`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
;

-- 订单表
CREATE TABLE IF NOT EXISTS orders (
  id INT AUTO_INCREMENT PRIMARY KEY,
  order_type VARCHAR(60) COMMENT "订单类型，新购，续费",
  order_id CHAR(36) UNIQUE NOT NULL COMMENT 'UUID主键',
  user_id CHAR(36) NOT NULL,
  profile_id CHAR(36) NOT NULL DEFAULT '' COMMENT '用户profile_id',
  product_id VARCHAR(255) NOT NULL,
  status VARCHAR(64),
  amount INT UNSIGNED NOT NULL,
  single_price INT NOT NULL,
  total_price INT NOT NULL,
  currency VARCHAR(10) NOT NULL,
  environment ENUM('production', 'sandbox') NOT NULL,
  extra_info JSON COMMENT 'Additional order information' DEFAULT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_order_user (user_id),
  INDEX idx_order_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO orders (order_id, user_id, profile_id, product_id, status, amount, single_price, total_price, currency, environment) VALUES
('39e94eb7-7577-4f79-bb0b-c1e1b4bbe699', '601c9239-713f-4c85-8d16-b580a36b8f5c', '8e3ce676-f03d-4476-800e-09ab8858cabc', 'c933c1cf-61dd-41d2-9c45-0d192f9e02c6', 'PURCHASED', 1, 9, 9, 'CNY', 'production');

-- 交易表（核心StoreKit2数据）
CREATE TABLE `transactions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `transaction_id` varchar(255) NOT NULL COMMENT 'StoreKit交易ID',
  `original_transaction_id` varchar(255) DEFAULT NULL COMMENT '初始交易id',
  `transaction_type` varchar(255) DEFAULT NULL,
  `order_id` varchar(36) NOT NULL,
  `environment` varchar(36) NOT NULL,
  `price` int DEFAULT NULL COMMENT '价格',
  `currency` varchar(50) DEFAULT 'CNY' COMMENT '币种',
  `purchase_date` datetime NOT NULL,
  `expires_date` datetime DEFAULT NULL COMMENT '订阅过期时间',
  `revocation_date` datetime DEFAULT NULL COMMENT '退款时间',
  `revocation_reason` varchar(100) DEFAULT NULL COMMENT '退款原因',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `transaction_id` (`transaction_id`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_expires_date` (`expires_date`)
) ENGINE=InnoDB AUTO_INCREMENT=75 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
;


-- 用户订阅表，会员用
CREATE TABLE IF NOT EXISTS subscriptions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  subscription_id CHAR(36) UNIQUE NOT NULL,
  user_id CHAR(36) NOT NULL,
  product_id VARCHAR(255) NOT NULL,
  latest_order_id VARCHAR(255) NOT NULL,
  start_date DATETIME NOT NULL,
  expires_date DATETIME NOT NULL,
  cancellation_reason ENUM('user_canceled', 'price_increase', 'other') DEFAULT NULL,
  sale_type INT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_subscription (user_id, expires_date),
  INDEX idx_subscription_product (product_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 阿里云workflow表
CREATE TABLE IF NOT EXISTS workflow_executions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  workflow_name VARCHAR(255) NOT NULL,
  workflow_execution_id VARCHAR(255) NOT NULL,
  execution_status VARCHAR(255) NOT NULL,
  callback_token VARCHAR(255) DEFAULT NULL,
  workflow_input JSON DEFAULT NULL,
  workflow_output JSON DEFAULT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_workflow_execution_id (workflow_execution_id),
  INDEX idx_execution_status (execution_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- workflow order 表
CREATE TABLE IF NOT EXISTS workflow_orders (
  id INT AUTO_INCREMENT PRIMARY KEY,
  workflow_execution_id VARCHAR(255) NOT NULL,
  order_id VARCHAR(255) NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_workflow_execution_id (workflow_execution_id),
  INDEX idx_order_id (order_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
