CREATE TABLE IF NOT EXISTS bazi_attempt (
    id INT AUTO_INCREMENT PRIMARY KEY,
    attempt_id VARCHAR(64) UNIQUE NOT NULL,
    year INT,
    month INT,
    day INT,
    hour INT,
    gender TINYINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_attempt_id (attempt_id),
    UNIQUE INDEX idx_year_month_day_hour_gender (year, month, day, hour, gender)
);

CREATE TABLE IF NOT EXISTS bazi_result (
    id INT AUTO_INCREMENT PRIMARY KEY,
    result_id VARCHAR(64) NOT NULL,
    bazi_attempt_id VARCHAR(64) NOT NULL,
    result_type VARCHAR(64) NOT NULL,
    version VARCHAR(64) NOT NULL,
    status VARCHAR(64) NOT NULL DEFAULT 'pending',
    output JSON,
    error JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_result_id (result_id),
    INDEX idx_bazi_attempt_id_type_version (bazi_attempt_id, result_type, version)
);
