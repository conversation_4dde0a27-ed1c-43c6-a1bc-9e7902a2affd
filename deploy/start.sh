#!/bin/bash

# Set default environment
ENV=${ENV:-"dev"}

# Create logs directory if it doesn't exist
mkdir -p /app/logs

# Generate timestamp for log file
TIMESTAMP=$(date '+%Y%m%d-%H-%M')
LOG_FILE="/app/logs/yuxugong-init-${TIMESTAMP}.log"

echo "Starting FastAPI application in $ENV environment..."
echo "Logging to: ${LOG_FILE}"

# Start the FastAPI application using uvicorn with config file and redirect output
exec uvicorn main:app \
    --host 0.0.0.0 \
    --port 5000 \
    --workers 4 \
    --log-level info \
    --timeout-keep-alive 75 \
    --proxy-headers \
    >> "${LOG_FILE}" 2>&1