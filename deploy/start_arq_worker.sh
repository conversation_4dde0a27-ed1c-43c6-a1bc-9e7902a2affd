#!/bin/bash

# ARQ Worker启动脚本
# 支持多环境配置

# 设置脚本目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_DIR="$( cd "$SCRIPT_DIR/.." &> /dev/null && pwd )"

# 进入项目目录
cd "$PROJECT_DIR"

# 检查环境变量
if [ -z "$ENV" ]; then
    echo "未设置ENV环境变量，使用默认值: dev"
    export ENV=dev
fi

echo "启动环境: $ENV"

# 根据环境加载对应的.env文件
case "$ENV" in
    "dev"|"DEV"|"Dev")
        ENV_FILE=".env.dev"
        ;;
    "staging"|"STAGING"|"Staging")
        ENV_FILE=".env.staging"
        ;;
    "prod"|"PROD"|"Prod"|"production"|"PRODUCTION"|"Production")
        ENV_FILE=".env.prod"
        ;;
    *)
        echo "未知环境: $ENV，使用默认的 .env.dev"
        ENV_FILE=".env.dev"
        ;;
esac

# 检查.env文件是否存在并加载
if [ -f "$ENV_FILE" ]; then
    echo "加载环境文件: $ENV_FILE"
    set -a
    source "$ENV_FILE"
    set +a
else
    echo "警告: 环境文件 $ENV_FILE 不存在，请确保文件存在"
    exit 1
fi

# 设置ARQ Worker上下文环境变量（用于日志识别）
export ARQ_WORKER_CONTEXT=true

# 启动ARQ Worker
echo "启动ARQ Worker..."
echo "队列: ${ARQ_QUEUE_NAME}"
echo "Redis: ${REDIS_HOST}:${REDIS_PORT}/${REDIS_BROKER_DB}"
echo "日志文件前缀: yuxugong-arq-worker"

# Generate timestamp for log file
TIMESTAMP=$(date '+%Y%m%d-%H-%M')
LOG_FILE="/app/logs/yuxugong-arq-worker-init-${TIMESTAMP}.log"

# 使用arq命令启动worker
if [ "$ENV" != "dev" ]; then
    exec arq src.arq_app.WorkerSettings >> "${LOG_FILE}" 2>&1
else
    exec arq src.arq_app.WorkerSettings
fi
