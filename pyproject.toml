[project]
name = "yuxugong"
version = "1.0.0"
description = "API for YuXuGong application"
authors = [
    {name = "YuXuGong Team", email = "<EMAIL>"},
]
readme = "README.md"
requires-python = ">=3.10"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
]
dependencies = [
    "fastapi==0.115.8",
    "uvicorn==0.34.0",
    "pydantic==2.10.6",
    "pydantic[email]==2.10.6",
    "httpx==0.28.1",
    "python-dotenv==1.0.1",
    "lunar-python==1.3.12",
    "pyyaml==6.0.1",
    "sqlalchemy==2.0.38",
    "pymysql==1.1.1",
    "cryptography==43.0.3",
    "aiofiles==24.1.0",
    "types-aiofiles==24.1.0.20250326",
    "volcengine-python-sdk[ark]==1.0.124",
    "json-repair==0.36.1",
    "aiomysql==0.2.0",
    "sqlalchemy[asyncio]==2.0.38",
    "oss2==2.19.1",
    "python-jose[cryptography]==3.4.0",
    "requests==2.32.3",
    "aiohttp==3.11.18",
    "alibabacloud-fnf20190315==1.1.3",
    "apscheduler==3.10.4",
    "langgraph>=0.3.5",
    "langchain-openai>=0.3.8",
    "jinja2>=3.1.3",
    "packaging==24.2",
    "tenacity==9.1.2",
    "python-multipart==0.0.20",
    "pyjwt>=2.10.1",
    "jsonschema>=4.0.0",
    "app-store-server-library[async]>=1.0.0",
    "redis>=4.2.0,<6.0.0",
    "arq>=0.26.3",
    "deprecated>=1.2.14",
]

[project.optional-dependencies]
worker = [
    # ARQ doesn't need flower, gevent, or greenlet
    # Monitoring can be done through Redis directly or custom solutions
]
dev = [
    "black==24.1.1",
    "mypy==1.8.0",
    "types-requests==2.31.0.20240125",
    "flake8==7.1.1",
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
]
test = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
]

[project.scripts]
yuxugong = "main:app"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = [
    "lunar_python.*",
    "volcengine_python_sdk.*",
    "oss2.*",
    "alibabacloud_fnf20190315.*",
    "apscheduler.*",
    "langgraph.*",
    "langchain_openai.*",
    "deprecated.*",
]
ignore_missing_imports = true

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    ".venv",
    "venv",
    ".mypy_cache",
    ".pytest_cache",
    "build",
    "dist",
]

[tool.pytest.ini_options]
testpaths = ["tests", "src/tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
asyncio_mode = "auto"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.ruff]
target-version = "py310"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]

[tool.ruff.isort]
known-first-party = ["src"]

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build.targets.wheel]
packages = ["src"] 
