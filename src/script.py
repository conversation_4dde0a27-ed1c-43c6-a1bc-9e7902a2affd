from uuid import uuid4

sql_base = (
    "INSERT INTO `products` (`product_id`,`apple_product_id`,`product_name`,`description`,`product_type`,"
    "`price`,`currency`,`localized_title`,`localized_desc`,`is_active`,`created_at`,`updated_at`,`type`) "
    "VALUES ('{}','{}','{}','{}','consumable',{},'USD','{}','{}',1,'{}','{}',1);"
)
for i in range(6):
    sql = sql_base.format(
        uuid4(),
        f"all_reports_{i+1}",
        f"八字算命-解锁{i+1}份报告",
        f"八字算命-解锁{i+1}份报告",
        (i + 1) * 6,
        f"八字算命-解锁{i+1}份报告",
        f"八字算命-解锁{i+1}份报告",
        "2025-04-01 05:19:56",
        "2025-04-01 05:19:56",
    )
    print(sql)
