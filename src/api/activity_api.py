from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query

from src.common.auth import <PERSON><PERSON><PERSON><PERSON>
from src.common.decorators import handle_exceptions
from src.models.common_reponse import CommonResponse
from src.models.activity_model import ActivityListResponse, ActivityQueryParams
from src.services.activity_service import activity_service
from src.utils.log_util import logger


# Create router without v1 prefix since it's handled by main.py
router = APIRouter(prefix="/activity", tags=["Activity"])


@router.get("", response_model=CommonResponse)
@handle_exceptions(error_message="Failed to get user activities")
async def get_user_activities(
    activity_status: Optional[str] = Query(None, description="Filter activities by status"),
    page: Optional[int] = Query(1, ge=1, description="Page number for pagination"),
    limit: Optional[int] = Query(20, ge=1, le=100, description="Number of items per page"),
    params: Optional[str] = Query(None, description="Additional filter parameters as JSON string"),
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> CommonResponse:
    """
    Get user activities with participation status
    
    This endpoint displays the current user's activity participation status in the mobile app.
    It queries active/valid activities and the user's participation records, returning combined data with pagination.
    
    Query Parameters:
    - activity_status (optional): Filter activities by status
    - page (optional): Page number for pagination (default: 1)
    - limit (optional): Number of items per page (default: 20, max: 100)
    - params (optional): Additional filter parameters as JSON string
    
    Headers:
    - Authorization: Bearer token required
    
    Returns:
    - data: List of activities with user participation status
    - pagination: Pagination information (total, page, limit, has_next, has_prev)
    """
    try:
        # Validate pagination parameters
        if page < 1:
            page = 1
        if limit < 1:
            limit = 1
        elif limit > 100:
            limit = 100
        
        # Get user activities from service
        result = await activity_service.get_user_activities(
            user_id=current_user_id,
            activity_status=activity_status,
            page=page,
            limit=limit
        )
        
        # Convert to the expected response format
        response_data = {
            "data": [activity.dict() for activity in result.data],
            "pagination": result.pagination.dict()
        }
        
        logger.info(f"Successfully retrieved {len(result.data)} activities for user {current_user_id}")
        return CommonResponse.success(data=response_data)
        
    except Exception as e:
        logger.error(f"Error getting activities for user {current_user_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get user activities: {str(e)}"
        )
