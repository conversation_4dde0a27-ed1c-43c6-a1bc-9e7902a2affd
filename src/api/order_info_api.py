from typing import Any

from fastapi import APIRouter, Depends
from src.models.order_model import (
    CreateOrderRequest,
    OrderListResponse,
    UpdateOrderStatusRequest,
    OrderResponse,
    OrderCountResponse,
)
from src.services.order_service import order_service
from src.common.auth import JWTAuth
from src.models.order_model import OrderQueryParams
from src.common.decorators import handle_exceptions

router = APIRouter(prefix="/order_info", tags=["Order Info"])


@router.post("", response_model=OrderResponse)
@handle_exceptions(error_message="create order failed")
async def create_new_order(
    request: CreateOrderRequest,
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> OrderResponse:
    order_response = await order_service.create_order_with_different_product(
        user_id=current_user_id,
        product_id=request.product_id,
        profile_id=request.profile_id,
        report_version=request.report_version,
    )
    return order_response


@router.get("", response_model=OrderListResponse)
@handle_exceptions(error_message="query orders failed")
async def get_orders(
    query_params: OrderQueryParams = Depends(),
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> OrderListResponse:
    """
    通用订单查询接口
    支持多种过滤条件
    """
    orders_response = await order_service.query_orders(
        filters=query_params, user_id=current_user_id
    )

    return OrderListResponse(orders=orders_response)


@router.put("/{order_id}", response_model=OrderResponse)
@handle_exceptions(error_message="update order status failed")
async def update_order(
    order_id: str,
    request: UpdateOrderStatusRequest,
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> Any:
    """
    更新订单状态
    """
    order = await order_service.update_order_status(
        order_id=order_id,
        status=request.status,
        user_id=current_user_id,
    )
    return OrderResponse.model_validate(order)


@router.post("/{order_id}/deliver")
@handle_exceptions(error_message="deliver order failed")
async def deliver_order(
    order_id: str,
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> dict:
    """
    交付订单
    根据订单类型执行相应的交付逻辑（生成报告或交付玉虚币）
    """
    await order_service.deliver_order(order_id)
    return {"message": "Order delivered successfully", "order_id": order_id}


@router.get("/count", response_model=OrderCountResponse)
@handle_exceptions(error_message="get order count failed")
async def get_user_order_count(
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> OrderCountResponse:
    """
    获取用户的订单数量

    Args:
        current_user_id: 从JWT token解析出的用户ID

    Returns:
        OrderCountResponse: 包含订单数量的响应
    """
    order_count = await order_service.get_user_order_count(current_user_id)
    return OrderCountResponse(order_count=order_count)
