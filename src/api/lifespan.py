"""
FastAPI lifespan event handlers for Redis pool management
"""

from typing import <PERSON><PERSON><PERSON><PERSON>ator
from contextlib import asynccontextmanager
from fastapi import FastAPI
from src.common.arq_redis_pool import redis_pool_manager, redis_health_check
from src.utils.log_util import logger


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    FastAPI lifespan context manager for Redis pool management

    This ensures the Redis pool is properly initialized when the application starts
    and cleaned up when the application shuts down.
    """
    # Startup
    logger.info("Starting FastAPI application...")

    try:
        # Initialize the Redis pool
        await redis_pool_manager.get_pool()
        logger.info("Redis pool initialized for FastAPI application")

        # Perform health check
        is_healthy = await redis_health_check()
        if is_healthy:
            logger.info("Redis health check passed")
        else:
            logger.warning("Redis health check failed, but continuing startup")

        yield  # Application is running

    except Exception as e:
        logger.error(f"Failed to initialize Redis pool: {e}")
        # Don't prevent startup, but log the error
        yield

    finally:
        # Shutdown
        logger.info("Shutting down FastAPI application...")

        try:
            # Close the Redis pool
            await redis_pool_manager.close_pool()
            logger.info("Redis pool closed for FastAPI application")
        except Exception as e:
            logger.error(f"Error closing Redis pool: {e}")


async def startup_event() -> None:
    """
    Alternative startup event handler (for older FastAPI versions)
    Use this if lifespan context manager is not available
    """
    logger.info("FastAPI startup event triggered")

    try:
        # Initialize the Redis pool
        await redis_pool_manager.get_pool()
        logger.info("Redis pool initialized via startup event")

        # Perform health check
        is_healthy = await redis_health_check()
        if is_healthy:
            logger.info("Redis health check passed")
        else:
            logger.warning("Redis health check failed")

    except Exception as e:
        logger.error(f"Failed to initialize Redis pool in startup event: {e}")


async def shutdown_event() -> None:
    """
    Alternative shutdown event handler (for older FastAPI versions)
    Use this if lifespan context manager is not available
    """
    logger.info("FastAPI shutdown event triggered")

    try:
        # Close the Redis pool
        await redis_pool_manager.close_pool()
        logger.info("Redis pool closed via shutdown event")
    except Exception as e:
        logger.error(f"Error closing Redis pool in shutdown event: {e}")
