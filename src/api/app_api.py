from typing import Dict
from fastapi import APIRouter, HTTPException
from src.models.version_model import (
    VersionCheckRequest,
    VersionCheckData,
    Platform,
)
from src.services.version_service import version_service
from src.utils.log_util import logger

router = APIRouter(prefix="/app", tags=["app"])


@router.get("/health")
async def health_check() -> Dict[str, str]:
    """
    Health check endpoint for load balancer and rolling updates
    """
    return {"status": "SUCCESS"}


@router.post("/check-version", response_model=VersionCheckData)
async def check_version(request: VersionCheckRequest) -> VersionCheckData:
    """
    Check app version and return update policy

    This endpoint checks the client app version against the backend configuration
    and returns appropriate update policy with localized messages.

    - **platform**: Platform type (ios/android)
    - **appVersion**: Current app version
    - **language**: Optional language code for localized messages (default: zh-CN)

    Returns:
    - **policy**: Update policy (0=no update, 1=suggested, 2=forced)
    - **latestVersion**: Latest available version
    - **updateUrl**: App store URL when update is needed
    - **title/description**: Localized update dialog text
    """
    try:
        logger.info(
            f"Version check request: platform={request.platform}, "
            f"version={request.appVersion}, language={request.language}"
        )

        # Validate platform
        if request.platform not in [Platform.IOS, Platform.ANDROID]:
            raise HTTPException(
                status_code=400, detail="Invalid platform. Must be 'ios' or 'android'"
            )

        # Check version using service
        version_data = version_service.check_version(request)

        logger.info(
            f"Version check result: policy={version_data.policy}, "
            f"latest={version_data.latestVersion}"
        )

        return version_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in version check: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
