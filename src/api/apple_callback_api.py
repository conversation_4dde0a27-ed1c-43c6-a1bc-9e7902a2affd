from fastapi import APIRouter, HTTPException

from src.utils.log_util import logger
from src.common.decorators import handle_exceptions
from src.services.apple_purchase_service import apple_purchase_service
from src.models.apple_callback_models import AppleIAPCallbackRequest
from src.services.order_service import order_service

# Create router
router = APIRouter(prefix="/apple", tags=["Apple IAP"])


@router.post("/iap/callback")
@handle_exceptions(error_message="Failed to process Apple IAP callback")
async def apple_iap_callback(
    apple_iap_callback_request: AppleIAPCallbackRequest,
) -> None:
    """
    接收苹果IAP支付回调通知
    """
    try:
        logger.info(f"Apple IAP callback request: {apple_iap_callback_request}")
        signed_payload = apple_iap_callback_request.signedPayload
        order = await apple_purchase_service.callback_verify(
            signed_payload,
        )
        if order:
            await order_service.deliver_order(order.order_id)
    except Exception as e:
        logger.error(f"Error processing Apple IAP callback: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to process callback: {str(e)}"
        )
