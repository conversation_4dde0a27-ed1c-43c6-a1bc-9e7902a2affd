from typing import List, Optional
from fastapi import APIRouter, Depends, UploadFile, File, HTTPException
from fastapi.responses import Response

from src.common.auth import J<PERSON><PERSON><PERSON>
from src.models.bazi_model import PRODUCT_NAME_TO_REPORT_TYPE, ReportType
from src.models.common_reponse import CommonResponse
from src.models.product_model import CSVImportResponse, ProductGroup
from src.services.product_service import product_service
from pydantic import BaseModel

from src.utils.log_util import logger

router = APIRouter(prefix="/products", tags=["products"])


class ProductReportTypesResponse(BaseModel):
    product_id: str
    report_types: List[ReportType]


@router.get("/{product_id}/report-types", response_model=ProductReportTypesResponse)
async def get_product_report_types(
    product_id: str, report_version: str = "1.0"
) -> ProductReportTypesResponse:
    """
    Get the list of report types associated with a product ID.
    The product_id should be one of the keys in PRODUCT_NAME_TO_REPORT_TYPE mapping.
    """
    product = await product_service.get_product_by_product_id(product_id)

    report_types: List[ReportType] = []
    if product:
        product_name = product.product_name
        if product.sale_type == 1:
            product_name = "八字算命-解锁全部报告"
        if product_name in PRODUCT_NAME_TO_REPORT_TYPE:
            if report_version != "1.0" and product_name == "八字算命-解锁全部报告":
                report_types = PRODUCT_NAME_TO_REPORT_TYPE[product_name] + [
                    ReportType.BASIC,
                    ReportType.DAYUN,
                ]
            else:
                report_types = PRODUCT_NAME_TO_REPORT_TYPE[product_name]
    return ProductReportTypesResponse(product_id=product_id, report_types=report_types)


@router.get("/export/csv")
async def download_products_csv() -> Response:
    """
    Download all products as a CSV file.
    """
    try:
        csv_content = await product_service.export_products_to_csv()

        # Return CSV file as response
        return Response(
            content=csv_content,
            media_type="text/csv",
            headers={"Content-Disposition": "attachment; filename=products.csv"},
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to export products: {str(e)}"
        )


@router.post("/import/csv", response_model=CSVImportResponse)
async def upload_products_csv(file: UploadFile = File(...)) -> CSVImportResponse:
    """
    Upload a CSV file to update products.

    Expected CSV format:
    - Headers: product_id, apple_product_id, product_name, description, product_type,
               product_group, payment_method, price, currency, localized_title,
               localized_desc, is_active, sale_type
    - product_id and product_name are required fields
    - If product_id exists, the product will be updated; otherwise, a new product will be created
    """
    try:
        # Validate file type
        if not file.filename or not file.filename.endswith(".csv"):
            raise HTTPException(status_code=400, detail="File must be a CSV file")

        # Read file content
        content = await file.read()
        csv_content = content.decode("utf-8")

        # Import products
        result = await product_service.import_products_from_csv(csv_content)
        return result

    except UnicodeDecodeError:
        raise HTTPException(status_code=400, detail="File encoding must be UTF-8")
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to import products: {str(e)}"
        )


@router.get("", response_model=CommonResponse)
async def get_products(
    product_group: Optional[ProductGroup] = None,
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> CommonResponse:
    """
    Get all products
    """
    try:
        products = await product_service.get_products(product_group)
        return CommonResponse.success(data=products)
    except Exception as e:
        logger.error(f"Error getting products: {e}", exc_info=True)
        return CommonResponse.error(message="获取产品失败")
