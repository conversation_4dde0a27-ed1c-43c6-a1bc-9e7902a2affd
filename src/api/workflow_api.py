from deprecated import deprecated  # type: ignore
from fastapi import APIRouter, Depends, HTTPException
from arq.jobs import Job
from src.common.auth import J<PERSON>TAuth
from src.models.product_model import ProductGroup
from src.services.user_service import user_service
from src.services.bazi_manager import bazi_manager
from src.services.order_service import order_service
from src.common.decorators import handle_exceptions
from src.models.order_model import (
    CreateOrderRequest,
    OrderResponse,
    VerifyPurchaseRequest,
    OrderWithProductResponse,
)
from src.models.workflow_model import (
    ArqWorkflowTaskStatusResponse,
    ArqWorkflowTaskListResponse,
    RetryAllFailedTasksResponse,
    RetryTaskResult,
    RetryTaskRequest,
)
from src.models.bazi_model import (
    BaziReportAfterPurchaseRequest,
    BaziReportAfterPurchaseResponse,
    PRODUCT_NAME_TO_REPORT_TYPE,
    BaziReportAfterPurchaseItem,
    BAZIReportRequest,
)
from src.utils.constants import OrderStatus, WorkflowTaskStatus, BaziResultStatus
from src.models.user_model import ProfileParams
from src.utils.log_util import logger
from typing import List

from src.common.arq_redis_pool import get_redis_pool
from src.configure.arq_config import arq_config
from src.utils.arq_task_utils import generate_job_id_with_order_id_and_type
from src.models.order_model import OrderQueryParams
from src.services.product_service import product_service
from src.models.bazi_model import ReportType
from src.client.database.bazi_result_client import bazi_result_client
from src.services.report_service import report_service

router = APIRouter(prefix="/workflow", tags=["Workflow"])


@deprecated(reason="Use order_info_api.create_order instead")
def map_arq_status_to_workflow_status(arq_status: str) -> WorkflowTaskStatus:
    """
    将ARQ任务状态映射到工作流任务状态枚举

    ARQ状态包括: 'queued', 'in_progress', 'complete', 'failed', 'not_found', 'deferred'
    """
    status_mapping = {
        "queued": WorkflowTaskStatus.PENDING,
        "deferred": WorkflowTaskStatus.PENDING,
        "in_progress": WorkflowTaskStatus.STARTED,
        "complete": WorkflowTaskStatus.SUCCESS,
        "failed": WorkflowTaskStatus.FAILURE,
        "not_found": WorkflowTaskStatus.REVOKED,  # 任务不存在或已过期
    }

    return status_mapping.get(arq_status, WorkflowTaskStatus.FAILURE)


@router.post("/execution/create_order", response_model=OrderResponse)
@handle_exceptions(error_message="create order failed")
async def execution_create_order(
    create_order_request: CreateOrderRequest,
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> OrderResponse:
    order_response: OrderResponse = await order_service.create_order(
        user_id=current_user_id,
        product_id=create_order_request.product_id,
        profile_id=create_order_request.profile_id or "",
        report_version=create_order_request.report_version,
    )

    return order_response


@deprecated(reason="Use purchase_api.verify_purchase instead")
@router.post("/execution/verify_purchase")
@handle_exceptions(error_message="verify purchase failed")
async def execution_verify_purchase(
    callback_request: VerifyPurchaseRequest,
    user_id: str = Depends(JWTAuth.get_current_user_id),
) -> dict:
    try:
        order = await order_service.verify_purchase(
            order_id=callback_request.order_id,
            transaction_id=callback_request.transaction_id,
            product_id=callback_request.product_id,
            user_id=user_id,
        )
    except Exception as e:
        return {"status": 1, "message": str(e)}

    await order_service.deliver_order(order.order_id)
    return {"status": 0, "message": "支付校验成功"}


@router.get(
    "/execution/get_order_status", response_model=BaziReportAfterPurchaseResponse
)
async def get_purchased_bazi_report(
    request: BaziReportAfterPurchaseRequest = Depends(),
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> BaziReportAfterPurchaseResponse:
    """
    获取支付后生成的八字报告
    """

    profile_id = request.profile_id
    product_id = request.product_id

    response = BaziReportAfterPurchaseResponse(
        profile_id=request.profile_id,
        bazi_product_order_result_list=[],
    )

    try:
        # Verify profile exists and belongs to user
        profile_params = ProfileParams(
            profile_id=request.profile_id, user_id=current_user_id
        )
        current_profiles = await user_service.get_profiles_by_profile_params(
            profile_params
        )
        if len(current_profiles) == 0:
            logger.warning(f"Profile {profile_id} not found")
            return response
        current_profile = current_profiles[0]

        # Check if the order is paid, the order status should be purchased
        # the product here only has product with type 0
        profile_id_to_product_orders = await order_service.get_purchased_reports(
            current_user_id, profile_id
        )
        if (
            profile_id not in profile_id_to_product_orders
            or not profile_id_to_product_orders[profile_id]
        ):
            logger.warning(f"Profile {profile_id} not found")
            return response
        product_orders: List[OrderWithProductResponse] = profile_id_to_product_orders[
            profile_id
        ]
        if product_id is not None:
            product_orders = [
                product_order
                for product_order in product_orders
                if product_order.product.product_id == product_id
            ]

        for product_order in product_orders:
            bazi_report_after_purchase_item = BaziReportAfterPurchaseItem(
                product=product_order.product,
                order=product_order.order,
                bazi_result=None,
            )
            if product_order.order.order_status == OrderStatus.SUCCESS.value:
                order_extra_info = product_order.order.extra_info
                if order_extra_info is None:
                    order_extra_info = {}
                report_version = order_extra_info.get("report_version", "1.0")
                # Get the result_id associated with this order
                bazi_base_data_request = BAZIReportRequest(
                    year=str(current_profile.birth_year),
                    month=str(current_profile.birth_month),
                    day=str(current_profile.birth_day),
                    hour=str(current_profile.birth_hour),
                    minute=str(current_profile.birth_minutes),
                    gender=current_profile.gender,
                    report_version=report_version,
                )

                result_type = PRODUCT_NAME_TO_REPORT_TYPE[
                    product_order.product.product_name
                ]
                existing_calc = await bazi_manager.find_existing_calculation(
                    bazi_base_data_request, result_type[0].value
                )
                if not existing_calc:
                    logger.warning(
                        f"Report not found for this order, "
                        f"order_id: {product_order.order.order_id}, "
                        f"product_id: {product_id}, "
                        f"profile_id: {profile_id}, "
                        f"result_type: {result_type}, "
                        f"report_version: {report_version}"
                    )
                    continue
                result_id = existing_calc.result_id
                result = await bazi_manager.get_calculation_result(result_id)
                bazi_report_after_purchase_item.bazi_result = result
            response.bazi_product_order_result_list.append(
                bazi_report_after_purchase_item
            )
        return response
    except HTTPException:
        raise
    except Exception as e:
        error_message = "获取八字报告结果失败"
        logger.error(f"{error_message}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=error_message)


@router.get("/task/status/{task_id}", response_model=ArqWorkflowTaskStatusResponse)
@handle_exceptions(error_message="get workflow task status failed")
async def get_workflow_task_status(
    task_id: str,
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> ArqWorkflowTaskStatusResponse:
    """
    获取工作流任务状态

    Args:
        task_id: Arq任务ID
        current_user_id: 当前用户ID

    Returns:
        任务状态信息
    """
    try:

        # Get shared Redis pool to check job status
        redis_pool = await get_redis_pool()

        # Create Job instance to check status
        job = Job(job_id=task_id, redis=redis_pool)

        # Get job status
        job_status = await job.status()

        # 构建响应
        response = ArqWorkflowTaskStatusResponse(
            task_id=task_id,
            status=map_arq_status_to_workflow_status(job_status.value),
        )

        if job_status.value == "complete":
            try:
                result = await job.result()
                response.result = result
            except Exception:
                # Job completed but result might be expired
                response.result = {"status": "completed", "message": "Result expired"}
        elif job_status.value == "not_found":
            response.error = "Task not found or expired"

        logger.info(
            f"Task status queried: task_id={task_id}, status={response.status.value}, user_id={current_user_id}"
        )

        return response

    except Exception as e:
        logger.error(
            f"Failed to get task status: task_id={task_id}, error={str(e)}",
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")


@router.post("/task/cancel/{task_id}")
@handle_exceptions(error_message="cancel workflow task failed")
async def cancel_workflow_task(
    task_id: str,
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> dict:
    """
    取消工作流任务

    Args:
        task_id: Arq任务ID
        current_user_id: 当前用户ID

    Returns:
        取消结果
    """
    try:
        # Get shared Redis pool to cancel job
        redis_pool = await get_redis_pool()

        # Create Job instance to abort
        job = Job(job_id=task_id, redis=redis_pool)

        # Try to abort the job
        aborted = await job.abort()

        if aborted:
            logger.info(f"Task cancelled: task_id={task_id}, user_id={current_user_id}")
            return {"status": "success", "message": "任务已取消", "task_id": task_id}
        else:
            logger.warning(
                f"Task could not be cancelled (may not exist or already completed): task_id={task_id}, user_id={current_user_id}"
            )
            return {
                "status": "warning",
                "message": "任务可能已完成或不存在",
                "task_id": task_id,
            }

    except Exception as e:
        logger.error(
            f"Failed to cancel task: task_id={task_id}, error={str(e)}", exc_info=True
        )
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")


@router.get("/tasks/active", response_model=ArqWorkflowTaskListResponse)
@handle_exceptions(error_message="get active workflow tasks failed")
async def get_active_workflow_tasks(
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> ArqWorkflowTaskListResponse:
    """
    获取当前用户的活跃工作流任务列表

    Args:
        current_user_id: 当前用户ID

    Returns:
        活跃任务列表
    """
    try:
        # We can check queued jobs
        redis_pool = await get_redis_pool()

        # Get queued jobs from ARQ
        queued_jobs = await redis_pool.queued_jobs(
            queue_name=arq_config.get_queue_name()
        )

        user_tasks = []
        total_count = 0

        for job_def in queued_jobs:
            task_info = {
                "task_id": job_def.job_id if hasattr(job_def, "job_id") else "unknown",
                "name": job_def.function if hasattr(job_def, "function") else "unknown",
                "worker": "arq_worker",
                "args": list(job_def.args) if hasattr(job_def, "args") else [],
                "kwargs": job_def.kwargs if hasattr(job_def, "kwargs") else {},
                "time_start": (
                    job_def.enqueue_time.isoformat()
                    if hasattr(job_def, "enqueue_time")
                    else None
                ),
            }
            user_tasks.append(task_info)
            total_count += 1

        logger.info(
            f"Active tasks queried: user_id={current_user_id}, count={total_count}"
        )

        return ArqWorkflowTaskListResponse(tasks=user_tasks, total=total_count)

    except Exception as e:
        logger.error(
            f"Failed to get active tasks: user_id={current_user_id}, error={str(e)}",
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail=f"获取活跃任务失败: {str(e)}")


@router.post("/retry")
@handle_exceptions(error_message="retry workflow failed")
async def retry_workflow(
    retry_task_request: RetryTaskRequest,
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> RetryTaskResult:
    """
    重新执行工作流（用于失败的订单）

    Args:
        retry_task_request: 重试任务请求
        current_user_id: 当前用户ID

    Returns:
        重试结果
    """
    order_id = retry_task_request.order_id
    force_retry_unfailed = retry_task_request.force_retry_unfailed

    try:
        # 获取订单信息

        order = await order_service.get_order_by_order_id(order_id)

        if not order:
            raise HTTPException(status_code=404, detail="订单不存在")

        if not force_retry_unfailed:
            retryable_status = [
                OrderStatus.PURCHASE_FAILED.value,
                OrderStatus.FAILED.value,
                OrderStatus.CANCELED.value,
            ]
            if order.status not in retryable_status:
                raise HTTPException(
                    status_code=400,
                    detail=f"订单状态不支持重试，当前订单状态为{order.status}，可重试订单状态为{retryable_status}",
                )

        # 获取报告版本
        order_extra_info = order.extra_info or {}
        report_version = order_extra_info.get("report_version", "1.0")

        # 根据 profile_id 获取 profile 信息
        profile_params = ProfileParams(
            profile_id=order.profile_id, user_id=order.user_id
        )
        current_profiles = await user_service.get_profiles_by_profile_params(
            profile_params
        )
        if len(current_profiles) == 0:
            raise HTTPException(status_code=404, detail="Profile 不存在")
        current_profile = current_profiles[0]

        # 根据生辰信息获取 bazi_attempt_id
        existing_attempt = await bazi_manager.attempt_client.get_by_date_and_gender(
            year=int(current_profile.birth_year),
            month=int(current_profile.birth_month),
            day=int(current_profile.birth_day),
            hour=int(current_profile.birth_hour),
            gender=current_profile.gender,
        )

        product = await product_service.get_product_by_product_id(order.product_id)
        if not product:
            raise HTTPException(status_code=404, detail="Product 不存在")
        if not product.product_group == ProductGroup.REPORT.value:
            raise HTTPException(status_code=400, detail="Product 不是八字产品")

        product_name = product.product_name
        if product.sale_type == 1:
            product_name = "八字算命-解锁全部报告"
        if report_version != "1.0" and product_name == "八字算命-解锁全部报告":
            report_types = PRODUCT_NAME_TO_REPORT_TYPE[product_name] + [
                ReportType.BASIC,
                ReportType.DAYUN,
            ]
        else:
            report_types = PRODUCT_NAME_TO_REPORT_TYPE[product_name]

        # 获取所有相关的 bazi_result 并将状态置为 FAILED
        if existing_attempt:
            for result_type in report_types:
                bazi_result = (
                    await bazi_result_client.get_latest_by_attempt_id_and_type(
                        existing_attempt.attempt_id, result_type.value, report_version
                    )
                )

                if bazi_result and bazi_result.status != BaziResultStatus.FAILED.value:
                    # 将状态更新为 FAILED
                    bazi_result.status = BaziResultStatus.FAILED.value
                    bazi_result.error = {"message": "Marked as failed before retry"}
                    await bazi_result_client.update_not_none_element(
                        bazi_result.result_id, bazi_result
                    )
                    logger.info(
                        f"Marked bazi_result as FAILED before retry: order_id={order_id}, result_id={bazi_result.result_id}, "
                        f"attempt_id={existing_attempt.attempt_id}, result_type={result_type.value}"
                    )

        job_id = await report_service.generate_report(order)

        logger.info(
            f"Workflow retried: order_id={order_id}, task_id={job_id}, user_id={order.user_id}"
        )

        return RetryTaskResult(order_id=order_id, task_id=job_id, status="success")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to retry workflow: order_id={order_id}, error={str(e)}",
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail=f"重试工作流失败: {str(e)}")


@router.post("/retry-all-failed", response_model=RetryAllFailedTasksResponse)
@handle_exceptions(error_message="retry all failed tasks failed")
async def retry_all_failed_tasks(
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> RetryAllFailedTasksResponse:
    """
    重试所有失败的工作流任务

    Args:
        current_user_id: 当前用户ID

    Returns:
        重试结果汇总
    """
    try:
        failed_orders = []

        # 查询FAILED状态的订单
        failed_query = OrderQueryParams(status=OrderStatus.FAILED.value)
        failed_orders_list = await order_service.query_orders(
            failed_query, current_user_id
        )
        failed_orders.extend(failed_orders_list)

        # 查询PURCHASE_FAILED状态的订单
        purchase_failed_query = OrderQueryParams(
            status=OrderStatus.PURCHASE_FAILED.value
        )
        purchase_failed_orders_list = await order_service.query_orders(
            purchase_failed_query, current_user_id
        )
        failed_orders.extend(purchase_failed_orders_list)

        total_failed_orders = len(failed_orders)
        successful_retries = 0
        failed_retries = 0
        retry_results = []

        logger.info(
            f"Found {total_failed_orders} failed orders for user {current_user_id}"
        )

        # 为每个失败的订单启动重试任务
        for order in failed_orders:
            retry_result = RetryTaskResult(order_id=order.order_id, status="failed")

            try:
                # 获取报告版本
                order_extra_info = order.extra_info or {}
                report_version = order_extra_info.get("report_version", "2.0")

                # 启动新的工作流任务
                redis_pool = await get_redis_pool()

                task_result = await redis_pool.enqueue_job(
                    "process_order_workflow",
                    order_id=order.order_id,
                    user_id=current_user_id,
                    profile_id=order.profile_id,
                    product_id=order.product_id,
                    report_version=report_version,
                    _queue_name=arq_config.get_queue_name(),
                    _job_id=generate_job_id_with_order_id_and_type(
                        order.order_id, "process_order_workflow", "all"
                    ),
                )

                if task_result:
                    retry_result.task_id = task_result.job_id
                    retry_result.status = "success"
                    successful_retries += 1
                    logger.info(
                        f"Successfully retried order {order.order_id}, task_id={task_result.job_id}"
                    )
                else:
                    retry_result.error = "Failed to enqueue workflow task"
                    failed_retries += 1
                    logger.error(f"Failed to enqueue task for order {order.order_id}")

            except Exception as e:
                retry_result.error = str(e)
                failed_retries += 1
                logger.error(f"Failed to retry order {order.order_id}: {str(e)}")

            retry_results.append(retry_result)

        # 构建响应消息
        if total_failed_orders == 0:
            message = "没有找到失败的订单"
        elif successful_retries == total_failed_orders:
            message = f"成功重试了所有 {total_failed_orders} 个失败的订单"
        elif successful_retries > 0:
            message = f"成功重试了 {successful_retries} 个订单，{failed_retries} 个订单重试失败"
        else:
            message = f"所有 {total_failed_orders} 个订单重试都失败了"

        logger.info(
            f"Retry all failed tasks completed: user_id={current_user_id}, "
            f"total={total_failed_orders}, successful={successful_retries}, failed={failed_retries}"
        )

        return RetryAllFailedTasksResponse(
            total_failed_orders=total_failed_orders,
            successful_retries=successful_retries,
            failed_retries=failed_retries,
            retry_results=retry_results,
            message=message,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to retry all failed tasks: user_id={current_user_id}, error={str(e)}",
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail=f"重试所有失败任务失败: {str(e)}")
