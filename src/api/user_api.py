from typing import Optional

from fastapi import APIRouter, HTTPException, Depends
from datetime import timedelta

from src.common.decorators import handle_exceptions
from src.models.common_reponse import CommonResponse
from src.utils.log_util import logger
from src.models.user_model import (
    FirstRegisterGiftResponse,
    UserResponse,
    SendVerificationRequest,
    VerifyCodeRequest,
    LoginRequest,
    LoginResponse,
    ProfileRequest,
    ProfileResponse,
    UserInfoResponse,
    UserUpdateRequest,
    AppleLoginRequest,
    ProfileParams,
    ProfileUpdateRequest,
    GiveYuxuCoinRequest,
    GiveYuxuCoinResponse,
)
from src.models.verification import VerificationCodeResponse
from src.services.user_service import user_service
from src.services.verification_service import verification_service
from src.services.activity_service import activity_service
from src.common.auth import JWTAuth
from typing import List


# Create router
router = APIRouter(prefix="/users", tags=["Users"])


@router.get("/yuxu/balance", response_model=CommonResponse)
async def get_user_balance(
    user_id: str = Depends(JWTAuth.get_current_user_id),
) -> CommonResponse:
    """
    Get user balance
    """
    try:
        yuxu_balance = await user_service.get_user_balance(user_id)
        return CommonResponse.success(data={"yuxu_balance": yuxu_balance})
    except Exception as e:
        logger.error(f"Error getting user {user_id} balance: {e}", exc_info=True)
        return CommonResponse.error(message="获取余额失败")


# Endpoints
@router.post("/send-verification", response_model=VerificationCodeResponse)
async def send_verification(
    request: SendVerificationRequest,
) -> VerificationCodeResponse:
    """
    Send a verification code to the phone number
    """
    verification_code_info = await verification_service.send_verification_code(
        request.phone_number
    )

    # In a production environment, we would not return the code
    # But for testing purposes, we'll return it
    return verification_code_info


@router.post("/verify-code", response_model=dict)
async def verify_verification_code(request: VerifyCodeRequest) -> dict:
    """
    Verify a code for a phone number
    """
    is_valid = await verification_service.verify_code(
        request.phone_number, request.verification_code
    )

    if not is_valid:
        raise HTTPException(status_code=400, detail="Invalid verification code")

    return {"success": True, "message": "Verification successful"}


@router.post("/login", response_model=LoginResponse)
@handle_exceptions(error_message="failed to login")
async def login(login_data: LoginRequest) -> LoginResponse:
    """
    Login with phone verification
    """

    # Verify the code
    is_valid = await verification_service.verify_code(
        login_data.phone_number, login_data.verification_code
    )

    if not is_valid:
        raise HTTPException(status_code=400, detail="Invalid verification code")
    yuxu_coin_reward = None
    # Get or create user
    user = await user_service.get_user_by_phone(login_data.phone_number)

    if not user:
        # Auto-register the user with a default username
        logger.info(
            f"Auto-registering user with phone number: {login_data.phone_number}"
        )
        user = await user_service.create_user(phone_number=login_data.phone_number)
        # 初始化用户钱包
        await user_service.init_user_wallet(user.user_id)
        # 发放首次注册奖励
        yuxu_coin_reward = await activity_service.give_first_register_gift(user.user_id)
    elif user.is_active == 0:
        await user_service.activate_user(user.user_id)

    # Create access token
    access_token_expires = timedelta(minutes=JWTAuth.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = JWTAuth.create_access_token(
        data={"sub": user.user_id}, expires_delta=access_token_expires
    )

    return LoginResponse(
        access_token=access_token,
        token_type="bearer",
        user_id=user.user_id,
        username=user.username,  # Ensure username is populated appropriately
        first_register_gift=(
            FirstRegisterGiftResponse(
                yuxu_coin=yuxu_coin_reward,
                title="首次注册奖励",
                description=f"欢迎加入玉虚宫，{yuxu_coin_reward/1000}金币已到账~",
            )
            if yuxu_coin_reward
            else None
        ),
    )


@router.post("/apple_login", response_model=LoginResponse)
@handle_exceptions(error_message="failed to apple login")
async def apple_login(login_data: AppleLoginRequest) -> LoginResponse:
    """
    Login or register with Apple ID
    """
    # Get or create user by Apple ID
    # This service method will need to be created
    user = await user_service.get_user_by_apple_user_id(
        apple_user_id=login_data.apple_user_id
    )
    yuxu_coin_reward = None

    if not user:
        logger.info(
            f"Auto-registering user with apple user id: {login_data.apple_user_id}"
        )
        user = await user_service.create_user(
            apple_user_id=login_data.apple_user_id,
            email_override=login_data.email,
            username_override=login_data.name,
        )
        # 初始化用户钱包
        await user_service.init_user_wallet(user.user_id)
        yuxu_coin_reward = await activity_service.give_first_register_gift(user.user_id)

    elif user.is_active == 0:
        await user_service.activate_user(user.user_id)

    # Create access token
    access_token_expires = timedelta(minutes=JWTAuth.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = JWTAuth.create_access_token(
        data={"sub": user.user_id}, expires_delta=access_token_expires
    )

    return LoginResponse(
        access_token=access_token,
        token_type="bearer",
        user_id=user.user_id,
        username=user.username,  # Ensure username is populated appropriately
        first_register_gift=(
            FirstRegisterGiftResponse(
                yuxu_coin=yuxu_coin_reward,
                title="首次注册奖励",
                description=f"欢迎加入玉虚宫，{yuxu_coin_reward}金币已到账~",
            )
            if yuxu_coin_reward
            else None
        ),
    )


@router.get("/profiles", response_model=List[ProfileResponse])
async def get_profiles(
    user_id: str = Depends(JWTAuth.get_current_user_id),
    query_params: ProfileParams = Depends(),
) -> List[ProfileResponse]:
    query_params.user_id = user_id
    profile_params = ProfileParams(**query_params.model_dump())
    current_profiles = await user_service.get_profiles_by_profile_params(profile_params)
    return current_profiles


@router.get("/profiles/{profile_id}", response_model=ProfileResponse)
async def get_profile(
    profile_id: str,
    user_id: str = Depends(JWTAuth.get_current_user_id),
) -> ProfileResponse:
    profile_params = ProfileParams(user_id=user_id, profile_id=profile_id)
    current_profile = await user_service.get_profiles_by_profile_params(profile_params)
    if not current_profile:
        raise HTTPException(status_code=400, detail="Profile not found")
    return current_profile[0]


@router.post("/profile", response_model=ProfileResponse)
async def create_profile(
    profile: ProfileRequest,
    user_id: str = Depends(JWTAuth.get_current_user_id),
) -> ProfileResponse:
    """
    Create a profile for a user
    - role_group: 角色分组（如 child, parent, spouse, other 等）
    """
    return await user_service.create_profile(profile, user_id)


@router.delete("/profile/{profile_id}", response_model=dict)
async def delete_profile(
    profile_id: str,
    user_id: str = Depends(JWTAuth.get_current_user_id),
) -> dict:
    """
    Delete a profile for a user
    """
    await user_service.delete_profile(profile_id, user_id)
    return {"success": True, "message": "Profile deleted"}


@router.put("/profiles/{profile_id}", response_model=ProfileResponse)
async def update_profile(
    profile_id: str,
    update_req: ProfileUpdateRequest,
    user_id: str = Depends(JWTAuth.get_current_user_id),
) -> ProfileResponse:
    """
    修改个人资料的name和role_group
    """
    updated_profile = await user_service.update_profile(profile_id, user_id, update_req)
    if not updated_profile:
        raise HTTPException(status_code=404, detail="Profile not found")
    return ProfileResponse.model_validate(updated_profile)


# Example protected endpoint
@router.get("/me", response_model=UserResponse)
async def read_users_me(
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> UserResponse:
    user = await user_service.get_user_by_id(current_user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    profiles = await user_service.get_user_profiles(current_user_id)
    return UserResponse(
        user_id=current_user_id,
        account_id=user.account_id,
        username=user.username,
        email=user.email,
        phone_number=user.phone_number,
        created_at=user.created_at,
        profiles=profiles,
    )


@router.put(
    "/me", response_model=UserResponse
)  # Changed from UserBase to UserResponse for consistency
async def update_user_me(
    user_update: UserUpdateRequest,
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> UserResponse:
    """
    Update current user's information (username, email)
    """
    updated_user = await user_service.update_user(current_user_id, user_update)
    if not updated_user:
        raise HTTPException(status_code=404, detail="User not found")

    # Fetch profiles to construct the full UserResponse
    profiles = await user_service.get_user_profiles(current_user_id)
    return UserResponse(
        user_id=updated_user.user_id,
        account_id=updated_user.account_id,
        username=updated_user.username,
        email=updated_user.email,
        phone_number=updated_user.phone_number,
        created_at=updated_user.created_at,
        profiles=profiles,
    )


# Using the decorator
@router.get("/protected")
@JWTAuth.jwt_required
async def protected_endpoint() -> None:
    # Your code here
    pass


# Using dependency injection
@router.get("/info", response_model=UserInfoResponse)
@handle_exceptions(error_message="failed to get user info")
async def get_user_info(
    profile_id: Optional[str] = None,
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> UserInfoResponse:
    """
    获取用户信息，包括个人资料和已购买的产品
    可选参数profile_id：指定获取特定个人资料的信息
    """
    user_info = await user_service.get_user_info(
        user_id=current_user_id, profile_id=profile_id
    )
    return user_info


@router.get("/exception")
async def exception_endpoint() -> None:
    raise HTTPException(status_code=500, detail="Internal Server Error")


@router.get("/privacy-policy", response_model=dict)
async def get_privacy_policy() -> dict:
    """
    获取隐私政策(markdown格式)
    """
    return await user_service.get_privacy_policy()


@router.get("/user-agreement", response_model=dict)
async def get_user_agreement() -> dict:
    """
    获取用户协议(markdown格式)
    """
    return await user_service.get_user_agreement()


@router.delete("/profiles", response_model=dict)
async def delete_profiles(
    profile_ids: List[str],
    user_id: str = Depends(JWTAuth.get_current_user_id),
) -> dict:
    """
    Batch delete profiles for a user
    """
    await user_service.delete_profiles(profile_ids, user_id)
    return {"success": True, "message": "Profiles deleted"}


@router.delete("/me", response_model=dict)
async def delete_user_me(
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> dict:
    """
    Delete current user's account and all associated data
    """
    try:
        await user_service.delete_user(current_user_id)
        return {"success": True, "message": "User account deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting user: {e}", exc_info=True)
        return {"success": False, "message": "注销失败"}


@router.get("/account/{account_id}", response_model=UserResponse)
async def get_user_by_account_id(
    account_id: str,
) -> UserResponse:
    """
    通过account_id获取用户信息（调试用）
    """

    user = await user_service.get_user_by_account_id(account_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    profiles = await user_service.get_user_profiles(user.user_id)
    return UserResponse(
        user_id=user.user_id,
        account_id=user.account_id,
        username=user.username,
        email=user.email,
        phone_number=user.phone_number,
        created_at=user.created_at,
        profiles=profiles,
    )


@router.post("/give-yuxu-coin", response_model=GiveYuxuCoinResponse)
@handle_exceptions(error_message="赠送积分失败")
async def give_yuxu_coin(
    request: GiveYuxuCoinRequest,
) -> GiveYuxuCoinResponse:
    """
    赠送玉虚币给指定用户（管理员接口）

    此接口主要用于管理人员给用户赠送积分，会记录交易流水并更新用户钱包余额。
    """
    return await user_service.give_yuxu_coin(request)
