from fastapi import APIRouter, HTTPException, BackgroundTasks
from src.services.llm_service import LLMService
from src.algorithm.bazi import get_bazi_data
from datetime import datetime
from src.utils.log_util import logger


from src.models.bazi_model import (
    BAZIBaseDataRequest,
    BAZIBaseDataResponse,
    DaYunResponse,
    DaYunReportRequest,
    SpecificReportRequest,  # 添加新的导入
    SpecificReportV2Request,
    ProcessBaziReportsRequest,
)
from src.services.bazi_service import bazi_service
from src.services.bazi_manager import bazi_manager
from src.models.bazi_model import BAZIResultModel, BAZIReportRequest
from src.models.bazi_model import ReportType

router = APIRouter(prefix="/bazi", tags=["Bazi"])
llm_service = LLMService()


@router.post("/data/basic", response_model=BAZIBaseDataResponse)
async def base_data(request: BAZIBaseDataRequest) -> BAZIBaseDataResponse:
    try:
        # 将请求参数转换为 datetime 对象
        birth = datetime(
            year=int(request.year),
            month=int(request.month),
            day=int(request.day),
            hour=int(request.hour),
            minute=int(request.minute),
        )

        # 调用八字计算函数
        bazi_info = get_bazi_data(birth=birth, gender=request.gender)

        return BAZIBaseDataResponse(data=bazi_info)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/data/dayun", response_model=DaYunResponse)
async def da_yun(request: BAZIBaseDataRequest) -> DaYunResponse:
    try:
        # 将请求参数转换为 datetime 对象
        birth_time = datetime(
            year=int(request.year),
            month=int(request.month),
            day=int(request.day),
            hour=int(request.hour),
            minute=int(request.minute),
        )

        res = await bazi_service.get_dayun_data(birth_time, request.gender)
        return DaYunResponse(da_yun_list=res)
    except Exception as e:
        logger.error("获取大运数据失败", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/report/basic", response_model=BAZIBaseDataResponse)
async def bazi_report(request: BAZIBaseDataRequest) -> BAZIBaseDataResponse:
    try:
        # 调用八字计算函数
        bazi_info = await bazi_service.get_bazi_report(request)
        return BAZIBaseDataResponse(data=bazi_info)
    except Exception as e:
        logger.error("生成八字报告失败", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/dayun/report", response_model=BAZIBaseDataResponse)
async def dayun_report(request: DaYunReportRequest) -> BAZIBaseDataResponse:
    try:
        report = await bazi_service.get_dayun_report(
            bazi_data=request.bazi_data,
            bazi_report=request.bazi_report,
            dayun_data=request.dayun_data,
        )
        return BAZIBaseDataResponse(data=report)
    except Exception as e:
        logger.error("生成大运分析报告失败", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/specific/report", response_model=BAZIBaseDataResponse)
async def specific_report(request: SpecificReportRequest) -> BAZIBaseDataResponse:
    try:
        report = await bazi_service.get_specific_report(
            bazi_data=request.bazi_data,
            bazi_report=request.bazi_report,
            da_yun_report=request.da_yun_report,
            specific_type=request.specific_type,
        )
        return BAZIBaseDataResponse(data=report)
    except Exception as e:
        logger.error("生成事业分析报告失败", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# only for testing
@router.post("/specific/report/v2", response_model=BAZIBaseDataResponse)
async def specific_report_v2(request: SpecificReportV2Request) -> BAZIBaseDataResponse:
    try:
        report = await bazi_service.get_specific_report_v2(request)
        return BAZIBaseDataResponse(data=report)
    except Exception as e:
        logger.error("生成专业分析报告失败", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# only for test
@router.post("/process/reports", response_model=BAZIBaseDataResponse)
async def process_bazi_reports(
    request: ProcessBaziReportsRequest,
) -> BAZIBaseDataResponse:
    """处理八字报告，将各个分析报告合并成综合分析报告"""
    try:
        report = await bazi_service.process_bazi_reports(
            bazi_data=request.bazi_data,
            kongwang=request.kongwang,
            yongshen=request.yongshen,
            shishen=request.shishen,
            shensha=request.shensha,
            gejv=request.gejv,
        )
        return BAZIBaseDataResponse(data=report)
    except Exception as e:
        logger.error("处理八字报告失败", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/report/{result_type}/async", response_model=BAZIResultModel)
async def bazi_report_async(
    result_type: str,
    request: BAZIReportRequest,
    background_tasks: BackgroundTasks,
) -> BAZIResultModel:
    if result_type not in [
        ReportType.BASIC.value,
        # dayun report is based on basic
        ReportType.DAYUN.value,
        # below are for the specific, and rely on basic and dayun
        ReportType.XUEYE.value,
        ReportType.SHIYE.value,
        ReportType.YINYUAN.value,
        ReportType.CAIYUN.value,
    ]:
        raise HTTPException(status_code=400, detail="Invalid result type")

    try:
        existing_calc = await bazi_manager.find_existing_calculation(
            request, result_type
        )
        if existing_calc:
            return existing_calc

        # Create new attempt
        result = await bazi_manager.create_initital_report(request, result_type)

        # Add calculation task to background
        background_tasks.add_task(
            bazi_manager.process_calculation_multi_version_with_timeout,
            request,
            result.attempt_id,
            result_type,
            result.result_id,
        )

        return result

    except Exception as e:
        error_message = f"处理异步{result_type}报告请求失败"
        logger.error(f"{error_message}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=error_message)


@router.get("/report/{result_id}", response_model=BAZIResultModel)
async def get_bazi_report_result(result_id: str) -> BAZIResultModel:
    try:
        return await bazi_manager.get_calculation_result(result_id)
    except HTTPException:
        raise
    except Exception as e:
        error_message = "获取八字报告结果失败"
        logger.error(f"{error_message}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=error_message)
