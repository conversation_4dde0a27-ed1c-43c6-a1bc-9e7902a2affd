from fastapi import APIRouter, Depends

from src.common.auth import <PERSON><PERSON><PERSON><PERSON>
from src.common.custom_exceptions import PurchaseFailedException
from src.models.common_reponse import CommonResponse
from src.models.order_model import VerifyPurchaseRequest
from src.models.purchase_model import PurchaseRequest
from src.services.apple_purchase_service import apple_purchase_service
from src.services.purchase_service import purchase_service
from src.utils.constants import ErrorCodes
from src.utils.log_util import logger
from src.services.order_service import order_service

router = APIRouter(prefix="/purchase", tags=["Purchase"])


@router.post("", response_model=CommonResponse)
async def purchase(
    request: PurchaseRequest,
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> CommonResponse:
    try:
        order = await purchase_service.purchase(
            request.order_id, request.payment_method, current_user_id
        )
        await order_service.deliver_order(order_id=order.order_id)
        return CommonResponse.success()
    except PurchaseFailedException as e:
        logger.error(f"Purchase failed: {e}", exc_info=True)
        return CommonResponse.error(e.error_code, str(e))
    except Exception as e:
        logger.error(f"Purchase failed: {e}", exc_info=True)
        return CommonResponse.error(ErrorCodes.SERVER_ERROR, str(e))


@router.post("/verify/apple", response_model=CommonResponse)
async def verify_purchase(
    request: VerifyPurchaseRequest,
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> CommonResponse:
    try:
        order = await apple_purchase_service.verify(
            request.order_id,
            request.transaction_id,
            request.product_id,
            current_user_id,
        )
        await order_service.deliver_order(order_id=order.order_id)
        return CommonResponse.success()
    except Exception as e:
        logger.error(f"Verify purchase failed: {e}", exc_info=True)
        return CommonResponse.error(error_code=1, message=str(e))
