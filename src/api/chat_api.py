import json
from typing import List, cast, Any, AsyncGenerator, Optional
from uuid import uuid4

from fastapi import APIRouter, HTTPException, Depends
from langchain_core.messages import BaseMessage, ToolMessage, AIMessageChunk
from starlette.responses import StreamingResponse

from src.chat.graph.builder import build_graph_with_memory
from src.common import JWTAuth
from src.models.chat_request import ChatMessage, StreamChatRequest
from src.models.chat_model import ChatRe<PERSON>, ChatResponse
from src.services.llm_service import LLMService
from src.services.mock_service import MockDataService
from src.client.database.user_query_client import user_query_client
from src.config import settings

router = APIRouter(tags=["Chat"])
llm_service = LLMService()
mock_service = MockDataService()
graph = build_graph_with_memory()


@router.post("/chat/completions", response_model=ChatResponse)
async def chat_completion(request: ChatRequest) -> ChatResponse:
    try:
        response = await llm_service.get_completion(request)
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/api/chat/stream")
async def chat_stream(
    request: StreamChatRequest,
    current_user_id: Optional[str] = Depends(JWTAuth.get_current_user_id_optional),
) -> StreamingResponse:
    thread_id = request.thread_id
    if thread_id == "__default__" or thread_id is None:
        thread_id = str(uuid4())

        # 获取用户ID和消息内容
    user_id = current_user_id
    messages = request.model_dump()["messages"]

    # 检查是否应该使用mock数据
    mock_file = mock_service.should_use_mock(messages)

    if mock_file:
        # 使用mock数据流式输出
        return StreamingResponse(
            mock_service.stream_mock_data(mock_file, thread_id),
            media_type="text/event-stream",
        )
    else:
        # 只有登录用户才进行查询次数限制和记录
        if user_id is not None:
            today_count = await user_query_client.get_today_query_count(user_id)
            if today_count >= settings.DAILY_CHAT_LIMIT:
                # 超过限制，返回提示消息
                return StreamingResponse(
                    _generate_limit_exceeded_message(thread_id),
                    media_type="text/event-stream",
                )

            # 记录用户查询
            if messages:
                # 获取最后一条用户消息作为查询内容
                user_message = None
                for msg in reversed(messages):
                    if msg["role"] == "user":
                        if isinstance(msg["content"], str):
                            user_message = msg["content"]
                        elif isinstance(msg["content"], list) and msg["content"]:
                            # 如果是列表，获取第一个text类型的内容
                            for item in msg["content"]:
                                if (
                                    hasattr(item, "type")
                                    and item.type == "text"
                                    and hasattr(item, "text")
                                ):
                                    user_message = item.text
                                    break
                        break

                if user_message:
                    await user_query_client.create_query(user_id, user_message)

        # 使用正常的workflow
        return StreamingResponse(
            _astream_workflow_generator(messages, thread_id),
            media_type="text/event-stream",
        )


async def _astream_workflow_generator(
    messages: List[ChatMessage],
    thread_id: str,
) -> AsyncGenerator[str, None]:
    input_ = {
        "messages": messages,
    }

    async for agent, _, event_data in graph.astream(
        input_,
        config={
            "thread_id": thread_id,
        },
        stream_mode=["messages", "updates"],
        subgraphs=True,
    ):
        if isinstance(event_data, dict):
            if "__interrupt__" in event_data:
                yield _make_event(
                    "interrupt",
                    {
                        "thread_id": thread_id,
                        "id": event_data["__interrupt__"][0].ns[0],
                        "role": "assistant",
                        "content": event_data["__interrupt__"][0].value,
                        "finish_reason": "interrupt",
                        "options": [
                            {"text": "Edit plan", "value": "edit_plan"},
                            {"text": "Start research", "value": "accepted"},
                        ],
                    },
                )
            continue
        message_chunk, message_metadata = cast(
            tuple[BaseMessage, dict[str, Any]], event_data
        )
        event_stream_message: dict[str, Any] = {
            "thread_id": thread_id,
            "agent": agent[0].split(":")[0],
            "id": message_chunk.id,
            "role": "assistant",
            "content": message_chunk.content,
        }
        if message_chunk.response_metadata.get("finish_reason"):
            event_stream_message["finish_reason"] = message_chunk.response_metadata.get(
                "finish_reason"
            )
        if isinstance(message_chunk, ToolMessage):
            # Tool Message - Return the result of the tool call
            event_stream_message["tool_call_id"] = message_chunk.tool_call_id
            yield _make_event("tool_call_result", event_stream_message)
        elif isinstance(message_chunk, AIMessageChunk):
            if message_chunk.tool_call_chunks:
                event_stream_message["tool_call_chunks"] = (
                    message_chunk.tool_call_chunks
                )
                yield _make_event("tool_call_chunks", event_stream_message)
            else:
                # AI Message - Raw message tokens
                yield _make_event("message_chunk", event_stream_message)


async def _generate_limit_exceeded_message(thread_id: str) -> AsyncGenerator[str, None]:
    """生成查询次数超限的消息流"""
    limit_message = {
        "thread_id": thread_id,
        "agent": "system",
        "id": str(uuid4()),
        "role": "assistant",
        "content": "抱歉，您今日的查询次数已达到上限（10次）。请明天再来使用我们的服务。",
        "finish_reason": "stop",
    }
    yield _make_event("message_chunk", limit_message)


def _make_event(event_type: str, data: dict[str, Any]) -> str:
    if data.get("content") == "":
        data.pop("content")
    return f"event: {event_type}\ndata: {json.dumps(data, ensure_ascii=False)}\n\n"
