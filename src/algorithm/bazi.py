from datetime import datetime
from typing import Dict, List, Tuple, Any

from lunar_python import Solar
from lunar_python import Eight<PERSON><PERSON>
from lunar_python.util import LunarUtil
from src.algorithm.wuxingnengliang import calculate_base_score
from src.algorithm.shenqiangshenruo import fast_strength_check
from src.algorithm.he_combinations import check_he_combinations
from src.algorithm.chong_combinations import check_chong_combinations

# 基础数据配置
CHANG_SHENG_OFFSET = {
    "甲": 1,
    "丙": 10,
    "戊": 10,
    "庚": 7,
    "壬": 4,
    "乙": 6,
    "丁": 9,
    "己": 9,
    "辛": 0,
    "癸": 3,
}


class BaziCalculator:
    def __init__(self, gender: int, birth_datetime: datetime) -> None:
        self.gender = gender
        self.birth_datetime = birth_datetime
        solar = Solar.fromDate(self.birth_datetime)
        self.lunar = solar.getLunar()
        self.eight_char = self.lunar.getEightChar()
        self.pillars = self.calculate_pillars()

    def calculate_pillars(self) -> Dict[str, <PERSON><PERSON>[str, str]]:
        """核心计算逻辑：获取四柱干支"""
        eight_char = self.eight_char
        return {
            "年柱": (eight_char.getYearGan(), eight_char.getYearZhi()),
            "月柱": (eight_char.getMonthGan(), eight_char.getMonthZhi()),
            "日柱": (eight_char.getDayGan(), eight_char.getDayZhi()),
            "时柱": (eight_char.getTimeGan(), eight_char.getTimeZhi()),
        }

    def build_pillar(self, ganzhi: Tuple[str, str], pillar_type: str) -> Dict[str, Any]:
        gan, zhi = ganzhi
        return {
            "天干": {
                "名称": gan + LunarUtil.WU_XING_GAN[gan],
                "主星": self.get_shishen_gan(pillar_type),
            },
            "地支": {
                "名称": zhi + LunarUtil.WU_XING_ZHI[zhi],
                "藏干": self.get_shishen_zhi(pillar_type),
            },
            "星运": self.get_longevity(pillar_type),
            "神煞": self.get_shensha(gan, zhi, pillar_type),
            "纳音": self.get_nayin(pillar_type),
            "自坐": self.get_self_seat(pillar_type),
        }

    def get_di_shi(self, gan: str, gan_index: int, zhi_index: int) -> Any:
        index = CHANG_SHENG_OFFSET.get(gan, 0) + (
            zhi_index if gan_index % 2 == 0 else -zhi_index
        )
        if index >= 12:
            index -= 12
        if index < 0:
            index += 12
        return EightChar.CHANG_SHENG[index]

    def get_self_seat(self, pillar_type: str) -> Any:
        if pillar_type == "年柱":
            return self.get_di_shi(
                self.eight_char.getYearGan(),
                self.lunar.getYearGanIndexExact(),
                self.lunar.getYearZhiIndexExact(),
            )
        elif pillar_type == "月柱":
            return self.get_di_shi(
                self.eight_char.getMonthGan(),
                self.lunar.getMonthGanIndexExact(),
                self.lunar.getMonthZhiIndexExact(),
            )
        elif pillar_type == "日柱":
            return self.get_di_shi(
                self.eight_char.getDayGan(),
                self.lunar.getDayGanIndexExact(),
                self.lunar.getDayZhiIndexExact(),
            )
        else:
            return self.get_di_shi(
                self.eight_char.getTimeGan(),
                self.lunar.getTimeGanIndex(),
                self.lunar.getTimeZhiIndex(),
            )

    def get_shensha(self, gan: str, zhi: str, pillar_type: str) -> List[str]:
        """计算神煞"""
        # 获取四柱信息
        year_gan, year_zhi = self.pillars["年柱"]
        month_gan, month_zhi = self.pillars["月柱"]
        day_gan, day_zhi = self.pillars["日柱"]
        hour_gan, hour_zhi = self.pillars["时柱"]

        # 构建八字数组，按照神煞计算要求的顺序：年干支月干支日干支时干支
        bazi = [
            year_gan,
            year_zhi,
            month_gan,
            month_zhi,
            day_gan,
            day_zhi,
            hour_gan,
            hour_zhi,
        ]

        # 获取年柱纳音五行
        nian_nayin = self.get_nayin(pillar_type)
        if nian_nayin:
            # 取五行属性（去掉具体描述，只保留五行）
            nian_nayin = nian_nayin[-1]  # 取最后一个字：金/木/水/火/土

        # 调用神煞计算函数
        # 对于地支，我们需要分别计算年月日时四柱的神煞
        # witch参数：1=年柱，2=月柱，3=日柱，4=时柱
        if pillar_type == "年柱":
            witch = 1
        elif pillar_type == "月柱":
            witch = 2
        elif pillar_type == "日柱":
            witch = 3
        elif pillar_type == "时柱":
            witch = 4
        else:
            return []

        # 调用神煞计算函数
        from src.algorithm.shensha import query_shen_sha

        return query_shen_sha([gan, zhi], bazi, self.gender == 1, witch, nian_nayin)

    def get_nayin(self, pillar_type: str) -> Any:
        """获取纳音五行"""
        if pillar_type == "年柱":
            return self.lunar.getYearNaYin()
        elif pillar_type == "月柱":
            return self.lunar.getMonthNaYin()
        elif pillar_type == "日柱":
            return self.lunar.getDayNaYin()
        elif pillar_type == "时柱":
            return self.lunar.getTimeNaYin()

    def get_longevity(self, pillar_type: str) -> Any:
        if pillar_type == "年柱":
            return self.eight_char.getYearDiShi()
        elif pillar_type == "月柱":
            return self.eight_char.getMonthDiShi()
        elif pillar_type == "日柱":
            return self.eight_char.getDayDiShi()
        else:
            return self.eight_char.getTimeDiShi()

    def get_kongwang(self, pillar_type: str) -> Any:
        if pillar_type == "年柱":
            return self.eight_char.getYearXunKong()
        elif pillar_type == "月柱":
            return self.eight_char.getMonthXunKong()
        elif pillar_type == "日柱":
            return self.eight_char.getDayXunKong()
        else:
            return self.eight_char.getTimeXunKong()

    def generate_full_chart(self) -> Dict[str, Any]:
        chart = {}

        for pillar_type in ["年柱", "月柱", "日柱", "时柱"]:
            data = self.build_pillar(self.pillars[pillar_type], pillar_type)
            data["空亡"] = self.get_kongwang(pillar_type)
            chart[pillar_type] = data

        return {"八字命盘": chart}

    def get_shishen_zhi(self, pillar_type: str) -> List[Dict[str, str]]:
        if pillar_type == "年柱":
            hide_gan = self.eight_char.getYearHideGan()
            hide_shishen = self.eight_char.getYearShiShenZhi()
        elif pillar_type == "月柱":
            hide_gan = self.eight_char.getMonthHideGan()
            hide_shishen = self.eight_char.getMonthShiShenZhi()
        elif pillar_type == "日柱":
            hide_gan = self.eight_char.getDayHideGan()
            hide_shishen = self.eight_char.getDayShiShenZhi()
        else:
            hide_gan = self.eight_char.getTimeHideGan()
            hide_shishen = self.eight_char.getTimeShiShenZhi()
        res = []
        for index in range(len(hide_gan)):
            res.append(
                {
                    "天干": hide_gan[index] + LunarUtil.WU_XING_GAN[hide_gan[index]],
                    "副星": hide_shishen[index],
                }
            )
        return res

    def get_shishen_gan(self, pillar_type: str) -> Any:
        eight_char = self.eight_char
        if pillar_type == "年柱":
            return eight_char.getYearShiShenGan()
        elif pillar_type == "月柱":
            return eight_char.getMonthShiShenGan()
        elif pillar_type == "日柱":
            return eight_char.getDayShiShenGan()
        else:
            return eight_char.getTimeShiShenGan()


def get_bazi_data(birth: datetime, gender: int) -> Dict[str, Any]:
    calculator = BaziCalculator(gender=gender, birth_datetime=birth)
    bazi_chart = calculator.generate_full_chart()

    # 获取四柱天干和地支列表
    tiangan_list = []
    dizhi_list = []
    for pillar in ["年柱", "月柱", "日柱", "时柱"]:
        gan = bazi_chart["八字命盘"][pillar]["天干"]["名称"][0]  # 取第一个字作为天干
        zhi = bazi_chart["八字命盘"][pillar]["地支"]["名称"][0]  # 取第一个字作为地支
        tiangan_list.append(gan)
        dizhi_list.append(zhi)

    # 计算合化关系
    he_results = check_he_combinations(tiangan_list, dizhi_list)
    bazi_chart["相合关系"] = he_results
    chong_results = check_chong_combinations(tiangan_list, dizhi_list)
    bazi_chart["相冲关系"] = chong_results
    # 计算身强身弱
    res = fast_strength_check(bazi_chart)
    qiangruo = {}
    qiangruo["身强身弱得分"] = res["score"]
    qiangruo["身强身弱"] = res["result"]
    qiangruo["得分依据"] = res["details"]
    bazi_chart["身强身弱"] = qiangruo

    # 计算五行能量
    res1 = calculate_base_score(bazi_chart)
    bazi_chart["五行能量"] = res1

    # 添加用户信息
    lunar = calculator.lunar
    user_info = {
        "出生日期（阳历）": birth.strftime("%Y年%m月%d日 %H时%M分"),
        "出生日期（农历）": f"{lunar.getYearInChinese()}年{lunar.getMonthInChinese()}月{lunar.getDayInChinese()}{lunar.getTimeZhi()}时",
    }
    bazi_chart["用户信息"] = user_info

    return bazi_chart


if __name__ == "__main__":
    # 使用示例
    birth_time = datetime(1997, 10, 23, 22, 16)
    # bazi_chart = get_bazi_data(birth_time=birth_time, gender=1)  # Add gender parameter
    # print(json.dumps(bazi_chart, ensure_ascii=False, indent=2))
    yun = EightChar.fromLunar(Solar.fromDate(birth_time).getLunar()).getYun(1)
    print(yun)
