# 初始化五行得分字典
from typing import Any

# 五行生克关系表
element_relations = {
    "木": {"生": "水", "克": "金"},  # 意思是 “水” 生 “木”，“金” 克 “木”
    "火": {"生": "木", "克": "水"},
    "土": {"生": "火", "克": "木"},
    "金": {"生": "土", "克": "火"},
    "水": {"生": "金", "克": "土"},
}


# 分值区间与状态
def evaluate_energy(score: int) -> str:
    if score < 1:
        return "缺失"
    if score == 1:
        return "偏弱"
    elif score == 2:
        return "平衡"
    else:
        return "极旺"


# 输入数据
input_data = {
    "八字命盘": {
        "年柱": {
            "天干": {"名称": "乙木", "主星": "伤官"},
            "地支": {"名称": "酉金", "藏干": [{"天干": "辛金", "副星": "正印"}]},
        },
        "月柱": {
            "天干": {"名称": "壬水", "主星": "比肩"},
            "地支": {
                "名称": "午火",
                "藏干": [
                    {"天干": "丁火", "副星": "正财"},
                    {"天干": "己土", "副星": "正官"},
                ],
            },
        },
        "日柱": {
            "天干": {"名称": "壬水", "主星": "日主"},
            "地支": {
                "名称": "午火",
                "藏干": [
                    {"天干": "丁火", "副星": "正财"},
                    {"天干": "己土", "副星": "正官"},
                ],
            },
        },
        "时柱": {
            "天干": {"名称": "辛金", "主星": "正印"},
            "地支": {
                "名称": "亥水",
                "藏干": [
                    {"天干": "壬水", "副星": "比肩"},
                    {"天干": "甲木", "副星": "食神"},
                ],
            },
        },
    }
}

# 五行天干对应
tian_gan_to_element = {
    "甲": "木",
    "乙": "木",
    "丙": "火",
    "丁": "火",
    "戊": "土",
    "己": "土",
    "庚": "金",
    "辛": "金",
    "壬": "水",
    "癸": "水",
}

# 五行地支权重
di_zhi_cang_gan_weights = {"主干": 1, "次干": 1}


# 解析天干和藏干的五行分值
def calculate_base_score(data: dict[str, Any]) -> dict[Any, Any]:
    five_elements = {"木": 0, "火": 0, "土": 0, "金": 0, "水": 0}
    for pillar_name, pillar in data["八字命盘"].items():
        # 提取天干并赋分
        tian_gan = pillar["天干"]["名称"]
        element = tian_gan_to_element[tian_gan[0]]
        five_elements[element] += 1  # 天干赋基础分为4

        dizhi = pillar["地支"]["名称"]
        element = dizhi[1]
        five_elements[element] += 1

        # 提取地支藏干并赋分
        # 简单考虑，不考虑藏干
        # for index, cang_gan in enumerate(pillar["地支"]["藏干"]):
        #     cang_gan_name = cang_gan["天干"]
        #     element = cang_gan_name[1]
        #     five_elements[element] += 1
    return summarize_five_elements(five_elements)


def summarize_five_elements(five_elements: dict[str, Any]) -> dict[Any, Any]:
    summary = {}
    for element, score in five_elements.items():
        state = evaluate_energy(score)
        summary[element] = {"score": score, "state": state}
    return summary
