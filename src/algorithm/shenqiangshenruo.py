# -*- coding: utf-8 -*-
"""
八字身强身弱快速判断程序 v3.1
输入要求：需预先生成天干十神与地支藏干十神
"""
from typing import Any

TIANGAN_WUXING_MAPPING = {
    "甲": "木",
    "乙": "木",
    "丙": "火",
    "丁": "火",
    "戊": "土",
    "己": "土",
    "庚": "金",
    "辛": "金",
    "壬": "水",
    "癸": "水",
}


def calculate_relationship(wuxing1: str, wuxing2: str) -> int:
    """
    根据五行关系，返回日干与其他干支的生克关系分值。
    :param wuxing1: 日主五行
    :param wuxing2: 其他天干或地支藏干的五行
    :return: 加分或减分
    """
    # 将天干五行转换为标准五行
    wuxing1 = TIANGAN_WUXING_MAPPING.get(wuxing1, wuxing1)  # 转换日干五行
    wuxing2 = TIANGAN_WUXING_MAPPING.get(wuxing2, wuxing2)  # 转换其他干支五行

    RELATIONSHIP_SCORE = {
        "生": 1,  # 生：加分
        "助": 1,  # 比和助：加分
        "克": -1,  # 克：减分
        "耗": -1,  # 泄耗：减分,
        "泄": -1,
    }

    relations = {
        "木": {"生": "水", "助": "木", "克": "金", "耗": "火", "泄": "土"},
        "火": {"生": "木", "助": "火", "克": "水", "耗": "土", "泄": "金"},
        "土": {"生": "火", "助": "土", "克": "木", "耗": "金", "泄": "水"},
        "金": {"生": "土", "助": "金", "克": "火", "耗": "水", "泄": "木"},
        "水": {"生": "金", "助": "水", "克": "土", "耗": "木", "泄": "火"},
    }

    if wuxing2 in relations[wuxing1].values():
        for relation_type, related_wuxing in relations[wuxing1].items():
            if wuxing2 == related_wuxing:
                return RELATIONSHIP_SCORE.get(relation_type, 0)

    return 0


def calculate_score(shishen_data: dict[str, Any]) -> Any:
    """
    基于权重规则，计算日干的身强身弱总得分。
    :param shishen_data: 结构化的十神数据
    :return: 综合得分与评分明细
    """
    weights = {
        "年干": 8,
        "年支": 4,
        "月干": 12,
        "月支": 40,
        "日支": 12,
        "时干": 12,
        "时支": 12,
    }

    score = 0
    details = []
    mingpan = shishen_data["八字命盘"]
    rigan_wuxing = mingpan["日柱"]["天干"]["名称"][1]  # 日主五行

    # 计算年干得分
    year_gan = mingpan["年柱"]["天干"]["名称"]
    year_score = weights["年干"] * calculate_relationship(rigan_wuxing, year_gan[1])
    score += year_score
    details.append(f"年干（{year_gan}）得分：{year_score}")

    year_zhi = mingpan["年柱"]["地支"]["名称"]
    year_score = weights["年支"] * calculate_relationship(rigan_wuxing, year_zhi[1])
    score += year_score
    details.append(f"年支（{year_zhi}）得分：{year_score}")

    # 计算月干得分
    month_gan = mingpan["月柱"]["天干"]["名称"]
    month_score = weights["月干"] * calculate_relationship(rigan_wuxing, month_gan[1])
    score += month_score
    details.append(f"月干（{month_gan}）得分：{month_score}")

    # 计算月支得分

    month_zhi = mingpan["月柱"]["地支"]["名称"]
    main_score = weights["月支"] * calculate_relationship(rigan_wuxing, month_zhi[1])
    score += main_score
    details.append(f"月支（{month_zhi}）得分：{main_score}")

    # 计算日支得分

    day_zhi = mingpan["日柱"]["地支"]["名称"]
    main_score = weights["日支"] * calculate_relationship(rigan_wuxing, day_zhi[1])
    score += main_score
    details.append(f"日支（{day_zhi}）得分：{main_score}")

    # 计算时干得分
    hour_gan = mingpan["时柱"]["天干"]["名称"]
    hour_score = weights["时干"] * calculate_relationship(rigan_wuxing, hour_gan[1])
    score += hour_score
    details.append(f"时干（{hour_gan}）得分：{hour_score}")

    # 计算时支得分
    hour_zhi = mingpan["时柱"]["地支"]["名称"]
    main_score = weights["时支"] * calculate_relationship(rigan_wuxing, hour_zhi[1])
    score += main_score
    details.append(f"时支（{hour_zhi}）得分：{main_score}")

    return score, details


def fast_strength_check(shishen_data: dict[str, Any]) -> dict[str, Any]:
    """
    快速身强判断核心函数
    :param shishen_data: 结构化的十神数据
    :return: 综合得分与结果
    """
    # 校验数据完整性
    # 计算得分
    score, details = calculate_score(shishen_data)

    # 身强身弱判定
    final_result = "身强" if score > 0 else "身弱"

    return {
        "score": score,
        "result": final_result,
        "details": details,
    }


if __name__ == "__main__":
    # 示例输入数据
    sample_data = {
        "八字命盘": {
            "年柱": {
                "天干": {"名称": "丁火", "主星": "正印"},
                "地支": {
                    "名称": "丑土",
                    "藏干": [
                        {"天干": "己土", "副星": "劫财"},
                        {"天干": "癸水", "副星": "正财"},
                        {"天干": "辛金", "副星": "伤官"},
                    ],
                },
                "星运": "养",
                "神煞": ["天乙", "太极", "国印"],
                "纳音": "涧下水",
                "自坐": "墓",
                "空亡": "申酉",
            },
            "月柱": {
                "天干": {"名称": "庚金", "主星": "食神"},
                "地支": {
                    "名称": "戌土",
                    "藏干": [
                        {"天干": "戊土", "副星": "比肩"},
                        {"天干": "辛金", "副星": "伤官"},
                        {"天干": "丁火", "副星": "正印"},
                    ],
                },
                "星运": "墓",
                "神煞": ["太极", "华盖", "寡宿", "披麻"],
                "纳音": "钗钏金",
                "自坐": "衰",
                "空亡": "寅卯",
            },
            "日柱": {
                "天干": {"名称": "戊土", "主星": "日主"},
                "地支": {
                    "名称": "戌土",
                    "藏干": [
                        {"天干": "戊土", "副星": "比肩"},
                        {"天干": "辛金", "副星": "伤官"},
                        {"天干": "丁火", "副星": "正印"},
                    ],
                },
                "星运": "墓",
                "神煞": ["太极", "魁罡", "八专", "寡宿", "十恶大败", "披麻"],
                "纳音": "平地木",
                "自坐": "墓",
                "空亡": "辰巳",
            },
            "时柱": {
                "天干": {"名称": "癸水", "主星": "正财"},
                "地支": {
                    "名称": "亥水",
                    "藏干": [
                        {"天干": "壬水", "副星": "偏财"},
                        {"天干": "甲木", "副星": "七杀"},
                    ],
                },
                "星运": "绝",
                "神煞": ["天乙", "福星", "词馆", "驿马", "天罗", "劫煞", "吊客"],
                "纳音": "大海水",
                "自坐": "帝旺",
                "空亡": "子丑",
            },
        }
    }

    # 运行程序
    # result = fast_strength_check(sample_data)
    #
    # print("=== 快速身强判断结果 ===")
    # print(f"日元: {sample_data['八字命盘']['日柱']['天干']['名称']}")
    # print(f"综合得分：{result['score']}")
    # print(f"强弱判定：{result['result']}")
    # print("\n评分明细：")
    # for detail in result["details"]:
    #     print(f"- {detail}")
