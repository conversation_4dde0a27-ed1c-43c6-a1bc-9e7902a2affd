# 天干相冲影响解释
from typing import List, Any

tiangan_chong_effects = {
    ("年柱", "月柱"): [
        "《三命通会·卷五》云：岁干克战月干，祖业飘零之兆",
        "主少年离祖，家业变动",
        "父母与祖辈关系紧张",
        "月令为父母宫，被年干冲则不利荫庇",
    ],
    ("年柱", "日柱"): [
        "《渊海子平·论冲击》：日犯岁君，灾殃必重",
        "日主根基动摇，健康易损",
        "不利父亲（年干为父星被冲）",
        "古法视为以下犯上，流年引动易见官非",
    ],
    ("年柱", "时柱"): [
        "《三命通会·卷三》：年时干头两相冲，首尾不能顾，祖业凋零子息空",
        "年柱为祖业根基，时柱为子息归宿，冲则祖荫难庇子孙",
        "晚年易因子嗣破财",
        "父子缘薄，子女叛逆不服管教",
    ],
    ("月柱", "日柱"): [
        "《子平真诠·论十干》：月令司权逢日伐，如臣欺君，福禄有损",
        "月干为父母兄弟宫，日干为己身，冲则六亲失和",
        "婚姻易受长辈干涉",
        "月令格局被破，事业起伏不定",
    ],
    ("月柱", "时柱"): [
        "《子平真诠·论宫位》：门户相冲，晚景难安",
        "子女宫受损，子息缘薄",
        "事业成果易被消耗",
        "中晚年易有突发变故",
    ],
    ("日柱", "时柱"): [
        "《神峰通考·论冲战》：身宫冲子息，老来无靠；妻宫冲门户，家宅不宁",
        "日干为自身根基，时干为晚年归宿，冲则晚景孤寒",
        "子女宫受损，易有流产堕胎",
        "日主五行被冲，对应器官易患慢性病",
    ],
}

# 地支相冲影响解释
dizhi_chong_effects = {
    ("年柱", "月柱"): [
        "《三命通会·论地支冲合》：年月支冲，离祖成家",
        "祖宅风水不利",
        "16-30岁运势动荡，背井离乡",
        "母亲健康需注意",
    ],
    ("年柱", "日柱"): [
        "《滴天髓·征战章》：日犯岁破，根基不稳",
        "婚姻宫受冲，晚婚为宜",
        "祖业难承",
        "日主根基受损，体质偏弱",
    ],
    ("年柱", "时柱"): [
        "《渊海子平·论时柱》：首尾相冲，福禄难全",
        "子女宫逢驿马冲，后代多奔波",
        "晚年易有突发疾病",
        "祖坟风水不利",
    ],
    ("月柱", "日柱"): [
        "《子平真诠·论格局成败》：月令提纲逢日冲，破格之兆",
        "月令格局被破，事业多阻",
        "夫妻宫受冲，婚姻危机暗藏",
        "肝胆系统易疾",
    ],
    ("月柱", "时柱"): [
        "《三命通会·论门户》：月时双冲，门户破败",
        "子女与父母缘薄",
        "晚年财库受损",
        "水火交战，心血系统需注意",
    ],
    ("日柱", "时柱"): [
        "《滴天髓·子女章》：时日相冲，老无所依",
        "婚姻宫与子女宫交战，易有堕胎流产",
        "50岁后健康衰退加速",
        "水火冲激，心肾不交之疾",
    ],
}


def check_chong_combinations(
    tiangan_list: List[str], dizhi_list: List[str]
) -> List[Any]:
    """
    判断天干四冲与地支六冲的组合关系
    :param tiangan_list: 四柱天干列表，顺序为[年干, 月干, 日干, 时干]
    :param dizhi_list: 四柱地支列表，顺序为[年支, 月支, 日支, 时支]
    :return: 结构化组合结果
    """
    # 天干相冲映射表
    tiangan_chong = {
        ("甲", "庚"): "金木相冲",
        ("乙", "辛"): "金木相冲",
        ("丙", "壬"): "水火相冲",
        ("丁", "癸"): "水火相冲",
    }

    # 地支相冲映射表
    dizhi_chong = {
        ("子", "午"): "水火相冲",
        ("丑", "未"): "土土相冲",
        ("寅", "申"): "木金相冲",
        ("卯", "酉"): "木金相冲",
        ("辰", "戌"): "土土相冲",
        ("巳", "亥"): "火水相冲",
    }

    position_names = ["年柱", "月柱", "日柱", "时柱"]
    results = []

    # 检查天干相冲
    for i in range(4):
        for j in range(i + 1, 4):
            gan1, gan2 = tiangan_list[i], tiangan_list[j]
            forward_pair = (gan1, gan2)
            reverse_pair = (gan2, gan1)

            if forward_pair in tiangan_chong or reverse_pair in tiangan_chong:
                chong_type = tiangan_chong.get(forward_pair) or tiangan_chong.get(
                    reverse_pair
                )
                results.append(
                    {
                        "类型": "天干相冲",
                        "相冲天干": [gan1, gan2],
                        "相冲四柱": [position_names[i], position_names[j]],
                        "相冲五行": chong_type,
                        "desc": tiangan_chong_effects.get(
                            (position_names[i], position_names[j])
                        )
                        or tiangan_chong_effects.get(
                            (position_names[j], position_names[i])
                        ),
                    }
                )

    # 检查地支相冲
    for i in range(4):
        for j in range(i + 1, 4):
            zhi1, zhi2 = dizhi_list[i], dizhi_list[j]
            forward_pair = (zhi1, zhi2)
            reverse_pair = (zhi2, zhi1)

            if forward_pair in dizhi_chong or reverse_pair in dizhi_chong:
                chong_type = dizhi_chong.get(forward_pair) or dizhi_chong.get(
                    reverse_pair
                )
                results.append(
                    {
                        "类型": "地支相冲",
                        "相冲地支": [zhi1, zhi2],
                        "相冲四柱": [position_names[i], position_names[j]],
                        "相冲五形": chong_type,
                        "desc": dizhi_chong_effects.get(
                            (position_names[i], position_names[j])
                        )
                        or dizhi_chong_effects.get(
                            (position_names[j], position_names[i])
                        ),
                    }
                )

    return results


# 示例使用
if __name__ == "__main__":
    # 示例八字：甲子 庚午 壬申 丁亥
    sample_tiangan = ["甲", "庚", "壬", "丁"]
    sample_dizhi = ["子", "午", "申", "亥"]

    combinations = check_chong_combinations(sample_tiangan, sample_dizhi)

    import json

    print(json.dumps(combinations, ensure_ascii=False, indent=2))
