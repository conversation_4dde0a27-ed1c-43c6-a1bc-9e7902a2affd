# 添加影响解释的映射字典
from typing import List, Any

tiangan_he_effects = {
    ("年柱", "日柱"): [
        "《三命通会》称“年日干合，主得祖业或配偶助力”",
        "命主性格沉稳，易继承家业或得配偶家族资源",
        "年柱为祖辈、父母宫，日柱为自身及配偶宫，合局主家庭与婚姻紧密关联，可能早婚或配偶与父母关系融洽",
    ],
    ("年柱", "月柱"): [
        "《三命通会》云：“年月干合，祖业根基深厚，父母贤能。”",
        "主家族资源丰厚，父母对命主助力大；",
    ],
    ("年柱", "时柱"): [
        "《子平真诠》载：“年时干合，晚岁荣昌，子孙显达。”",
        "主晚年得子女奉养，或投资获利；",
    ],
    ("月柱", "日柱"): [
        "《三命通会》称：“月日干合，中年得贵，夫妻同心。”",
        "主事业得配偶助力，中年崛起",
    ],
    ("月柱", "时柱"): [
        "中年得势，老来名成",
        "若化真，主中年后智慧提升，适合从事文化、教育行业",
        "若不合化，主事业多变动",
    ],
    ("日柱", "时柱"): [
        "《滴天髓》云：“日时干合，归息成局，晚年福厚。”",
        "主子女成才，老来衣食无忧；",
    ],
}

dizhi_he_effects = {
    ("年柱", "日柱"): [
        "根基深厚，家宅安宁",
        "命主适应力强",
        "配偶可能性格隐忍，婚姻关系稳定但缺乏激情",
    ],
    ("年柱", "月柱"): [
        "传统依据：《渊海子平》曰：“年月支合，少运安稳，家宅兴旺。”",
        "若合化为喜用神（如寅亥合木，木为用），主早年学业顺遂，家庭和睦；",
        "若合化为忌神（如子丑合土，土重克水），则体质孱弱，易患湿寒之疾。",
    ],
    ("年柱", "时柱"): [
        "传统依据：《神峰通考》云：“年时支合，祖荫延及子孙，家业不衰。”",
        "主家传技艺得以继承，子女聪慧；",
    ],
    ("月柱", "日柱"): [
        "《渊海子平》曰：“月日支合，中年家成业就，心性通达。”",
        "主中年财运亨通，人脉广阔；",
    ],
    ("月柱", "时柱"): ["中年后运势渐佳", "晚年积蓄丰厚", "需注意保养身体"],
    ("日柱", "时柱"): [
        "《子平真诠》载：“日时支合，家宅安宁，夫妻白头。”",
        "主晚年投资获利，夫妻感情深厚；",
    ],
}


def check_he_combinations(tiangan_list: List[str], dizhi_list: List[str]) -> List[Any]:
    """
    判断天干五合与地支六合的组合关系
    :param tiangan_list: 四柱天干列表，顺序为[年干, 月干, 日干, 时干]
    :param dizhi_list: 四柱地支列表，顺序为[年支, 月支, 日支, 时支]
    :return: 结构化组合结果
    """
    # 天干五合映射表（有序字典保持顺序）
    tiangan_he = {
        ("甲", "己"): "土",
        ("乙", "庚"): "金",
        ("丙", "辛"): "水",
        ("丁", "壬"): "木",
        ("戊", "癸"): "火",
    }

    # 地支六合映射表
    dizhi_he = {
        ("子", "丑"): "土",
        ("寅", "亥"): "木",
        ("卯", "戌"): "火",
        ("辰", "酉"): "金",
        ("巳", "申"): "水",
        ("午", "未"): "土",
    }

    position_names = ["年柱", "月柱", "日柱", "时柱"]
    results = []

    # 检查天干五合（遍历所有两柱组合）
    for i in range(4):
        for j in range(i + 1, 4):
            gan1, gan2 = tiangan_list[i], tiangan_list[j]
            forward_pair = (gan1, gan2)
            reverse_pair = (gan2, gan1)

            if forward_pair in tiangan_he:
                he_element = tiangan_he[forward_pair]
            elif reverse_pair in tiangan_he:
                he_element = tiangan_he[reverse_pair]
            else:
                continue

            results.append(
                {
                    "类型": "天干相合",
                    "相合天干": [gan1, gan2],
                    "相合四柱": [position_names[i], position_names[j]],
                    "合化生成": he_element,
                    "desc": (
                        tiangan_he_effects[(position_names[i], position_names[j])]
                        if (position_names[i], position_names[j]) in tiangan_he_effects
                        else tiangan_he_effects[(position_names[j], position_names[i])]
                    ),
                }
            )

    # 检查地支六合（遍历所有两柱组合）
    for i in range(4):
        for j in range(i + 1, 4):
            zhi1, zhi2 = dizhi_list[i], dizhi_list[j]
            forward_pair = (zhi1, zhi2)
            reverse_pair = (zhi2, zhi1)

            if forward_pair in dizhi_he:
                he_element = dizhi_he[forward_pair]
            elif reverse_pair in dizhi_he:
                he_element = dizhi_he[reverse_pair]
            else:
                continue

            results.append(
                {
                    "类型": "地支相合",
                    "相合地支": [zhi1, zhi2],
                    "相合四柱": [position_names[i], position_names[j]],
                    "合化生成": he_element,
                    "desc": (
                        dizhi_he_effects[(position_names[i], position_names[j])]
                        if (position_names[i], position_names[j]) in dizhi_he_effects
                        else dizhi_he_effects[(position_names[j], position_names[i])]
                    ),
                }
            )

    return results


# 示例使用
if __name__ == "__main__":
    # 示例八字：甲子 己巳 壬戌 乙巳
    sample_tiangan = ["甲", "己", "壬", "乙"]
    sample_dizhi = ["子", "丑", "戌", "巳"]

    combinations = check_he_combinations(sample_tiangan, sample_dizhi)

    import json

    print(json.dumps(combinations, ensure_ascii=False, indent=2))
