from typing import List, Any

JIAZI = [
    "　",
    "甲子",
    "乙丑",
    "丙寅",
    "丁卯",
    "戊辰",
    "己巳",
    "庚午",
    "辛未",
    "壬申",
    "癸酉",
    "甲戌",
    "乙亥",
    "丙子",
    "丁丑",
    "戊寅",
    "己卯",
    "庚辰",
    "辛巳",
    "壬午",
    "癸未",
    "甲申",
    "乙酉",
    "丙戌",
    "丁亥",
    "戊子",
    "己丑",
    "庚寅",
    "辛卯",
    "壬辰",
    "癸巳",
    "甲午",
    "乙未",
    "丙申",
    "丁酉",
    "戊戌",
    "己亥",
    "庚子",
    "辛丑",
    "壬寅",
    "癸卯",
    "甲辰",
    "乙巳",
    "丙午",
    "丁未",
    "戊申",
    "己酉",
    "庚戌",
    "辛亥",
    "壬子",
    "癸丑",
    "甲寅",
    "乙卯",
    "丙辰",
    "丁巳",
    "戊午",
    "己未",
    "庚申",
    "辛酉",
    "壬戌",
    "癸亥",
]


def tiangan_yinyang(tiangan: str) -> bool:
    """
    判断天干的阴阳属性
    Args:
        tiangan: 天干
    Returns:
        bool: True为阳，False为阴
    """
    yang_list = ["甲", "丙", "戊", "庚", "壬"]
    return tiangan in yang_list


def query_shen_sha(
    ganzhi: List[str], bazi: List[str], is_man: bool, witch: int, nian_nayin: str
) -> List[Any]:
    """
    根据干支和八字信息，查询神煞组合。
    Args:
        ganzhi: 干支(柱)
        bazi: 八字数组，数组元素0-7，分别是年柱、月柱、日柱、时柱的干支
        is_man: 性别
        witch: 哪一柱，1，2，3，4分别代表年/月/日/时柱。其它分别代表大运，流年，流月，流时。
        nian_nayin: 年柱纳音
    Returns:
        返回神煞数组
    """
    sheng_sha_list = []
    nian_gan = bazi[0]
    nian_zhi = bazi[1]
    # yue_gan = bazi[2]
    yue_zhi = bazi[3]
    ri_gan = bazi[4]
    ri_zhi = bazi[5]
    shi_gan = bazi[6]
    shi_zhi = bazi[7]
    gan = ganzhi[0]
    zhi = ganzhi[1]

    if tianyiguiren(ri_gan, zhi) == 1 or tianyiguiren(nian_gan, zhi) == 1:
        sheng_sha_list.append("天乙")

    if taijiguiren(ri_gan, zhi) == 1 or taijiguiren(nian_gan, zhi) == 1:
        sheng_sha_list.append("太极")

    if tiandeguiren(yue_zhi, gan) == 1 or tiandeguiren(yue_zhi, zhi) == 1:
        sheng_sha_list.append("天德")

    if yuede(yue_zhi, gan) == 1:
        sheng_sha_list.append("月德")

    if tiandehe(yue_zhi, gan) == 1 or tiandehe(yue_zhi, zhi) == 1:
        sheng_sha_list.append("天德合")

    if yuedehe(yue_zhi, gan) == 1:
        sheng_sha_list.append("月德合")

    if fuxing(nian_gan, zhi) == 1 or fuxing(ri_gan, zhi) == 1:
        sheng_sha_list.append("福星")

    if wenchang(ri_gan, zhi) == 1 or wenchang(nian_gan, zhi) == 1:
        sheng_sha_list.append("文昌")

    if witch != 3 and xuetang(nian_nayin, gan, zhi) == 1:
        sheng_sha_list.append("学堂")

    if witch != 3 and ciguan(nian_nayin, gan, zhi) == 1:
        sheng_sha_list.append("词馆")

    if witch == 3 and kuigang(ri_gan, ri_zhi) == 1:
        sheng_sha_list.append("魁罡")

    if guoying(ri_gan, zhi) == 1 or guoying(nian_gan, zhi) == 1:
        sheng_sha_list.append("国印")

    if (witch != 3 and yima(ri_zhi, zhi) == 1) or (
        witch != 1 and yima(nian_zhi, zhi) == 1
    ):
        sheng_sha_list.append("驿马")

    if (witch != 3 and huagai(ri_zhi, zhi) == 1) or (
        witch != 1 and huagai(nian_zhi, zhi) == 1
    ):
        sheng_sha_list.append("华盖")

    if (witch != 3 and jiangxing(ri_zhi, zhi) == 1) or (
        witch != 1 and jiangxing(nian_zhi, zhi) == 1
    ):
        sheng_sha_list.append("将星")

    if jingyu(ri_gan, zhi) == 1 or jingyu(nian_gan, zhi) == 1:
        sheng_sha_list.append("金舆")

    if (witch == 3 and jinshen(ri_gan, ri_zhi) == 1) or (
        witch == 4 and jinshen(shi_gan, shi_zhi) == 1
    ):
        sheng_sha_list.append("金神")

    if witch != 2 and wugui(yue_zhi, zhi):
        sheng_sha_list.append("五鬼")

    if witch != 2 and tianyi(yue_zhi, zhi) == 1:
        sheng_sha_list.append("天医")

    if lushen(ri_gan, zhi) == 1:
        sheng_sha_list.append("禄神")

    if tianshe(yue_zhi, ri_gan, ri_zhi) == 1:
        sheng_sha_list.append("天赦")

    if witch != 1 and hongluan(nian_zhi, zhi) == 1:
        sheng_sha_list.append("红鸾")

    if witch != 1 and tianxi(nian_zhi, zhi) == 1:
        sheng_sha_list.append("天喜")

    if liuxia(ri_gan, zhi) == 1:
        sheng_sha_list.append("流霞")

    if hongyan(ri_gan, zhi) == 1:
        sheng_sha_list.append("红艳")

    if (witch != 3 and tianluo(ri_zhi, zhi) == 1) or (
        witch != 1 and tianluo(nian_zhi, zhi) == 1
    ):
        sheng_sha_list.append("天罗")

    if (witch != 3 and diwang(ri_zhi, zhi) == 1) or (
        witch != 1 and diwang(nian_zhi, zhi) == 1
    ):
        sheng_sha_list.append("地网")

    if yangren(ri_gan, zhi) == 1:
        sheng_sha_list.append("羊刃")

    if feiren(ri_gan, zhi) == 1:
        sheng_sha_list.append("飞刃")

    if xueren(yue_zhi, zhi) == 1:
        sheng_sha_list.append("血刃")

    if witch == 3 and bazhuan(ri_gan, ri_zhi) == 1:
        sheng_sha_list.append("八专")

    if witch == 3 and jiuchou(ri_gan, ri_zhi) == 1:
        sheng_sha_list.append("九丑")

    if jiesha(ri_zhi, zhi) == 1 or jiesha(nian_zhi, zhi) == 1:
        sheng_sha_list.append("劫煞")

    if zaisha(nian_zhi, zhi) == 1:
        sheng_sha_list.append("灾煞")

    if witch != 1 and yuancheng(nian_zhi, zhi, is_man, tiangan_yinyang(nian_gan)) == 1:
        sheng_sha_list.append("元辰")

    if (witch != 3 and kongwang(ri_gan + ri_zhi, zhi) == 1) or (
        witch != 1 and kongwang(nian_gan + nian_zhi, zhi) == 1
    ):
        sheng_sha_list.append("空亡")

    if (witch == 3 and tongzi(yue_zhi, nian_nayin, ri_zhi)) or (
        witch == 4 and tongzi(yue_zhi, nian_nayin, shi_zhi)
    ):
        sheng_sha_list.append("童子")

    if witch != 1 and gucheng(nian_zhi, zhi) == 1:
        sheng_sha_list.append("孤辰")

    if witch != 1 and guashu(nian_zhi, zhi) == 1:
        sheng_sha_list.append("寡宿")

    if (witch != 3 and wangshen(ri_zhi, zhi) == 1) or (
        witch != 1 and wangshen(nian_zhi, zhi) == 1
    ):
        sheng_sha_list.append("亡神")

    if witch == 3 and shiedabai(ri_gan, ri_zhi) == 1:
        sheng_sha_list.append("十恶大败")

    if taohua(ri_zhi, zhi) == 1 or taohua(nian_zhi, zhi) == 1:
        sheng_sha_list.append("桃花")

    if witch == 3 and guluan(ri_gan, ri_zhi) == 1:
        sheng_sha_list.append("孤鸾")

    if witch == 3 and yingyangchacuo(ri_gan, ri_zhi) == 1:
        sheng_sha_list.append("阴差阳错")

    if witch == 3 and sifei(yue_zhi, ri_gan, ri_zhi) == 1:
        sheng_sha_list.append("四废")

    if witch != 1 and shangmen(nian_zhi, zhi) == 1:
        sheng_sha_list.append("丧门")

    if witch != 1 and diaoke(nian_zhi, zhi) == 1:
        sheng_sha_list.append("吊客")

    if witch != 1 and pima(nian_zhi, zhi) == 1:
        sheng_sha_list.append("披麻")

    if witch == 3 and shiling(ri_gan, ri_zhi) == 1:
        sheng_sha_list.append("十灵")

    return sheng_sha_list


def kongwang(ganzhi: str, dizhi: str) -> int:
    """
    甲子旬在戌亥。甲戌旬在申酉。甲申旬在午未。
    甲午旬在辰巳。甲辰旬在寅卯。甲寅旬在子丑。
    查法：以日/年柱，见余三地支
    查法: 以日柱为主, 柱中年、 月、 时支见者为空亡.
    Args:
        ganzhi: 干支
        dizhi: 地支
    """
    idx = get_jiazi_order(ganzhi)
    if (
        (idx <= 10 and dizhi in ("戌", "亥"))
        or (idx > 10 and idx <= 20 and dizhi in ("申", "酉"))
        or (idx > 20 and idx <= 30 and dizhi in ("午", "未"))
        or (idx > 30 and idx <= 40 and dizhi in ("辰", "巳"))
        or (idx > 40 and idx <= 50 and dizhi in ("寅", "卯"))
        or (idx > 50 and dizhi in ("子", "丑"))
    ):
        return 1
    return 0


def taohua(param_string1: str, param_string2: str) -> int:
    """
    桃花:
    申子辰在酉, 寅午戌在卯,
    巳酉丑在午, 亥卯未在子.
    查法: 以年支或日支查四柱其它地支.
    """
    conditions = {
        "申": "酉",
        "子": "酉",
        "辰": "酉",
        "寅": "卯",
        "午": "卯",
        "戌": "卯",
        "巳": "午",
        "酉": "午",
        "丑": "午",
        "亥": "子",
        "卯": "子",
        "未": "子",
    }
    return 1 if conditions.get(param_string1) == param_string2 else 0


def yingyangchacuo(param_string1: str, param_string2: str) -> int:
    """
    阴阳差错:
    丙子, 丁丑, 戊寅, 辛卯, 壬辰, 癸巳,
    丙午, 丁未, 戊申, 辛酉, 壬戌, 癸亥.
    查法: 日柱见者为是.
    """
    conditions = {
        "丙": ["子", "午"],
        "丁": ["丑", "未"],
        "戊": ["寅", "申"],
        "辛": ["卯", "酉"],
        "壬": ["辰", "戌"],
        "癸": ["巳", "亥"],
    }
    return (
        1
        if conditions.get(param_string1) and param_string2 in conditions[param_string1]
        else 0
    )


def tianyiguiren(param_string1: str, param_string2: str) -> int:
    """
    天乙贵人 甲戊并牛羊, 乙己鼠猴乡, 丙丁猪鸡位, 壬癸兔蛇藏, 庚辛逢虎马, 此是贵人方.
    查法: 以日干年干起贵人, 地支见者为是
    """
    conditions = {
        "甲": ["丑", "未"],
        "戊": ["丑", "未"],
        "乙": ["申", "子"],
        "己": ["申", "子"],
        "丙": ["亥", "酉"],
        "丁": ["亥", "酉"],
        "壬": ["卯", "巳"],
        "癸": ["卯", "巳"],
        "庚": ["午", "寅"],
        "辛": ["午", "寅"],
    }
    return (
        1
        if conditions.get(param_string1) and param_string2 in conditions[param_string1]
        else 0
    )


def taijiguiren(param_string1: str, param_string2: str) -> int:
    """
    太极贵人:
    甲乙生人子午中,丙丁鸡兔定亨通,
    戊己两干临四季,庚辛寅亥禄丰隆,
    壬癸巳申偏喜美,值此应当福气钟,
    更须贵格来相扶,候封万户到三公.
    Args:
        param_string1: 日干
        param_string2: 地支
    """
    conditions = {
        "甲": ["子", "午"],
        "乙": ["子", "午"],
        "丙": ["酉", "卯"],
        "丁": ["酉", "卯"],
        "庚": ["寅", "亥"],
        "辛": ["寅", "亥"],
        "壬": ["申", "巳"],
        "癸": ["申", "巳"],
        "戊": "土",
        "己": "土",
    }

    if param_string1 in ["戊", "己"]:
        return 1 if param_string2 in ["辰", "戌", "丑", "未"] else 0
    return (
        1
        if conditions.get(param_string1) and param_string2 in conditions[param_string1]
        else 0
    )


def wenchang(param_string1: str, param_string2: str) -> int:
    """
    文昌贵人:
    甲乙巳午报君知, 丙戊申宫丁己鸡.
    庚猪辛鼠壬逢虎, 癸人见卯入云梯.
    查法: 以年干或日干为主, 凡四柱中地支所见者为是
    """
    conditions = {
        "甲": "巳",
        "乙": "午",
        "丙": "申",
        "丁": "酉",
        "戊": "申",
        "己": "酉",
        "庚": "亥",
        "辛": "子",
        "壬": "寅",
        "癸": "卯",
    }
    return 1 if conditions.get(param_string1) == param_string2 else 0


def kuigang(param_string1: str, param_string2: str) -> int:
    """
    魁罡贵人:
    壬辰庚戌与庚辰, 戊戌魁罡四座神,
    不见财官刑煞并,身行旺地贵无伦.
    查法: 日柱见者为是
    """
    conditions = {"壬": "辰", "庚": ["戌", "辰"], "戊": "戌"}

    if isinstance(conditions.get(param_string1), list):
        return 1 if param_string2 in conditions[param_string1] else 0

    return 1 if conditions.get(param_string1) == param_string2 else 0


def yima(param_string1: str, param_string2: str) -> int:
    """
    驿马:
    申子辰马在寅, 寅午戌马在申,
    巳酉丑马在亥, 亥卯未马在巳.
    查法：以年、日支查余三支
    """
    conditions = {
        "申": "寅",
        "子": "寅",
        "辰": "寅",
        "寅": "申",
        "午": "申",
        "戌": "申",
        "亥": "巳",
        "卯": "巳",
        "未": "巳",
        "巳": "亥",
        "酉": "亥",
        "丑": "亥",
    }
    return 1 if conditions.get(param_string1) == param_string2 else 0


def huagai(param_string1: str, param_string2: str) -> int:
    """
    华盖:
    寅午戌见戌, 亥卯未见未,
    申子辰见辰, 巳酉丑见丑.
    查法： 以年支或日支为主, 凡四柱中所见者为有华盖星.
    """
    conditions = {
        "申": "辰",
        "子": "辰",
        "辰": "辰",
        "寅": "戌",
        "午": "戌",
        "戌": "戌",
        "巳": "丑",
        "酉": "丑",
        "丑": "丑",
        "亥": "未",
        "卯": "未",
        "未": "未",
    }
    return 1 if conditions.get(param_string1) == param_string2 else 0


def jingyu(param_string1: str, param_string2: str) -> int:
    """
    金舆:
    甲龙乙蛇丙戊羊, 丁己猴歌庚犬方,
    辛猪壬牛癸逢虎, 凡人遇此福气昌.
    查法：以日/年干查四地支
    """
    conditions = {
        "甲": "辰",
        "乙": "巳",
        "丁": "申",
        "己": "申",
        "丙": "未",
        "戊": "未",
        "庚": "戌",
        "辛": "亥",
        "壬": "丑",
        "癸": "寅",
    }
    return 1 if conditions.get(param_string1) == param_string2 else 0


def jinshen(param_string1: str, param_string2: str) -> int:
    """
    金神者，乙丑，己巳，癸酉三组干支。
    查法：日柱或时柱见者为是。
    Args:
        param_string1: 天干
        param_string2: 地支
    """
    combinations = {"乙丑", "己巳", "癸酉"}
    return 1 if param_string1 + param_string2 in combinations else 0


def wugui(param_string1: str, param_string2: str) -> int:
    """
    五鬼星的查法如下：
    子月支见辰支，丑月支见巳支，
    寅月支见午支，卯月支见未支，
    辰月支见申支，巳月支见酉支，
    午月支见戌支，未月支见亥支，
    申月支见子支，酉月支见丑支，
    戌月支见寅支，亥月支见卯支。
    即是命犯五鬼星。
    """
    conditions = {
        "子": "辰",
        "丑": "巳",
        "寅": "午",
        "卯": "未",
        "辰": "申",
        "巳": "酉",
        "午": "戌",
        "未": "亥",
        "申": "子",
        "酉": "丑",
        "戌": "寅",
        "亥": "卯",
    }
    return 1 if conditions.get(param_string1) == param_string2 else 0


def get_jiazi_order(yearganzhi: str) -> int:
    """
    算年柱在60甲子顺序，注意顺序1-60
    """
    return JIAZI.index(yearganzhi)


def dizhi_wuxing(dizhi: str) -> str:
    """
    查询地支五行。
    """
    wuxing_mapping = {
        "寅": "木",
        "卯": "木",
        "巳": "火",
        "午": "火",
        "丑": "土",
        "辰": "土",
        "未": "土",
        "戌": "土",
        "申": "金",
        "酉": "金",
        "亥": "水",
        "子": "水",
    }
    return wuxing_mapping.get(dizhi, "")


def tianluo(param_string1: str, param_string2: str) -> int:
    """
    天罗:
    查法一：以年支/日支查余三支
    戌亥为天罗，辰巳为地网；
    戌见亥, 亥见戌为天罗；辰见巳, 巳见辰为地网。
    查法二：以年纳音查日支
    火命人逢戌亥为天罗, 水土命逢辰巳为地网.
    采用查法一。
    """
    return (
        1
        if (
            (param_string1 == "戌" and param_string2 == "亥")
            or (param_string1 == "亥" and param_string2 == "戌")
        )
        else 0
    )


def diwang(param_string1: str, param_string2: str) -> int:
    """
    地网:
    查法一：以年支/日支查余三支
    戌亥为天罗，辰巳为地网；
    戌见亥, 亥见戌为天罗；辰见巳, 巳见辰为地网。
    查法二：以年纳音查日支
    火命人逢戌亥为天罗, 水土命逢辰巳为地网.
    """
    return (
        1
        if (
            (param_string1 == "辰" and param_string2 == "巳")
            or (param_string1 == "巳" and param_string2 == "辰")
        )
        else 0
    )


def yangren(param_string1: str, param_string2: str) -> int:
    """
    羊刃:
    甲羊刃在卯, 乙羊刃在寅,
    丙戊羊刃在午, 丁己羊刃在巳,
    庚羊刃在酉, 辛羊刃在申,
    壬羊刃在子, 癸羊刃在亥.
    查法: 以日干为主, 四支见之者为是.
    Args:
        param_string1: 日干
        param_string2: 地支
    """
    match_pairs = {
        "甲": "卯",
        "乙": "寅",
        "丙": "午",
        "丁": "巳",
        "戊": "午",
        "己": "巳",
        "庚": "酉",
        "辛": "申",
        "壬": "子",
        "癸": "亥",
    }
    return 1 if match_pairs.get(param_string1) == param_string2 else 0


def feiren(param_string1: str, param_string2: str) -> int:
    """
    飞刃:
    甲羊刃在卯，如果地支见酉，即为飞刃；
    乙刃在寅，寅申相冲，即为飞刃；
    丙戊羊刃在午，地支见子，即为飞刃；
    丁己羊刃在未，地支见丑，即为飞刃；
    庚羊刃在酉，地支见卯，即为飞刃；
    辛羊刃在戌， 地支见辰，即为羊刃；
    壬羊刃在子，地支见午，即为飞刃；
    癸羊刃在丑，地支见未，即为飞刃。
    查法: 以日干查四地支，羊刃的六冲
    Args:
        param_string1: 日干
        param_string2: 地支
    """
    match_pairs = {
        "甲": "酉",
        "乙": "申",
        "丙": "子",
        "戊": "子",
        "丁": "丑",
        "己": "丑",
        "庚": "卯",
        "辛": "辰",
        "壬": "午",
        "癸": "未",
    }
    return 1 if match_pairs.get(param_string1) == param_string2 else 0


def jiesha(param_string1: str, param_string2: str) -> int:
    """
    劫煞:
    申子辰见巳, 亥卯未见申,
    寅午戌见亥, 巳酉丑见寅.
    查法: 以年柱或日柱为主, 四柱地支见之者为是.
    Args:
        param_string1: 年支/日支
        param_string2: 地支
    """
    match_groups = {
        "亥": ["寅", "午", "戌"],
        "巳": ["申", "子", "辰"],
        "寅": ["巳", "酉", "丑"],
        "申": ["亥", "卯", "未"],
    }
    return (
        1
        if match_groups.get(param_string2)
        and param_string1 in match_groups[param_string2]
        else 0
    )


def zaisha(param_string1: str, param_string2: str) -> int:
    """
    灾煞:
    申子辰见午, 亥卯未见酉,
    寅午戌见子, 巳酉丑见卯.
    查法: 以年支为主, 四柱地支中见之者为是.
    Args:
        param_string1: 年支
        param_string2: 地支
    """
    match_groups = {
        "午": ["申", "子", "辰"],
        "子": ["寅", "午", "戌"],
        "卯": ["巳", "酉", "丑"],
        "酉": ["亥", "卯", "未"],
    }
    return (
        1
        if match_groups.get(param_string2)
        and param_string1 in match_groups[param_string2]
        else 0
    )


def tongzi(param_string1: str, param_string2: str, param_string3: str) -> int:
    """
    春秋寅子贵，冬夏卯未辰；
    金木马卯合，水火鸡犬多；
    土命逢辰巳，童子定不错。
    查法：
    1、命造生在春季或秋季的（以月令算），日支或时支见寅或子的。
    2、命造生在冬季或夏季的（以月令算），日支或时支见卯、未或辰的。
    3、年柱纳音为金或木的，日支或时支见午或卯的。
    4、年柱纳音为水或火的，日支或时支见酉或戌的。
    5、年柱纳音为土命的，日支或时支见辰或巳的。
    Args:
        param_string1: 月支
        param_string2: 年柱纳音
        param_string3: 日支或时支
    """
    first_conditions = {
        "寅": ["寅", "子"],
        "卯": ["寅", "子"],
        "辰": ["寅", "子"],
        "申": ["寅", "子"],
        "酉": ["寅", "子"],
        "戌": ["寅", "子"],
        "巳": ["卯", "未", "辰"],
        "午": ["卯", "未", "辰"],
        "未": ["卯", "未", "辰"],
        "亥": ["卯", "未", "辰"],
        "子": ["卯", "未", "辰"],
        "丑": ["卯", "未", "辰"],
    }

    if param_string3 in first_conditions.get(param_string1, []):
        return 1

    if param_string2 in ["金", "木"] and param_string3 in ["午", "卯"]:
        return 1

    if param_string2 in ["水", "火"] and param_string3 in ["酉", "戌"]:
        return 1

    if param_string2 == "土" and param_string3 in ["辰", "巳"]:
        return 1

    return 0


def gucheng(param_string1: str, param_string2: str) -> int:
    """
    孤辰:
    亥子丑人, 见寅为孤, 见戌为寡.
    寅卯辰人, 见巳为孤, 见丑为寡.
    巳午未人, 见申为孤, 见辰为寡.
    申酉戌人, 见亥为孤, 见未为寡.
    查法: 以年支为准, 四柱其它地支见者为是. 如巳年生人, 见申为孤辰, 见辰为寡宿.
    Args:
        param_string1: 年支
        param_string2: 地支
    """
    match_groups = {
        "寅": ["亥", "子", "丑"],
        "巳": ["寅", "卯", "辰"],
        "申": ["巳", "午", "未"],
        "亥": ["申", "酉", "戌"],
    }
    return (
        1
        if match_groups.get(param_string2)
        and param_string1 in match_groups[param_string2]
        else 0
    )


def guashu(param_string1: str, param_string2: str) -> int:
    """
    寡宿:
    亥子丑人, 见寅为孤, 见戌为寡.
    寅卯辰人, 见巳为孤, 见丑为寡.
    巳午未人, 见申为孤, 见辰为寡.
    申酉戌人, 见亥为孤, 见未为寡.
    查法: 以年支为准, 四柱其它地支见者为是. 如巳年生人, 见申为孤辰, 见辰为寡宿.
    Args:
        param_string1: 年支
        param_string2: 地支
    """
    match_groups = {
        "戌": ["亥", "子", "丑"],
        "丑": ["寅", "卯", "辰"],
        "辰": ["巳", "午", "未"],
        "未": ["申", "酉", "戌"],
    }
    return (
        1
        if match_groups.get(param_string2)
        and param_string1 in match_groups[param_string2]
        else 0
    )


def wangshen(param_string1: str, param_string2: str) -> int:
    """
    亡神:
    寅午戌见巳, 亥卯未见寅,
    巳酉丑见申, 申子辰见亥.
    查法: 以年/日支查余三支.
    Args:
        param_string1: 年支/日支
        param_string2: 地支
    """
    match_groups = {
        "亥": ["申", "子", "辰"],
        "巳": ["寅", "午", "戌"],
        "申": ["巳", "巳", "丑"],
        "寅": ["亥", "卯", "未"],
    }
    return (
        1
        if match_groups.get(param_string2)
        and param_string1 in match_groups[param_string2]
        else 0
    )


def shiedabai(param_string1: str, param_string2: str) -> int:
    """
    十恶大败:
    甲辰乙巳与壬申, 丙申丁亥及庚辰,
    戊戌癸亥加辛巳, 己丑都来十位神.
    查法: 四柱日柱逢之即是.
    Args:
        param_string1: 日干
        param_string2: 日支
    """
    match_pairs = {
        "甲": "辰",
        "乙": "巳",
        "壬": "申",
        "丙": "申",
        "丁": "亥",
        "庚": "辰",  # 修改：庚寅 -> 庚辰
        "戊": "戌",
        "癸": "亥",
        "辛": "巳",
        "己": "丑",
    }
    return 1 if match_pairs.get(param_string1) == param_string2 else 0


def shiling(param_string1: str, param_string2: str) -> int:
    """
    十灵日。
    古诀：
    "男带十灵好文章，女带十灵好衣裳。"
    甲辰、乙亥、丙辰、丁酉、戊午、庚戌、庚寅、辛亥、壬寅、癸未
    查法：查日柱
    Args:
        param_string1: 日干
        param_string2: 日支
    """
    valid_pairs = {
        "甲辰",
        "乙亥",
        "丙辰",
        "丁酉",
        "戊午",
        "庚戌",
        "庚寅",
        "辛亥",
        "壬寅",
        "癸未",
    }
    return 1 if (param_string1 + param_string2) in valid_pairs else 0


_NIANZHI = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]
_SHANGM = ["寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥", "子", "丑"]
_DIAOK = ["戌", "亥", "子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉"]
_PIM = ["酉", "戌", "亥", "子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申"]


def check_relation(param_string1: str, param_string2: str, type_name: str) -> int:
    """
    查法：以年支查余三支
    Args:
        param_string1: 年支
        param_string2: 地支
        type_name: 类型（"shangmen"、"diaoke"、"pima"）
    Returns:
        int: 1 表示符合条件，0 表示不符合
    """
    index = _NIANZHI.index(param_string1) if param_string1 in _NIANZHI else -1
    if index == -1:
        return 0

    target_array = {"shangmen": _SHANGM, "diaoke": _DIAOK, "pima": _PIM}.get(type_name)

    if not target_array:
        return 0

    return 1 if target_array[index] == param_string2 else 0


def shangmen(param_string1: str, param_string2: str) -> Any:
    """丧门"""
    return check_relation(param_string1, param_string2, "shangmen")


def diaoke(param_string1: str, param_string2: str) -> Any:
    """吊客"""
    return check_relation(param_string1, param_string2, "diaoke")


def pima(param_string1: str, param_string2: str) -> Any:
    """披麻"""
    return check_relation(param_string1, param_string2, "pima")


def guoying(param_string1: str, param_string2: str) -> int:
    """
    国印贵人:
    甲见戌, 乙见亥, 丙见丑, 丁见寅, 戊见丑,
    己见寅, 庚见辰, 辛见巳. 壬见未, 癸见申
    查法：以年、日干查四支
    """
    conditions = {
        "甲": "戌",
        "乙": "亥",
        "丙": "丑",
        "丁": "寅",
        "戊": "丑",
        "己": "寅",
        "庚": "辰",
        "辛": "巳",
        "壬": "未",
        "癸": "申",
    }
    return 1 if conditions.get(param_string1) == param_string2 else 0


def jiangxing(param_string1: str, param_string2: str) -> int:
    """
    将星:
    寅午戌见午, 巳酉丑见酉,
    申子辰见子, 辛卯未见卯.
    查法: 以年支或日支查其余各支, 见者为将星.
    """
    conditions = {
        "申": ["子"],
        "子": ["子"],
        "寅": ["午"],
        "午": ["午"],
        "戌": ["午"],
        "巳": ["酉"],
        "酉": ["酉"],
        "丑": ["酉"],
        "亥": ["卯"],
        "卯": ["卯"],
        "未": ["卯"],
    }
    return (
        1
        if conditions.get(param_string1) and param_string2 in conditions[param_string1]
        else 0
    )


def jingshen(param_string1: str, param_string2: str) -> int:
    """
    金神 金神者, 乙丑, 己巳, 癸酉三组干支. 日柱或时柱见者为是.
    """
    valid_pairs = [["乙", "丑"], ["己", "巳"], ["癸", "酉"]]
    return 1 if [param_string1, param_string2] in valid_pairs else 0


def guluan(param_string1: str, param_string2: str) -> int:
    """
    孤鸾煞:
    甲寅日。乙巳日。丙午日。丁巳日。戊午日。戊申日。辛亥日。壬子日。
    查法: 查日柱
    Args:
        param_string1: 日干
        param_string2: 日支
    """
    valid_pairs = [
        ["乙", "巳"],
        ["丁", "巳"],
        ["辛", "亥"],
        ["戊", "申"],
        ["甲", "寅"],
        ["戊", "午"],
        ["丙", "午"],
        ["壬", "子"],
    ]
    return 1 if [param_string1, param_string2] in valid_pairs else 0


def tiandeguiren(param_string1: str, param_string2: str) -> int:
    """
    天德贵人:
    正月生者见丁, 二月生者见申, 三月生者见壬, 四月生者见辛,
    五月生者见亥, 六月生者见甲, 七月生者见癸, 八月生者见寅,
    九月生者见丙, 十月生者见乙, 十一月生者见巳, 十二月生者见庚.
    查法：以月支查四柱干支
    Args:
        param_string1: 月支
        param_string2: 干或支
    """
    conditions = {
        "寅": "丁",
        "卯": "申",
        "辰": "壬",
        "巳": "辛",
        "午": "亥",
        "未": "甲",
        "申": "癸",
        "酉": "寅",
        "戌": "丙",
        "亥": "乙",
        "子": "巳",
        "丑": "庚",
    }
    return 1 if conditions.get(param_string1) == param_string2 else 0


def yuede(param_string1: str, param_string2: str) -> int:
    """
    月德贵人:
    寅午戌月生者见丙, 申子辰月生者见壬,
    亥卯未月生者见甲, 巳酉丑月生者见庚.
    凡柱中年月日时干上见者为有月德贵人.
    查法：以月支查四柱干支
    Args:
        param_string1: 月支
        param_string2: 年月日时干
    """
    conditions = {
        "寅": "丙",
        "午": "丙",
        "戌": "丙",
        "亥": "甲",
        "卯": "甲",
        "未": "甲",
        "申": "壬",
        "子": "壬",
        "辰": "壬",
        "巳": "庚",
        "酉": "庚",
        "丑": "庚",
    }
    return 1 if conditions.get(param_string1) == param_string2 else 0


def tiandehe(param_string1: str, param_string2: str) -> int:
    """
    天德合:
    寅月壬。卯月巳。辰月丁。巳月丙。午月寅。未月己。
    申月戊。酉月亥。戌月辛。亥月庚。子月申。丑月乙。
    查法：以月支查其它干/支
    Args:
        param_string1: 月支
        param_string2: 天干或地支
    """
    conditions = {
        "寅": "壬",
        "卯": "巳",
        "辰": "丁",
        "巳": "丙",
        "午": "寅",
        "未": "己",
        "申": "戊",
        "酉": "亥",
        "戌": "辛",
        "亥": "庚",
        "子": "申",
        "丑": "乙",
    }
    return 1 if conditions.get(param_string1) == param_string2 else 0


def yuedehe(param_string1: str, param_string2: str) -> int:
    """
    月德合:
    寅午戌月见辛，申子辰月见丁，巳酉丑月见乙，亥卯未月见己。
    查法：以月支查天干
    """
    match_groups = {
        "辛": ["寅", "午", "戌"],
        "丁": ["申", "子", "辰"],
        "乙": ["巳", "酉", "丑"],
        "己": ["亥", "卯", "未"],
    }

    return (
        1
        if param_string2 in match_groups
        and param_string1 in match_groups[param_string2]
        else 0
    )


def fuxing(param_string1: str, param_string2: str) -> int:
    """
    福星:
    凡甲、丙两干见寅或子，乙、癸两干见卯或丑，
    戊干见申，己干见未，丁干见亥，庚干见午，
    辛干见巳，壬干见辰是也。
    查法: 以年/日干查地支
    """
    conditions = [
        {"gan": ["甲", "丙"], "zhi": ["寅", "子"]},
        {"gan": ["乙", "癸"], "zhi": ["卯", "丑"]},
        {"gan": ["戊"], "zhi": ["申"]},
        {"gan": ["己"], "zhi": ["未"]},
        {"gan": ["丁"], "zhi": ["亥"]},
        {"gan": ["庚"], "zhi": ["午"]},
        {"gan": ["辛"], "zhi": ["巳"]},
        {"gan": ["壬"], "zhi": ["辰"]},
    ]

    for condition in conditions:
        if param_string1 in condition["gan"] and param_string2 in condition["zhi"]:
            return 1
    return 0


def tianyi(param_string1: str, param_string2: str) -> int:
    """
    天医:
    正月生见丑, 二月生见寅, 三月生见卯, 四月生见辰,
    五月生见巳, 六月生见午, 七月生见未, 八月生见申,
    九月生见酉, 十月生见戌, 十一月生见亥, 十二月生见子.
    查法: 以月支查其它地支, 见者为是.
    """
    conditions = {
        "寅": "丑",
        "卯": "寅",
        "辰": "卯",
        "巳": "辰",
        "午": "巳",
        "未": "午",
        "申": "未",
        "酉": "申",
        "戌": "酉",
        "亥": "戌",
        "子": "亥",
        "丑": "子",
    }
    return 1 if conditions.get(param_string1) == param_string2 else 0


def lushen(param_string1: str, param_string2: str) -> int:
    """
    禄神:
    甲禄在寅, 乙禄在卯,
    丙戊禄在巳, 丁己禄在午,
    庚禄在申, 辛禄在酉,
    壬禄在亥, 癸禄在子.
    查法: 以日干查四支
    """
    conditions = {
        "甲": "寅",
        "乙": "卯",
        "丙": "巳",
        "丁": "午",
        "戊": "巳",
        "己": "午",
        "庚": "申",
        "辛": "酉",
        "壬": "亥",
        "癸": "子",
    }
    return 1 if conditions.get(param_string1) == param_string2 else 0


def tianshe(yue_zhi: str, ri_gan: str, ri_zhi: str) -> int:
    """
    天赦:
    春戊寅, 夏甲午, 秋戊申, 冬甲子.
    查法: 以月支为令, 日柱见者为是
    """
    conditions = {
        "寅": ri_gan == "戊" and ri_zhi == "寅",
        "卯": ri_gan == "戊" and ri_zhi == "寅",
        "辰": ri_gan == "戊" and ri_zhi == "寅",
        "巳": ri_gan == "甲" and ri_zhi == "午",
        "午": ri_gan == "甲" and ri_zhi == "午",
        "未": ri_gan == "甲" and ri_zhi == "午",
        "申": ri_gan == "戊" and ri_zhi == "申",
        "酉": ri_gan == "戊" and ri_zhi == "申",
        "戌": ri_gan == "戊" and ri_zhi == "申",
        "亥": ri_gan == "甲" and ri_zhi == "子",
        "子": ri_gan == "甲" and ri_zhi == "子",
        "丑": ri_gan == "甲" and ri_zhi == "子",
    }
    return 1 if conditions.get(yue_zhi, False) else 0


def hongluan(param_string1: str, param_string2: str) -> int:
    """
    红鸾：
    红鸾年支:
     子 丑 寅 卯 辰 巳 午 未 申 酉 戌 亥
    其他地支见:
     卯 寅 丑 子 亥 戌 酉 申 未 午 巳 辰
    查法：以年支查余地支。如子年生人见卯为红鸾，见酉为天喜。
    """
    conditions = {
        "子": "卯",
        "丑": "寅",
        "寅": "丑",
        "卯": "子",
        "辰": "亥",
        "巳": "戌",
        "午": "酉",
        "未": "申",
        "申": "未",
        "酉": "午",
        "戌": "巳",
        "亥": "辰",
    }
    return 1 if conditions.get(param_string1) == param_string2 else 0


def tianxi(param_string1: str, param_string2: str) -> int:
    """
    天喜:
    天喜年支:
     子 丑 寅 卯 辰 巳 午 未 申 酉 戌 亥
    其他地支见:
     酉 申 未 午 巳 辰 卯 寅 丑 子 亥 戌
    """
    conditions = {
        "子": "酉",
        "丑": "申",
        "寅": "未",
        "卯": "午",
        "辰": "巳",
        "巳": "辰",
        "午": "卯",
        "未": "寅",
        "申": "丑",
        "酉": "子",
        "戌": "亥",
        "亥": "戌",
    }
    return 1 if conditions.get(param_string1) == param_string2 else 0


def liuxia(param_string1: str, param_string2: str) -> int:
    """
    流霞:
    甲日酉。乙日戌。丙日未。丁日申。戊日巳。
    己日午。庚日辰。辛日卯。壬日亥。癸日寅。
    查法：以日干查四地支
    """
    conditions = {
        "甲": "酉",
        "乙": "戌",
        "丙": "未",
        "丁": "申",
        "戊": "巳",
        "己": "午",
        "庚": "辰",
        "辛": "卯",
        "壬": "亥",
        "癸": "寅",
    }
    return 1 if conditions.get(param_string1) == param_string2 else 0


def hongyan(param_string1: str, param_string2: str) -> int:
    """
    红艳:
    甲日午。乙日午。丙日寅。丁日未。戊日辰。
    己日辰。庚日戌。辛日酉。壬日子。癸日申。
    查法：以日干查四地支
    """
    conditions = {
        "甲": "午",
        "乙": "午",
        "丙": "寅",
        "丁": "未",
        "戊": "辰",
        "己": "辰",
        "庚": "戌",
        "辛": "酉",
        "壬": "子",
        "癸": "申",
    }
    return 1 if conditions.get(param_string1) == param_string2 else 0


def xueren(param_string1: str, param_string2: str) -> int:
    """
    血刃:
    寅月丑。卯月未。辰月寅。巳月申。午月卯。未月酉。
    申月辰。酉月戌。戌月巳。亥月亥。子月午。丑月子。
    查法：以月支查四柱干支
    """
    conditions = {
        "子": "午",
        "丑": "子",
        "寅": "丑",
        "卯": "未",
        "辰": "寅",
        "巳": "申",
        "午": "卯",
        "未": "酉",
        "申": "辰",
        "酉": "戌",
        "戌": "巳",
        "亥": "亥",
    }
    return 1 if conditions.get(param_string1) == param_string2 else 0


def bazhuan(param_string1: str, param_string2: str) -> int:
    """
    古决:
    甲寅日。乙卯日。丁未日。戊戌日。己未日。庚申日。辛酉日。癸丑日。
    查法: 查日柱
    此日柱，大抵天干坐禄或冠带，通常是身体比较好的人，生理欲望强，容易因酒、色而招来困扰、失败。
    """
    valid_pairs = [
        ["甲", "寅"],
        ["乙", "卯"],
        ["丁", "未"],
        ["戊", "戌"],
        ["己", "未"],
        ["庚", "申"],
        ["辛", "酉"],
        ["癸", "丑"],
    ]
    return 1 if [param_string1, param_string2] in valid_pairs else 0


def jiuchou(param_string1: str, param_string2: str) -> int:
    """
    九丑:
    丁酉日。戊子日。戊午日。己卯日。己酉日。辛卯日。辛酉日。壬子日。壬午日。
    查法：查日柱
    此煞名"丑"，不是指容貌不好看，相反的，此日生者大多容貌美丽，或很有吸引人的魅力。其所以名"丑"，
    是指名声方面的风评，因感情的事容易出问题，严重的可能会惹上法律纠纷，名声受损。
    """
    valid_pairs = [
        ["丁", "酉"],
        ["戊", "子"],
        ["戊", "午"],
        ["己", "卯"],
        ["己", "酉"],
        ["辛", "卯"],
        ["辛", "酉"],
        ["壬", "子"],
        ["壬", "午"],
    ]
    return 1 if [param_string1, param_string2] in valid_pairs else 0


def yuancheng(
    param_string1: str, param_string2: str, is_man: bool, is_yang: bool
) -> int:
    """
    元辰：
    阳男阴女：子年未。丑年申。寅年酉。卯年戌。辰年亥。巳年子。午年丑。未年寅。申年卯。酉年辰。戌年巳。亥年午。
    阴男阳女：子年巳。丑年午。寅年未。卯年申。辰年酉。巳年戌。午年亥。未年子。申年丑。酉年寅。戌年卯。亥年辰。
    查法：以年支查余三支
    """
    yang_nan_yin_nv = {
        "子": "未",
        "丑": "申",
        "寅": "酉",
        "卯": "戌",
        "辰": "亥",
        "巳": "子",
        "午": "丑",
        "未": "寅",
        "申": "卯",
        "酉": "辰",
        "戌": "巳",
        "亥": "午",
    }
    yin_nan_yang_nv = {
        "子": "巳",
        "丑": "午",
        "寅": "未",
        "卯": "申",
        "辰": "酉",
        "巳": "戌",
        "午": "亥",
        "未": "子",
        "申": "丑",
        "酉": "寅",
        "戌": "卯",
        "亥": "辰",
    }

    is_yang_nan_yin_nv = is_man == is_yang  # True: 阳男阴女, False: 阴男阳女
    conditions = yang_nan_yin_nv if is_yang_nan_yin_nv else yin_nan_yang_nv
    return 1 if conditions.get(param_string1) == param_string2 else 0


def sifei(yue_zhi: str, ri_gan: str, ri_zhi: str) -> int:
    """
    四废:
    春庚申, 辛酉,
    夏壬子, 癸亥,
    秋甲寅, 乙卯,
    冬丙午, 丁巳.
    查法: 凡四柱日干支生于该季为是
    """
    conditions = {
        "寅": ["庚申", "辛酉"],
        "卯": ["庚申", "辛酉"],
        "辰": ["庚申", "辛酉"],
        "巳": ["壬子", "癸亥"],
        "午": ["壬子", "癸亥"],
        "未": ["壬子", "癸亥"],
        "申": ["甲寅", "乙卯"],
        "酉": ["甲寅", "乙卯"],
        "戌": ["甲寅", "乙卯"],
        "亥": ["丙午", "丁巳"],
        "子": ["丙午", "丁巳"],
        "丑": ["丙午", "丁巳"],
    }

    key = conditions.get(yue_zhi, [])
    return 1 if (ri_gan + ri_zhi) in key else 0


def xuetang(nian_nayin: str, gan: str, zhi: str) -> int:
    """
    学堂:
    年柱纳音为金命见其他三支有"巳"为学堂，见"辛巳"为正学堂（海中金、剑锋金、沙中金都为纳音金命）；
    年柱纳音为木命见其他三支有"亥"为学堂，见"己亥"为正学堂；
    年柱纳音为水命见其他三支有"申"为学堂，见"甲申"为正学堂；
    年柱纳音为土命见其他三支有"申"为学堂，见"戊申"为正学堂；
    年柱纳音为火命见其他三支有"寅"为学堂，见"丙寅"为正学堂。
    """
    conditions = {
        "金": ["巳", ["辛", "巳"]],
        "木": ["亥", ["己", "亥"]],
        "水": ["申", ["甲", "申"]],
        "土": ["申", ["戊", "申"]],
        "火": ["寅", ["丙", "寅"]],
    }

    if nian_nayin in conditions:
        main_match, sub_match = conditions[nian_nayin]
        return (
            1
            if zhi == main_match or (gan == sub_match[0] and zhi == sub_match[1])
            else 0
        )
    return 0


def ciguan(nian_nayin: str, gan: str, zhi: str) -> int:
    """
    词馆:
    年柱纳音为金命见其他三支有"申"为学堂，见"壬申"为正学堂（海中金、剑锋金、沙中金都为纳音金命）；
    年柱纳音为木命见其他三支有"寅"为学堂，见"庚寅"为正学堂；
    年柱纳音为水命见其他三支有"亥"为学堂，见"癸亥"为正学堂；
    年柱纳音为土命见其他三支有"亥"为学堂，见"丁亥"为正学堂；
    年柱纳音为火命见其他三支有"巳"为学堂，见"乙巳"为正学堂。
    """
    conditions = {
        "金": ["申", ["壬", "卯"]],
        "木": ["寅", ["庚", "寅"]],
        "水": ["亥", ["癸", "亥"]],
        "土": ["亥", ["丁", "亥"]],
        "火": ["巳", ["乙", "巳"]],
    }

    if nian_nayin in conditions:
        main_match, sub_match = conditions[nian_nayin]
        return (
            1
            if zhi == main_match or (gan == sub_match[0] and zhi == sub_match[1])
            else 0
        )
    return 0
