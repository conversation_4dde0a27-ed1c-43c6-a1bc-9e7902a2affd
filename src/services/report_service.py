from datetime import datetime, timedelta
from src.utils.arq_task_utils import generate_job_id_with_order_id_and_type
from src.client.database.order_client import order_client
from src.common.auth import JWTAuth
from src.config import settings
from src.configure.arq_config import arq_config
from src.models.database.orders import Order
from src.models.workflow_model import AliyunWorkflow
from src.services.workflow_service import workflow_service
from src.utils.constants import OrderStatus
from src.utils.log_util import logger
from src.common.arq_redis_pool import get_redis_pool
from typing import Optional


class ReportService:

    async def generate_report(self, order: Order) -> Optional[str]:
        order.status = OrderStatus.PROCESSING.value
        await order_client.update_order_by_order_id(order.order_id, order)
        order_extra_info = order.extra_info
        if order_extra_info is None:
            order_extra_info = {}
        report_version = order_extra_info.get("report_version", "1.0")

        if report_version == "1.0":
            workflow_name = "order_generate"
        else:
            workflow_name = "order_generate_with_version"

        access_token_expires = timedelta(minutes=JWTAuth.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = JWTAuth.create_access_token(
            data={"sub": order.user_id}, expires_delta=access_token_expires
        )

        workflow = AliyunWorkflow(
            workflow_execution_id=order.order_id
            + "_"
            + str(int(datetime.now().timestamp())),
            workflow_name=workflow_name,
            workflow_input={
                "jwt_token": "Bearer " + access_token,
                "order_id": order.order_id,
                "profile_id": order.profile_id,
                "product_id": order.product_id,
                "report_version": report_version,
            },
        )
        try:
            if settings.USE_ARQ_TO_GENERATE_REPORT:
                # 启动ARQ异步任务处理订单工作流
                redis_pool = await get_redis_pool()

                task_result = await redis_pool.enqueue_job(
                    "process_order_workflow",
                    order_id=order.order_id,
                    user_id=order.user_id,
                    profile_id=order.profile_id,
                    product_id=order.product_id,
                    report_version=report_version,
                    _queue_name=arq_config.get_queue_name(),
                    _job_id=generate_job_id_with_order_id_and_type(
                        order.order_id,
                        "process_order_workflow",
                        "all",
                    ),
                )
                if not task_result:
                    raise Exception("Failed to enqueue workflow task")

                logger.info(
                    f"ARQ workflow task started: task_id={task_result.job_id}, "
                    f"order_id={order.order_id}"
                )

                return task_result.job_id
            else:
                await workflow_service.execute_workflow(workflow)
        except Exception as e:
            logger.error(f"workflow execution failed: {e}, order_id: {order.order_id}")

        return None


report_service = ReportService()
