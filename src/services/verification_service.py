import random
import string
from datetime import datetime, timedelta

from src.models.database.verification_codes import VerificationCode
from src.models.verification import VerificationCodeResponse
from src.client.database.verification_code_client import verification_code_client
from src.utils.log_util import logger
from src.client.spug_sms_client import spug_sms_client

EXPIRE_TIME_MINUTES = 5


class VerificationService:
    def __init__(self) -> None:
        # Initialize whitelist for testing/development
        self.whitelist = [
            {
                "phone_number": "17751781262",
                "verification_code": "123456",
            },
            {
                "phone_number": "13326291344",
                "verification_code": "123456",
            },
            {
                "phone_number": "18811313396",
                "verification_code": "123456",
            },
        ]

    def generate_verification_code(self, length: int = 6) -> str:
        """Generate a random verification code"""
        return "".join(random.choices(string.digits, k=length))

    async def send_verification_code(
        self, phone_number: str
    ) -> VerificationCodeResponse:
        """
        Send a verification code to the phone number via Spug push service

        Args:
            phone_number: The phone number to send the code to

        Returns:
            VerificationCode: The verification code object (Pydantic model)
        """
        # Generate a 6-digit code
        code = self.generate_verification_code()

        # Set expiration time (5 minutes from now)
        expires_at = datetime.now() + timedelta(minutes=EXPIRE_TIME_MINUTES)

        # Create database verification code record
        db_verification = VerificationCode(
            phone_number=phone_number,
            verification_code=code,
            is_used=False,
            expires_at=expires_at,
        )

        try:
            # Send SMS using Spug SMS client
            await spug_sms_client.send_verification_code(phone_number, code)

            # Store in database
            await verification_code_client.create_verification_code(db_verification)

            logger.info(f"Verification code {code} sent to {phone_number}")

        except Exception as e:
            logger.error(f"Failed to send verification code: {str(e)}")
            raise Exception(f"Failed to send verification code: {str(e)}")

        # Convert database model to Pydantic model for return
        return VerificationCodeResponse.model_validate(db_verification)

    async def verify_code(self, phone_number: str, code: str) -> bool:
        """
        Verify a code for a phone number

        Args:
            phone_number: The phone number
            code: The verification code to verify

        Returns:
            bool: True if the code is valid, False otherwise
        """
        # Check whitelist first
        for whitelist_item in self.whitelist:
            if (
                whitelist_item["phone_number"] == phone_number
                and whitelist_item["verification_code"] == code
            ):
                return True

        # Get verification from database
        verification = (
            await verification_code_client.get_valid_verification_code_by_phone_number(
                phone_number
            )
        )

        # Check if verification code exists
        if not verification:
            logger.warning(f"No verification code found for {phone_number}")
            return False

        # Note: Expired codes are already filtered out in the database query
        # No need to check expires_at here since get_by_phone_number only returns non-expired codes

        # Check if code is already used
        if verification.is_used:
            logger.warning(f"Verification code for {phone_number} is already used")
            return False

        # Check if code matches
        if verification.verification_code != code:
            logger.warning(f"Invalid verification code for {phone_number}")
            return False

        # Mark code as used
        await verification_code_client.mark_as_used(verification.verification_id)

        logger.info(f"Verification successful for {phone_number}")
        return True


# Create a singleton instance
verification_service = VerificationService()
