from src.client.aliyun_workflow_client import <PERSON><PERSON>WorkflowClient
from src.models.workflow_model import (
    AliyunWorkflow,
)
from src.client.database.order_client import OrderClient
from uuid import uuid4


class WorkflowService:
    def __init__(self) -> None:
        self.aliyun_workflow_client = <PERSON>yunWorkflowClient()
        self.order_client = OrderClient()

    async def execute_workflow(self, workflow: <PERSON>yunWorkflow) -> AliyunWorkflow:
        if not workflow.workflow_execution_id:
            workflow.workflow_execution_id = str(uuid4())
        await self.aliyun_workflow_client.start_execution(
            workflow.workflow_name,
            workflow.workflow_execution_id,
            workflow.workflow_input,
        )
        return workflow


workflow_service = WorkflowService()
