import json
import uuid
from datetime import datetime
from src.services.report_service import report_service
from src.services.yuxu_coin_service import yuxu_coin_service
from src.utils.constants import OrderStatus, OrderType
from src.models.database.orders import Order
from src.models.database.products import Product
from src.common.session import get_session
from fastapi import HTTPException
from sqlalchemy import select
from typing import List, Optional, Dict, Any
from sqlalchemy import and_
from src.models.order_model import (
    OrderQueryParams,
    OrderResponse,
    OrderWithProductResponse,
)
from src.client.apple.apple_client import apple_client
from src.utils.log_util import logger
from src.models.database.orders import Transaction
from src.client.database.order_client import order_client
from src.client.database.product_client import product_client
from src.client.database.user_client import user_client
from src.models.product_model import ProductGroup, ProductResponse
from src.utils import time_utils
from deprecated import deprecated  # type: ignore


class OrderService:
    def __init__(self) -> None:
        self.order_client = order_client
        self.product_client = product_client
        self.user_client = user_client

    @deprecated(reason="Use create_order_with_different_product instead")
    async def create_order(
        self,
        user_id: str,
        product_id: str,
        profile_id: str,
        report_version: Optional[str] = "1.0",
    ) -> OrderResponse:
        # 验证个人资料
        profile = await self.user_client.get_profile_by_id(profile_id)
        if not profile or profile.user_id != user_id:
            raise HTTPException(status_code=404, detail="个人资料不存在或无权访问")

        # 验证产品信息
        product = await self.product_client.get_active_product_by_product_id(product_id)
        if not product:
            raise HTTPException(status_code=404, detail="产品不存在")

        # check if user has already a running order
        orders = await self.order_client.get_orders_by_params(
            Order(user_id=user_id, profile_id=profile_id, product_id=product_id)
        )

        # 批量加锁检查订单状态
        if orders:
            order_ids = [order.order_id for order in orders]
            locked_orders = await order_client.batch_get_orders_by_ids_for_update(
                order_ids
            )

            for locked_order in locked_orders:
                if locked_order.status in [
                    OrderStatus.PROCESSING.value,
                    OrderStatus.PURCHASED.value,
                    OrderStatus.SUCCESS.value,
                ]:
                    raise HTTPException(
                        status_code=400, detail="用户已有正在进行或已成功的订单"
                    )
                elif locked_order.status == OrderStatus.INIT.value:
                    return OrderResponse.model_validate(locked_order)

        # 创建订单
        order = Order(
            order_id=str(uuid.uuid4()),
            user_id=user_id,
            profile_id=profile_id,
            product_id=product_id,
            amount=1,
            status=OrderStatus.INIT.value,
            order_type=OrderType.NEW.value,
        )
        if report_version:
            order.extra_info = {"report_version": report_version}

        created_order = await self.order_client.create_order(order)
        return OrderResponse.model_validate(created_order)

    async def create_order_with_different_product(
        self,
        user_id: str,
        product_id: str,
        profile_id: Optional[str] = None,
        report_version: Optional[str] = "1.0",
    ) -> OrderResponse:
        """
        创建订单
        :param user_id: 用户ID
        :param product_id: 产品ID
        :param profile_id: 个人资料ID
        :param report_version: 报告版本
        :return: 订单响应
        """
        product = await self.product_client.get_active_product_by_product_id(product_id)
        if product is None:
            raise HTTPException(status_code=404, detail="产品不存在")
        if product.product_group == ProductGroup.REPORT.value:
            return await self.create_report_order(
                user_id, product, profile_id, report_version
            )
        elif product.product_group == ProductGroup.YUXU_COIN.value:
            return await self.create_yuxu_coin_order(user_id, product)
        else:
            raise HTTPException(status_code=400, detail="产品组不存在")

    async def create_report_order(
        self,
        user_id: str,
        product: Product,
        profile_id: Optional[str] = None,
        report_version: Optional[str] = "1.0",
    ) -> OrderResponse:
        if profile_id is None:
            raise HTTPException(status_code=400, detail="个人资料ID不能为空")
        profile = await self.user_client.get_profile_by_id(profile_id)
        if not profile or profile.user_id != user_id:
            raise HTTPException(status_code=404, detail="个人资料不存在或无权访问")
        # 检查该用户，该profile_id，该product_id是否已有进行中的订单
        orders = await self.order_client.get_orders_by_params(
            Order(user_id=user_id, profile_id=profile_id, product_id=product.product_id)
        )

        # 批量加锁检查订单状态
        if orders:
            order_ids = [order.order_id for order in orders]
            locked_orders = await order_client.batch_get_orders_by_ids_for_update(
                order_ids
            )

            for locked_order in locked_orders:
                if locked_order.status in [
                    OrderStatus.PROCESSING.value,
                    OrderStatus.PURCHASED.value,
                    OrderStatus.SUCCESS.value,
                ]:
                    raise HTTPException(
                        status_code=400, detail="用户已有正在进行或已成功的订单"
                    )
                elif locked_order.status == OrderStatus.INIT.value:
                    return OrderResponse.model_validate(locked_order)

        # 创建订单
        order = Order(
            order_id=str(uuid.uuid4()),
            user_id=user_id,
            profile_id=profile_id,
            product_id=product.product_id,
            amount=1,
            status=OrderStatus.INIT.value,
            order_type=OrderType.NEW.value,
        )
        # 填充订单价格，解锁报告暂时写死为使用玉虚币
        self.fill_order_price(order, product, currency="YUXU")
        if report_version:
            order.extra_info = {"report_version": report_version}
        created_order = await self.order_client.create_order(order)
        return OrderResponse.model_validate(created_order)

    async def create_yuxu_coin_order(
        self,
        user_id: str,
        product: Product,
    ) -> OrderResponse:
        order = Order(
            order_id=str(uuid.uuid4()),
            user_id=user_id,
            product_id=product.product_id,
            amount=1,
            status=OrderStatus.INIT.value,
            order_type=OrderType.NEW.value,
        )
        # 玉虚币目前只支持真钱购买，不填充价格，支付后回填价格
        created_order = await self.order_client.create_order(order)
        return OrderResponse.model_validate(created_order)

    def fill_order_price(
        self, order: Order, product: Product, currency: str = "YUXU"
    ) -> None:
        """
        填充订单价格
        :param order: 订单
        :param product: 产品
        :param currency: 货币
        """
        if not product.product_group == ProductGroup.REPORT.value:
            return
        payment_method = json.loads(product.payment_method)
        price = payment_method.get("price").get(currency)
        order.single_price = price
        order.total_price = price * order.amount
        order.currency = currency

    async def query_orders(
        self, filters: OrderQueryParams, user_id: Optional[str] = None
    ) -> List[OrderResponse]:
        """
        通用订单查询服务
        :param filters: 查询参数对象
        :param user_id: 用户ID
        :return: 订单列表
        """
        try:
            if user_id:
                filters.user_id = user_id
            order_params = Order(**filters.to_filter_dict())
            orders = await self.order_client.get_orders_by_params(order_params)
            return [OrderResponse.model_validate(order) for order in orders]
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"查询订单失败: {str(e)}")

    @time_utils.timing()
    async def _verify_transaction(
        self, transaction_id: str, apple_product_id: str
    ) -> Any:
        """
        验证交易信息
        :param transaction_id: 交易ID
        :param apple_product_id: Apple产品ID
        :return: 验证后的交易信息
        """
        # 处理模拟交易或真实交易
        apple_transaction = None
        if transaction_id == "mock_transaction_id":
            transaction_id = "mock_" + uuid.uuid4().hex
            apple_transaction = {
                "productId": apple_product_id,
                "transactionId": transaction_id,
                "originalTransactionId": transaction_id,
                "purchaseDate": int(datetime.now().timestamp() * 1000),
                "environment": "Sandbox",
                "price": 100000,
                "currency": "CNY",
            }
        else:
            # 获取真实交易信息
            apple_transaction = await apple_client.get_transaction(transaction_id)

        # 检查交易是否存在
        if apple_transaction is None:
            logger.error(f"apple_transaction is None, transaction_id: {transaction_id}")
            raise Exception("交易不存在")

        logger.info(f"apple_transaction: {apple_transaction}")
        # 验证交易信息
        if not apple_transaction.get("productId") == apple_product_id:
            raise Exception("交易信息验证失败")

        return apple_transaction, transaction_id

    @deprecated(reason="Use purchase_service.verify_purchase instead")
    @time_utils.timing()
    async def verify_purchase(
        self, order_id: str, transaction_id: str, product_id: str, user_id: str
    ) -> Order:
        logger.info(
            f"verify_purchase, order_id: {order_id}, transaction_id: {transaction_id}, product_id: {product_id}, user_id: {user_id}"
        )
        async with get_session() as session:
            # 查询订单
            order_result = await session.execute(
                select(Order)
                .where(Order.order_id == order_id, Order.user_id == user_id)
                .with_for_update()
            )
            order = order_result.scalars().first()

            if not order:
                raise Exception("订单不存在")
            if order.status not in [
                OrderStatus.INIT.value,
                OrderStatus.PURCHASE_FAILED.value,
                OrderStatus.CANCELED.value,
            ]:
                logger.info(
                    f"order_id: {order_id}, transaction_id: {transaction_id} order already in status {order.status}, skipping verification"
                )
                return order

            # 查询产品
            product_result = await session.execute(
                select(Product).where(Product.product_id == product_id)
            )
            product = product_result.scalars().first()
            if not product:
                logger.error(f"product is None, product_id: {product_id}")
                raise Exception("产品不存在")
            apple_product_id = product.apple_product_id
            if not apple_product_id:
                logger.error(f"apple_product_id is None, product_id: {product_id}")
                raise Exception("非苹果产品")
            try:
                # 验证交易信息
                apple_transaction, transaction_id = await self._verify_transaction(
                    transaction_id, apple_product_id
                )
                logger.info(
                    f"apple_transaction: {apple_transaction}, order_id: {order_id}"
                )

                # 处理各种时间戳
                purchase_date = time_utils.parse_timestamp_to_datetime(
                    apple_transaction.get("purchaseDate"), datetime.now()
                )
                expires_date = time_utils.parse_timestamp_to_datetime(
                    apple_transaction.get("expiresDate")
                )
                revocation_date = time_utils.parse_timestamp_to_datetime(
                    apple_transaction.get("revocationDate")
                )

                # 创建交易记录
                transaction = Transaction(
                    transaction_id=transaction_id,
                    original_transaction_id=apple_transaction.get(
                        "originalTransactionId"
                    ),
                    transaction_type="charge",
                    order_id=order_id,
                    environment=apple_transaction.get("environment"),
                    price=apple_transaction.get("price"),
                    currency=apple_transaction.get("currency"),
                    purchase_date=purchase_date,
                    expires_date=expires_date,
                    revocation_date=revocation_date,
                    revocation_reason=apple_transaction.get("revocationReason"),
                )
                session.add(transaction)

                # 更新订单状态
                order.status = OrderStatus.PURCHASED.value
                order.product_id = product_id
                order.total_price = transaction.price
                order.currency = transaction.currency
                order.environment = transaction.environment
                order.single_price = transaction.price
                order.amount = 1
                await session.commit()
                await session.refresh(order)
                return order
            except Exception as e:
                logger.error(
                    f"order_id: {order_id}, verify_purchase failed, error: {e}"
                )
                # 先回滚事务，撤销之前的所有修改
                await session.rollback()
                # 然后单独更新订单状态为失败
                order.status = OrderStatus.PURCHASE_FAILED.value
                await session.commit()
                await session.refresh(order)
                raise HTTPException(status_code=500, detail=f"验证购买失败: {str(e)}")

    async def update_order_status(
        self, order_id: str, status: str, user_id: Optional[str] = None
    ) -> Order:
        async with get_session() as session:
            # 查询订单
            result = await session.execute(
                select(Order)
                .where(Order.order_id == order_id, Order.user_id == user_id)
                .with_for_update()
            )
            order = result.scalars().first()
            if not order:
                raise HTTPException(status_code=404, detail="订单不存在")

            # 更新订单状态
            order.status = status
            await session.commit()
            await session.refresh(order)
            return order

    async def get_purchased_reports(
        self, user_id: str, profile_id: Optional[str] = None
    ) -> Dict[str, List[OrderWithProductResponse]]:
        """
        获取用户已购买的产品信息
        :param user_id: 用户ID
        :param profile_id: 可选的个人资料ID
        :return: 按profile_id分组的订单和产品信息
        """
        async with get_session() as session:
            # 构建查询条件，只查询已购买的产品
            conditions = [
                Order.user_id == user_id,
                Order.status.in_(
                    [
                        OrderStatus.PURCHASED.value,
                        OrderStatus.SUCCESS.value,
                        OrderStatus.PROCESSING.value,
                    ]
                ),
            ]

            # 如果指定了profile_id，添加到查询条件
            if profile_id:
                conditions.append(Order.profile_id == profile_id)

            # 查询所有符合条件的订单
            order_query = select(Order).where(and_(*conditions))
            order_result = await session.execute(order_query)
            orders = list(order_result.scalars().all())

            # 查询所有报告类产品
            product_query = select(Product).where(
                Product.product_group == ProductGroup.REPORT.value
            )
            product_result = await session.execute(product_query)
            products = list(product_result.scalars().all())

        # 按profile_id对order分组
        profile_ids_orders: Dict[str, List[OrderResponse]] = {}
        for order in orders:
            if order.profile_id not in profile_ids_orders:
                profile_ids_orders[order.profile_id] = []
            profile_ids_orders[order.profile_id].append(
                OrderResponse.model_validate(order)
            )

        # 创建产品ID到产品的映射
        product_map = {
            product.product_id: ProductResponse.model_validate(product)
            for product in products
        }
        # 按profile_id分组订单和具体产品（sale_type == 0）
        profile_orders: Dict[str, List[OrderWithProductResponse]] = {}

        # 分组处理
        for profile_id, p_orders in profile_ids_orders.items():
            profile_orders[profile_id] = []
            profile_product_ids = []
            all_reports_order = None
            for p_order in p_orders:
                product = product_map.get(p_order.product_id)
                if product is None:
                    continue
                if product.sale_type == 0:
                    profile_orders[profile_id].append(
                        OrderWithProductResponse(order=p_order, product=product)
                    )
                    profile_product_ids.append(product.product_id)
                else:
                    all_reports_order = p_order
            # 存在全部解锁订单
            if all_reports_order:
                for product_id, product in product_map.items():
                    if product_id not in profile_product_ids and product.sale_type == 0:
                        profile_orders[profile_id].append(
                            OrderWithProductResponse(
                                order=all_reports_order, product=product
                            )
                        )

        return profile_orders

    @time_utils.timing()
    async def deliver_order(self, order_id: str) -> None:
        try:
            order = await order_client.get_order_by_id_for_update(order_id)
            if not order:
                raise ValueError(f"order_id: {order_id} order not found")

            if order.status != OrderStatus.PURCHASED.value:
                raise ValueError(f"order_id: {order_id} order status is not PURCHASED")

            product = await self.product_client.get_active_product_by_product_id(
                order.product_id
            )
            if not product:
                raise ValueError(f"product_id: {order.product_id} product not found")

            if product.product_group == ProductGroup.REPORT.value:
                await report_service.generate_report(order)
            elif product.product_group == ProductGroup.YUXU_COIN.value:
                await yuxu_coin_service.deliver_yuxu_coin(order, product)
            else:
                raise ValueError(
                    f"product_id: {order.product_id} product group not supported"
                )
        except Exception as e:
            logger.error(f"Failed to deliver order: {str(e)}", exc_info=True)

    async def get_order_by_order_id(self, order_id: str) -> Optional[Order]:
        return await self.order_client.get_order_by_id(order_id)

    async def get_user_order_count(self, user_id: str) -> int:
        """
        获取用户的订单数量
        使用 get_purchased_reports 的逻辑来统计订单数量

        Args:
            user_id: 用户ID

        Returns:
            int: 用户成功订单数量
        """
        # 使用 get_purchased_reports 获取用户的所有已购买报告
        profile_orders = await self.get_purchased_reports(user_id)

        # 统计所有 profile 下的订单数量
        total_order_count = 0
        for profile_id, orders in profile_orders.items():
            # 获取该 profile 下的唯一订单ID集合
            unique_order_ids = set()
            for order_with_product in orders:
                if order_with_product.order.order_status == OrderStatus.SUCCESS.value:
                    unique_order_ids.add(order_with_product.order.order_id)
            total_order_count += len(unique_order_ids)

        return total_order_count


# Create singleton instance
order_service = OrderService()
