# -*- coding: utf-8 -*-
from datetime import datetime
import logging
from typing import Dict, Any, Optional, List

import json_repair
from lunar_python import <PERSON><PERSON><PERSON>, Solar

from src.chat.llms.llm import get_llm_by_type
from src.chat.prompt.template import get_system_prompt
from src.models.bazi_model import (
    BAZIBaseDataRequest,
    SpecificReportType,
    SpecificReportV2Request,
)
from src.utils.log_util import logger
import json
from src.algorithm.bazi import get_bazi_data
from src.config import settings
from src.utils.schema_validator import schema_validator

from src.client.ark_client import ark_client
from tenacity import before_sleep_log, retry, stop_after_attempt, wait_exponential
import asyncio

KONGWANG_AGENT_ID = settings.KONGWANG_AGENT_ID
YONGSHEN_AGENT_ID = settings.YONGSHEN_AGENT_ID
SHISHEN_AGENT_ID = settings.SHISHEN_AGENT_ID
GEJV_AGENT_ID = settings.GEJV_AGENT_ID
SHENSHA_AGENT_ID = settings.SHENSHA_AGENT_ID
BAZI_AGENT_ID = settings.BAZI_AGENT_ID
DAYUN_AGENT_ID = settings.DAYUN_AGENT_ID
SHIYE_AGENT_ID = settings.SHIYE_AGENT_ID
YINYUAN_AGENT_ID = settings.YINYUAN_AGENT_ID
XUEYE_AGENT_ID = settings.XUEYE_AGENT_ID
CAIYUN_AGENT_ID = settings.CAIYUN_AGENT_ID
JIANKANG_AGENT_ID = settings.JIANKANG_AGENT_ID
FIX_JSON_AGENT_ID = settings.FIX_JSON_AGENT_ID
ARK_API_KEY = settings.ARK_API_KEY


class BaziService:
    # 类级别的信号量容量配置
    _SPECIFIC_REPORT_CONCURRENCY_LIMIT = 12
    # 类级别的信号量，用于限制 get_specific_report_v2 的并发数量
    _specific_report_semaphore = asyncio.Semaphore(_SPECIFIC_REPORT_CONCURRENCY_LIMIT)

    def __init__(
        self,
        attempt_id: Optional[str] = None,
        result_id: Optional[str] = None,
        result_type: Optional[str] = None,
    ) -> None:
        self.ark_client = ark_client
        self.attempt_id = attempt_id
        self.result_id = result_id
        self.result_type = result_type
        self.log_mark = f"[attempt_id: {self.attempt_id}, result_id: {self.result_id}, result_type: {self.result_type}]"
        self._validate_environment()

    def _validate_environment(self) -> None:
        """Validate all required environment variables are set."""
        required_agents = {
            "KONGWANG_AGENT_ID": KONGWANG_AGENT_ID,
            "YONGSHEN_AGENT_ID": YONGSHEN_AGENT_ID,
            "SHISHEN_AGENT_ID": SHISHEN_AGENT_ID,
            "GEJV_AGENT_ID": GEJV_AGENT_ID,
            "SHENSHA_AGENT_ID": SHENSHA_AGENT_ID,
            "BAZI_AGENT_ID": BAZI_AGENT_ID,
        }

        missing_agents = [name for name, value in required_agents.items() if not value]
        if missing_agents:
            raise ValueError(
                f"Missing required environment variables: {', '.join(missing_agents)}"
            )

    async def get_bazi_kongwang_report(self, bazi_data: dict[str, Any]) -> str:
        logger.info(f"{self.log_mark} 开始生成空亡分析报告")
        user_prompt = "用户八字数据+\n" + json.dumps(bazi_data, ensure_ascii=False)
        result = await self.ark_client.chat_with_agent(
            agent_id=KONGWANG_AGENT_ID, user_prompt=user_prompt
        )
        logger.debug(f"{self.log_mark} 空亡分析报告生成完成，报告内容：\n{result}")
        return result

    async def get_bazi_yongshen_report(self, bazi_data: dict[str, Any]) -> str:
        logger.info(f"{self.log_mark} 开始生成用神喜忌分析报告")
        user_prompt = "用户八字数据+\n" + json.dumps(bazi_data, ensure_ascii=False)
        result = await self.ark_client.chat_with_agent(
            agent_id=YONGSHEN_AGENT_ID, user_prompt=user_prompt
        )
        logger.debug(f"{self.log_mark} 用神喜忌分析报告生成完成，报告内容：\n{result}")
        return result

    async def get_bazi_shishen_report(self, bazi_data: dict[str, Any]) -> str:
        logger.info(f"{self.log_mark} 开始生成十神分析报告")
        user_prompt = "用户八字数据+\n" + json.dumps(bazi_data, ensure_ascii=False)
        result = await self.ark_client.chat_with_agent(
            agent_id=SHISHEN_AGENT_ID, user_prompt=user_prompt
        )
        logger.debug(f"{self.log_mark} 十神分析报告生成完成，报告内容：\n{result}")
        return result

    async def get_bazi_gejv_report(self, bazi_data: dict[str, Any]) -> str:
        logger.info(f"{self.log_mark} 开始生成格局分析报告")
        user_prompt = "用户八字数据+\n" + json.dumps(bazi_data, ensure_ascii=False)
        result = await self.ark_client.chat_with_agent(
            agent_id=GEJV_AGENT_ID, user_prompt=user_prompt
        )
        logger.debug(f"{self.log_mark} 格局分析报告生成完成，报告内容：\n{result}")
        return result

    async def get_bazi_shesha_report(self, bazi_data: dict[str, Any]) -> str:
        logger.info(f"{self.log_mark} 开始生成神煞分析报告")
        user_prompt = "用户八字数据+\n" + json.dumps(bazi_data, ensure_ascii=False)
        result = await self.ark_client.chat_with_agent(
            agent_id=SHENSHA_AGENT_ID, user_prompt=user_prompt
        )
        logger.debug(f"{self.log_mark} 神煞分析报告生成完成，报告内容：\n{result}")
        return result

    # TODO 后续下掉这个方法
    async def get_specific_report(
        self,
        bazi_data: str,
        bazi_report: str,
        da_yun_report: str,
        specific_type: SpecificReportType,
    ) -> Dict[str, Any]:
        logger.info(f"{self.log_mark} 开始生成具体分析报告")
        user_prompt = (
            "用户八字数据+\n"
            + bazi_data
            + "\n"
            + "八字分析报告+\n"
            + bazi_report
            + "\n"
            + "大运分析报告+\n"
            + da_yun_report
        )

        # Get the appropriate agent ID based on specific type
        if specific_type == SpecificReportType.SHIYE:
            agent_id = SHIYE_AGENT_ID
        elif specific_type == SpecificReportType.YINYUAN:
            agent_id = YINYUAN_AGENT_ID
        elif specific_type == SpecificReportType.XUEYE:
            agent_id = XUEYE_AGENT_ID
        elif specific_type == SpecificReportType.CAIYUN:
            agent_id = CAIYUN_AGENT_ID
        else:
            raise ValueError(f"Invalid specific_type: {specific_type}")

        # Validate that the agent ID exists
        if not agent_id:
            raise ValueError(f"Agent ID for {specific_type.value} is not set")

        result = await self.ark_client.chat_with_agent(
            agent_id=agent_id, user_prompt=user_prompt, response_format="json_object"
        )
        result = result.replace("格局对学业的核心影响", "格局对姻缘的核心影响")
        return self.process_agent_response(result, f"{specific_type.value}分析报告")

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=2, min=3),
        before_sleep=before_sleep_log(logger, log_level=logging.INFO),
    )
    async def get_specific_report_v2(
        self,
        request: SpecificReportV2Request,
    ) -> Dict[str, Any]:
        async with self._specific_report_semaphore:
            # 计算当前正在处理的请求数 = 总容量 - 当前可用数
            current_processing = (
                self._SPECIFIC_REPORT_CONCURRENCY_LIMIT
                - self._specific_report_semaphore._value
            )
            logger.info(
                f"{self.log_mark} 获取信号量成功，开始处理特定报告生成请求 [当前并发数: {current_processing}/{self._SPECIFIC_REPORT_CONCURRENCY_LIMIT}]"
            )

            try:
                system_prompt = get_system_prompt(
                    SpecificReportType.get_prompt_template(request.specific_type)
                )
                logger.info(f"system_prompt: {system_prompt}")
                user_prompt = f"""
《用户八字基础数据》:
{request.bazi_data}
《用户大运数据》:
{request.dayun_data}
《用户神煞数据》:
{request.shensha_data}
《用户十神数据》:
{request.shishen_data}
《用户格局数据》:
{request.gejv_data}
《用户空亡数据》:
{request.kongwang_data}
《用户用神喜忌数据》:
    {request.yongshen_data}"""
                logger.info(f"{self.log_mark} user_prompt: {user_prompt}")
                # 获取对应报告类型的JSON schema
                report_type = request.specific_type.get_report_type_str()
                try:
                    schema = schema_validator.load_schema(f"{report_type}_v2")
                    schema_prompt = f"""
# 重要
- 你输出的报告，应该严格符合以下json schema的格式：
{json.dumps(schema, ensure_ascii=False, indent=2)}"""
                except (FileNotFoundError, ValueError) as e:
                    logger.warning(
                        f"{self.log_mark} Failed to load schema for {request.specific_type.value}: {e}"
                    )
                    schema_prompt = "请以JSON格式输出报告。"

                # 构建包含schema的消息列表
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                    {"role": "user", "content": schema_prompt},
                ]

                # 配置LLM支持JSON格式输出
                llm = get_llm_by_type("reasoning")
                response = llm.bind(response_format={"type": "json_object"}).astream(
                    messages
                )
                result = ""
                log_interval = 100  # 每100个字符打印一次日志
                last_log_length = 0
                async for chunk in response:
                    if isinstance(chunk.content, str):
                        content_piece = chunk.content
                        result += content_piece
                    elif isinstance(chunk.content, list):
                        # Handle list content by joining string elements
                        content_piece = "".join(
                            str(item) for item in chunk.content if isinstance(item, str)
                        )
                        result += content_piece

                    # 每当累积长度超过间隔时打印日志
                    if len(result) - last_log_length >= log_interval:
                        logger.info(
                            f"{self.log_mark} 流式输出中... 当前长度: {len(result)} 字符"
                        )
                        last_log_length = len(result)

                logger.info(f"{self.log_mark} 流式响应完成，总长度: {len(result)} 字符")
                logger.info(
                    f"finished get_specific_report_v2 response, {self.log_mark}, result: {result}"
                )
                processed_result = self.process_agent_response(
                    result, f"{request.specific_type.value}分析报告"
                )

                # 对所有支持的报告类型进行schema校验
                schema_validator.validate_report(
                    data=processed_result, report_type=report_type, version="v2"
                )
                logger.info(
                    f"{self.log_mark} Schema validation passed for {report_type} report"
                )

                return processed_result
            except Exception as e:
                logger.error(
                    f"{self.log_mark} get specific report v2 failed: {e}", exc_info=True
                )
                raise e
            finally:
                # 注意：这里获取的是释放信号量后的状态
                remaining_processing = self._SPECIFIC_REPORT_CONCURRENCY_LIMIT - (
                    self._specific_report_semaphore._value + 1
                )
                logger.info(
                    f"{self.log_mark} 释放信号量，处理完成 [剩余并发数: {remaining_processing}/{self._SPECIFIC_REPORT_CONCURRENCY_LIMIT}]"
                )

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=2, min=3),
        before_sleep=before_sleep_log(logger, log_level=logging.INFO),
    )
    async def process_bazi_reports(
        self,
        bazi_data: str,
        kongwang: str,
        yongshen: str,
        shishen: str,
        shensha: str,
        gejv: str,
    ) -> Dict[str, Any]:
        if not BAZI_AGENT_ID:
            raise ValueError("BAZI_AGENT_ID environment variable is not set")

        user_prompt = (
            f"《用户八字基础数据》\n{bazi_data}\n"
            f"《用神喜忌分析报告》\n{yongshen}\n "
            f"《空亡分析报告》\n{kongwang}\n"
            f"《格局分析报告》\n{gejv}\n"
            f"《十神分析报告》\n{shishen}\n"
            f"《神煞分析报告》\n{shensha}\n"
        )
        logger.info(f"{self.log_mark} 开始生成八字分析报告, user_prompt: {user_prompt}")
        result = await self._generate_bazi_report_with_retry(user_prompt)
        return self.process_agent_response(result, "八字分析报告")

    async def _generate_bazi_report_with_retry(self, user_prompt: str) -> str:
        """Generate bazi report with retry logic."""
        return await self.ark_client.chat_with_agent(
            agent_id=BAZI_AGENT_ID,
            user_prompt=user_prompt,
            response_format="json_object",
        )

    def process_agent_response(
        self, result: str, report_type: str = "分析报告"
    ) -> Dict[str, Any]:
        """处理 Agent 返回的 JSON 响应
        Args:
            result: Agent 返回的原始响应字符串
            report_type: 日志中显示的报告类型
        Returns:
            Dict[str, Any]: 解析后的 JSON 数据
        """
        result = (
            result.replace("```json", "")
            .replace("```", "")
            .replace("\n", "")
            .replace(" ", "")
        )
        logger.debug(f"{self.log_mark} {report_type}: {result}")
        try:
            return dict(json.loads(result))
        except Exception as e:
            logger.error(e, exc_info=True)
            parsed_result = json_repair.loads(result)
            if not isinstance(parsed_result, dict):
                raise ValueError(
                    f"Expected dictionary response, got {type(parsed_result)}"
                )
            return parsed_result

    async def get_bazi_report(self, request: BAZIBaseDataRequest) -> Dict[str, Any]:
        birth_time = datetime(
            year=int(request.year),
            month=int(request.month),
            day=int(request.day),
            hour=int(request.hour),
            minute=int(request.minute),
        )
        bazi_data = get_bazi_data(birth=birth_time, gender=request.gender)

        # 并行执行所有异步任务
        tasks = [
            self.get_bazi_kongwang_report(bazi_data),
            self.get_bazi_yongshen_report(bazi_data),
            self.get_bazi_shishen_report(bazi_data),
            self.get_bazi_shesha_report(bazi_data),
            self.get_bazi_gejv_report(bazi_data),
        ]

        # 等待所有任务完成
        reports = await asyncio.gather(*tasks)

        # 处理综合报告
        processed_result = await self.process_bazi_reports(
            bazi_data=json.dumps(bazi_data, ensure_ascii=False),
            kongwang=reports[0],
            yongshen=reports[1],
            shishen=reports[2],
            shensha=reports[3],
            gejv=reports[4],
        )

        return processed_result

    async def get_dayun_report(
        self, bazi_data: str, bazi_report: str, dayun_data: str
    ) -> Dict[str, Any]:
        """生成大运分析报告
        Args:
            bazi_data: 八字基础数据
            bazi_report: 八字分析报告
            dayun_data: 大运数据列表
        Returns:
            dict: 大运分析报告
        """
        if not DAYUN_AGENT_ID:
            raise ValueError("DAYUN_AGENT_ID environment variable is not set")

        user_prompt = (
            f"《用户八字基础数据》\n{bazi_data}\n"
            f"《八字分析报告》\n{bazi_report}\n"
            f"《大运数据》\n{dayun_data}\n"
        )
        result = await self.ark_client.chat_with_agent(
            agent_id=DAYUN_AGENT_ID,
            user_prompt=user_prompt,
            response_format="json_object",
        )
        return self.process_agent_response(result, "大运分析报告")

    async def get_dayun_data(
        self, birth_time: datetime, gender: int
    ) -> List[Dict[str, Any]]:
        da_yun_list = (
            EightChar.fromLunar(Solar.fromDate(birth_time).getLunar())
            .getYun(gender)
            .getDaYun()
        )
        res = []
        for i in range(1, len(da_yun_list)):
            da_yun = da_yun_list[i]
            da_yun_info = {
                "开始年份": da_yun.getStartYear(),
                "结束年份": da_yun.getEndYear(),
                "开始年龄": da_yun.getStartAge(),
                "结束年龄": da_yun.getEndAge(),
                "天干": da_yun.getGanZhi()[0],
                "地支": da_yun.getGanZhi()[1],
                "旬": da_yun.getXun(),
                "旬空": da_yun.getXunKong(),
            }
            res.append(da_yun_info)
        return res


bazi_service = BaziService()
