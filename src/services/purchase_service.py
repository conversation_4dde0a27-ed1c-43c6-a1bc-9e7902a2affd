from uuid import uuid4

from src.common.custom_exceptions import PurchaseFailedException
from src.models.database.user_wallet import UserWallet
from src.models.database.yuxucoin_transaction import (
    YuxuCoinTransaction,
    YuxuCoinTransactionRelatedEntityType,
    YuxuCoinTransactionType,
)
from src.models.purchase_model import PaymentMethod

from src.client.database.order_client import order_client
from src.client.database.user_wallet_client import user_wallet_client

from src.utils.log_util import logger
from src.models.database.orders import Order, PURCHASABLE_ORDER_STATUSES
from src.common.session import get_order_session
from src.utils.constants import OrderStatus, ErrorCodes


class PurchaseService:
    """购买服务类，处理各种支付平台的购买验证"""

    def _create_yuxu_transaction_record(
        self, order: Order, user_id: str, user_wallet: UserWallet
    ) -> YuxuCoinTransaction:
        """
        创建玉虚币交易记录

        Args:
            order: 订单对象
            user_id: 用户ID
            user_wallet: 用户钱包对象

        Returns:
            YuxuCoinTransaction: 玉虚币交易记录
        """
        if order.total_price is None:
            raise ValueError(f"Order {order.order_id} has no total_price")

        return YuxuCoinTransaction(
            transaction_log_id=str(uuid4()),
            wallet_id=user_wallet.wallet_id,
            user_id=user_id,
            amount=-order.total_price,  # 负数表示支出
            balance_before=user_wallet.balance,
            balance_after=user_wallet.balance - order.total_price,
            transaction_type=YuxuCoinTransactionType.PURCHASE.value,
            related_entity_type=YuxuCoinTransactionRelatedEntityType.ORDER.value,
            related_entity_id=order.order_id,
            description=f"purchase order {order.order_id}, product {order.product_id}",
        )

    async def purchase_with_yuxu(self, order_id: str, user_id: str) -> Order:
        """
        使用玉虚币购买

        Args:
            order_id: 订单ID
            user_id: 用户ID
        """
        async with get_order_session("Yuxu coin purchase", order_id) as session:
            # 查询订单并验证
            order = await order_client.get_order_with_lock(session, order_id)
            if not order:
                raise PurchaseFailedException(
                    ErrorCodes.ORDER_NOT_FOUND,
                    f"Order {order_id} not found",
                )

            if order.status not in PURCHASABLE_ORDER_STATUSES:
                logger.error(
                    f"Order {order.order_id} status {order.status} is not in allowed statuses: {PURCHASABLE_ORDER_STATUSES}"
                )
                raise PurchaseFailedException(
                    ErrorCodes.ORDER_ALREADY_PURCHASED,
                    f"Order {order.order_id} status {order.status} is not in allowed statuses: {PURCHASABLE_ORDER_STATUSES}",
                )

            if order.user_id != user_id:
                logger.error(
                    f"Order {order.order_id} does not belong to user {user_id}"
                )
                raise PurchaseFailedException(
                    ErrorCodes.ORDER_NOT_BELONG_TO_USER,
                    f"Order {order.order_id} does not belong to user {user_id}",
                )

            # 获取用户钱包并验证余额
            user_wallet = await user_wallet_client.get_user_wallet_with_lock(
                session, user_id
            )

            if user_wallet is None:
                raise PurchaseFailedException(
                    ErrorCodes.SERVER_ERROR,
                    f"User wallet not found for user {user_id}",
                )

            if order.total_price is None:
                raise PurchaseFailedException(
                    ErrorCodes.SERVER_ERROR,
                    f"Order {order.order_id} has no total_price",
                )

            if user_wallet.balance < order.total_price:
                logger.error(
                    f"Insufficient balance, order_id: {order.order_id}, "
                    f"user_id: {user_id}, balance: {user_wallet.balance}, total_price: {order.total_price}"
                )
                raise PurchaseFailedException(
                    ErrorCodes.BALANCE_NOT_ENOUGH,
                    f"Insufficient balance, order_id: {order.order_id}, "
                    f"user_id: {user_id}, balance: {user_wallet.balance}, total_price: {order.total_price}",
                )

            # 创建玉虚币交易记录
            yuxu_transaction = self._create_yuxu_transaction_record(
                order, user_id, user_wallet
            )
            session.add(yuxu_transaction)

            # 更新钱包余额和订单状态
            user_wallet.balance -= order.total_price
            order.status = OrderStatus.PURCHASED.value

            # 确保修改后的对象被添加到session中进行持久化
            session.add(user_wallet)
            session.add(order)

            logger.info(
                f"Purchase completed successfully: order_id={order_id}, user_id={user_id}, amount={order.total_price}"
            )
            return order

    async def purchase(
        self, order_id: str, payment_method: PaymentMethod, user_id: str
    ) -> Order:
        """
        购买订单

        Args:
            order_id: 订单ID
            payment_method: 支付方式
            user_id: 用户ID
        """
        # 暂时只支持玉虚币支付
        if payment_method != PaymentMethod.YUXU:
            raise PurchaseFailedException(
                ErrorCodes.SERVER_ERROR,
                f"Unsupported payment method: {payment_method}",
            )

        return await self.purchase_with_yuxu(order_id, user_id)


# 创建服务实例
purchase_service = PurchaseService()
