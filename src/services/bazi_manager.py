import json
import traceback
from typing import Optional, Dict, Any
from datetime import datetime
from uuid import uuid4
from fastapi import HTTPException
import asyncio

from src.models.bazi_model import (
    BAZIBaseDataRequest,
    BaziResultStatus,
    BAZIResultModel,
    SpecificReportV2Request,
    BAZIReportRequest,
)
from src.models.database.bazi import BaziAttempt, BaziResult
from src.client.database.bazi_attempt_client import bazi_attempt_client
from src.client.database.bazi_result_client import bazi_result_client
from src.services.bazi_service import BaziService
from src.client.oss_client import oss_client
from src.algorithm.bazi import get_bazi_data
from src.utils.log_util import logger
from src.models.bazi_model import SpecificReportType, PreReportType, ReportType
from src.config import settings

REPORT_FILE_KEY = "yuxugong/{aliyun_bucket_env}/{attempt_id}/bazi_report_{result_type}_{result_id}.json"


class BaziManager:
    def __init__(self) -> None:
        self.attempt_client = bazi_attempt_client
        self.result_client = bazi_result_client
        self.s3_client = oss_client
        self.timeout_seconds_map = {
            ReportType.BASIC.value: 1200,
            ReportType.DAYUN.value: 1200,
            ReportType.XUEYE.value: 1200,
            ReportType.SHIYE.value: 1200,
            ReportType.YINYUAN.value: 1200,
            ReportType.CAIYUN.value: 1200,
            PreReportType.PRE_REPORT.value: 1200,
        }

    async def find_existing_calculation_by_attempt(
        self, bazi_attempt_id: str, result_type: str, report_version: str
    ) -> Optional[BAZIResultModel]:
        latest_result = await self.result_client.get_latest_by_attempt_id_and_type(
            bazi_attempt_id, result_type, report_version
        )
        if not latest_result or latest_result.status == BaziResultStatus.FAILED.value:
            return None

        if latest_result.status == BaziResultStatus.SUCCESS.value:
            return BAZIResultModel(
                attempt_id=bazi_attempt_id,
                result_type=latest_result.result_type,
                result_id=latest_result.result_id,
                status=BaziResultStatus(latest_result.status),
                report_version=latest_result.version,
            )

        # if it's pending or running and timeout, set status to failed and return None
        # timeout standard is current time - last_result.updated > self.timeout*2
        time_difference = (datetime.utcnow() - latest_result.updated_at).total_seconds()

        if time_difference > self.timeout_seconds_map[latest_result.result_type] * 2:
            # Update the result status to failed
            latest_result.status = BaziResultStatus.FAILED.value
            latest_result.error = {"message": "Calculation timed out"}
            await self.result_client.update_not_none_element(
                latest_result.result_id, latest_result
            )
            logger.info(
                f"Marking timed out calculation as failed for attempt {bazi_attempt_id}, time difference: {time_difference}"
            )
            return None
        else:
            return BAZIResultModel(
                attempt_id=bazi_attempt_id,
                result_type=latest_result.result_type,
                result_id=latest_result.result_id,
                status=BaziResultStatus(latest_result.status),
                report_version=latest_result.version,
            )

    async def find_existing_calculation(
        self, request: BAZIReportRequest, result_type: str
    ) -> Optional[BAZIResultModel]:
        """Check if calculation exists and return result if available"""
        existing_attempt = await self.attempt_client.get_by_date_and_gender(
            year=int(request.year),
            month=int(request.month),
            day=int(request.day),
            hour=int(request.hour),
            gender=int(request.gender),
        )

        if existing_attempt:
            return await self.find_existing_calculation_by_attempt(
                existing_attempt.attempt_id,
                result_type,
                request.report_version or "2.0",
            )

        return None

    async def create_bazi_attempt_by_date_and_gender_if_not_exists(
        self,
        year: int,
        month: int,
        day: int,
        hour: int,
        gender: int,
    ) -> BaziAttempt:
        attempt = await self.attempt_client.get_by_date_and_gender(
            year, month, day, hour, gender
        )
        if not attempt:
            attempt = await self.attempt_client.create_attempt_idempotent(
                BaziAttempt(
                    attempt_id=str(uuid4()),
                    year=year,
                    month=month,
                    day=day,
                    hour=hour,
                    gender=gender,
                )
            )
        return attempt

    async def create_initial_result(
        self, attempt_id: str, result_type: str, report_version: str
    ) -> BAZIResultModel:
        # create a new bazi_result
        result = BaziResult(
            result_id=str(uuid4()),
            bazi_attempt_id=attempt_id,
            result_type=result_type,
            version=report_version,
            status=BaziResultStatus.PENDING.value,
        )
        created_result = await self.result_client.create_initial_bazi_result_idempotent(
            result
        )
        return BAZIResultModel(
            attempt_id=attempt_id,
            result_type=result_type,
            result_id=created_result.result_id,
            status=BaziResultStatus(created_result.status),
            report_version=report_version,
        )

    async def create_initital_report(
        self, request: BAZIReportRequest, result_type: str
    ) -> BAZIResultModel:
        """Create a new bazi calculation attempt"""
        attempt_to_create = (
            await self.create_bazi_attempt_by_date_and_gender_if_not_exists(
                year=int(request.year),
                month=int(request.month),
                day=int(request.day),
                hour=int(request.hour),
                gender=int(request.gender),
            )
        )
        if not attempt_to_create:
            raise Exception("Failed to create bazi attempt")

        return await self.create_initial_result(
            attempt_to_create.attempt_id, result_type, request.report_version or "2.0"
        )

    async def get_basic_report(
        self, request: BAZIBaseDataRequest, attempt_id: str, result_id: str
    ) -> Dict[str, Any]:
        bazi_service = BaziService(attempt_id, result_id, "basic")
        return await bazi_service.get_bazi_report(request)

    async def wait_for_report_finished(
        self,
        request: BAZIBaseDataRequest,
        attempt_id: str,
        result_id: str,
        result_type: str,
    ) -> str:
        # wait for the basic report to be finished
        latest_result: Optional[BaziResult] = await self.result_client.get_by_result_id(
            result_id
        )
        if not latest_result:
            raise Exception(
                f"Basic report not found for attempt_id: {attempt_id}, result_id: {result_id}"
            )

        while latest_result and latest_result.status not in [
            BaziResultStatus.SUCCESS.value,
            BaziResultStatus.FAILED.value,
        ]:
            await asyncio.sleep(1)
            latest_result = await self.result_client.get_by_result_id(result_id)

        if latest_result and latest_result.status == BaziResultStatus.SUCCESS.value:
            file_key = latest_result.output[f"bazi_{result_type}_report_s3_path"]
            bazi_report = await self.s3_client.read_data(file_key)
            logger.info(
                f"Finished preparing {result_type} report, attempt_id: {attempt_id}, result_id: {latest_result.result_id}"
            )
            return bazi_report
        else:
            raise Exception(
                f"{result_type} report failed for attempt_id: {attempt_id}, result_id: {result_id}"
            )

    async def prepare_report_multi_version(
        self, request: BAZIReportRequest, attempt_id: str, result_type: str
    ) -> str:
        # check if there is an existing basic report
        latest_result = await self.find_existing_calculation(request, result_type)
        if latest_result:
            return await self.wait_for_report_finished(
                request, attempt_id, latest_result.result_id, result_type
            )
        else:
            # create a new basic report
            result = await self.create_initital_report(request, result_type)
            await self.process_calculation_multi_version_with_timeout(
                request, result.attempt_id, result_type, result.result_id
            )
            logger.info(
                f"Finished preparing {result_type} report, attempt_id: {attempt_id}, result_id: {result.result_id}"
            )
            return await self.wait_for_report_finished(
                request, attempt_id, result.result_id, result_type
            )

    async def get_pre_report(
        self,
        request: BAZIReportRequest,
        attempt_id: str,
        result_id: str,
    ) -> Dict[str, Any]:
        birth_time = datetime(
            year=int(request.year),
            month=int(request.month),
            day=int(request.day),
            hour=int(request.hour),
            minute=int(request.minute),
        )
        bazi_service = BaziService(
            attempt_id, result_id, PreReportType.PRE_REPORT.value
        )
        bazi_data = get_bazi_data(birth=birth_time, gender=request.gender)

        # 并行执行所有异步任务
        tasks = {
            PreReportType.KONGWANG_REPORT.value: bazi_service.get_bazi_kongwang_report(
                bazi_data
            ),
            PreReportType.YONGSHEN_REPORT.value: bazi_service.get_bazi_yongshen_report(
                bazi_data
            ),
            PreReportType.SHISHEN_REPORT.value: bazi_service.get_bazi_shishen_report(
                bazi_data
            ),
            PreReportType.SHENSHA_REPORT.value: bazi_service.get_bazi_shesha_report(
                bazi_data
            ),
            PreReportType.GEJV_REPORT.value: bazi_service.get_bazi_gejv_report(
                bazi_data
            ),
        }

        # 等待所有任务完成并将结果存储在字典中
        task_results = await asyncio.gather(*tasks.values())
        reports = dict(zip(tasks.keys(), task_results))
        return reports

    async def get_report_multi_version(
        self,
        request: BAZIReportRequest,
        attempt_id: str,
        result_id: str,
        result_type: str,
    ) -> Dict[str, Any]:
        bazi_service = BaziService(attempt_id, result_id, result_type)
        birth_time = datetime(
            year=int(request.year),
            month=int(request.month),
            day=int(request.day),
            hour=int(request.hour),
            minute=int(request.minute),
        )

        # prepare data for specific report
        logger.info(
            f"Preparing specific report for attempt_id: {attempt_id}, result_id: {result_id}, result_type: {result_type}"
        )
        bazi_data = get_bazi_data(birth=birth_time, gender=request.gender)
        dayun_data = await bazi_service.get_dayun_data(birth_time, request.gender)
        bazi_pre_report_str = await self.prepare_report_multi_version(
            request, attempt_id, PreReportType.PRE_REPORT.value
        )
        bazi_pre_report_dict = json.loads(bazi_pre_report_str)
        logger.info(
            f"Finished preparing pre report, attempt_id: {attempt_id}, result_id: {result_id}, result_type: {result_type}, "
            f"bazi_pre_report_dict: {bazi_pre_report_dict}"
        )

        specific_request = SpecificReportV2Request(
            bazi_data=str(bazi_data),
            dayun_data=str(dayun_data),
            shensha_data=bazi_pre_report_dict[PreReportType.SHENSHA_REPORT.value],
            shishen_data=bazi_pre_report_dict[PreReportType.SHISHEN_REPORT.value],
            gejv_data=bazi_pre_report_dict[PreReportType.GEJV_REPORT.value],
            kongwang_data=bazi_pre_report_dict[PreReportType.KONGWANG_REPORT.value],
            yongshen_data=bazi_pre_report_dict[PreReportType.YONGSHEN_REPORT.value],
            specific_type=SpecificReportType.get_report_type(result_type),
        )
        report = await bazi_service.get_specific_report_v2(specific_request)
        return report

    async def process_calculation_multi_version(
        self,
        request: BAZIReportRequest,
        attempt_id: str,
        result_type: str,
        result_id: str,
    ) -> None:
        """Process the bazi calculation v2 and update result"""
        result = await self.result_client.get_by_result_id(result_id)
        if not result:
            logger.error(f"Result record {result_id} not found")
            return
        # update result status to running
        result.status = BaziResultStatus.RUNNING.value
        await self.result_client.update_not_none_element(result_id, result)

        if result_type == PreReportType.PRE_REPORT.value:
            bazi_info = await self.get_pre_report(request, attempt_id, result_id)
        else:
            bazi_info = await self.get_report_multi_version(
                request, attempt_id, result_id, result_type
            )

        # upload result to s3
        file_key = REPORT_FILE_KEY.format(
            aliyun_bucket_env=settings.ALIYUN_BUCKET_ENV,
            attempt_id=attempt_id,
            result_type=result_type,
            result_id=result_id,
        )
        await self.s3_client.write_data(
            json.dumps(bazi_info, ensure_ascii=False), file_key
        )

        # update result status to success
        result.status = BaziResultStatus.SUCCESS.value
        result.output = {
            f"bazi_{result_type}_report_s3_path": file_key,
        }
        await self.result_client.update_not_none_element(result_id, result)

    async def process_calculation_multi_version_with_timeout(
        self,
        request: BAZIReportRequest,
        attempt_id: str,
        result_type: str,
        result_id: str,
    ) -> None:
        try:
            # Wrap the original process with timeout
            await asyncio.wait_for(
                self.process_calculation_multi_version(
                    request, attempt_id, result_type, result_id
                ),
                timeout=self.timeout_seconds_map[result_type],  # seconds
            )
        except asyncio.TimeoutError:
            # Update result status on timeout
            result = await self.result_client.get_by_result_id(result_id)
            if result:
                result.status = BaziResultStatus.FAILED.value
                result.error = {"message": "Calculation timed out"}
                await self.result_client.update_not_none_element(result_id, result)
            logger.error(
                f"Calculation v2 timed out for attempt_id: {attempt_id}, result_id: {result_id}, result_type: {result_type}"
            )
            raise
        except Exception as e:
            logger.error(
                f"Failed to process bazi calculation v2: {str(e)}", exc_info=True
            )
            result = await self.result_client.get_by_result_id(result_id)
            if result:
                result.status = BaziResultStatus.FAILED.value
                result.error = {"message": str(e), "stacktrace": traceback.format_exc()}
                await self.result_client.update_not_none_element(result_id, result)
            raise

    async def get_calculation_result(self, result_id: str) -> BAZIResultModel:
        """Get the calculation result for an attempt"""
        latest_result = await self.result_client.get_by_result_id(result_id)
        if not latest_result:
            raise HTTPException(status_code=404, detail="Report not found")

        calculation_result: BAZIResultModel = BAZIResultModel(
            attempt_id=latest_result.bazi_attempt_id,
            result_type=latest_result.result_type,
            result_id=latest_result.result_id,
            status=BaziResultStatus(latest_result.status),
            report_version=latest_result.version,
        )
        if latest_result.status == BaziResultStatus.SUCCESS.value:
            # convert original s3 path to download url
            calculation_result.output = {}
            for key, value in latest_result.output.items():
                calculation_result.output[key] = await self.s3_client.get_file_url(
                    value
                )
        elif latest_result.status == BaziResultStatus.FAILED.value:
            calculation_result.error = latest_result.error

        return calculation_result


# Create singleton instance
bazi_manager = BaziManager()
