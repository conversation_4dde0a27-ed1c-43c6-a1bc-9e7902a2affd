from datetime import datetime
from typing import Optional, List
import uuid
import aiofiles
import os

from src.client.database.user_wallet_client import user_wallet_client
from src.common.session import get_session
from src.models.database.user_wallet import UserWallet
from src.models.database.users import UserProfile
from fastapi import HTTPException
from src.models.database.users import User
from src.client.database.user_client import user_client
from src.models.user_model import ProfileRequest, ProfileParams
from src.models.user_model import ProfileResponse, UserInfoResponse
from src.services.order_service import order_service
from src.models.user_model import (
    ProfileWithProductsResponse,
    PurchasedProductResponse,
    UserUpdateRequest,  # Add this import
    ProfileUpdateRequest,
    GiveYuxuCoinRequest,
    GiveYuxuCoinResponse,
    GiveYuxuCoinData,
)  # 添加导入
from src.utils.constants import OrderStatus
from src.utils.log_util import logger
from src.models.database.yuxucoin_transaction import (
    YuxuCoinTransactionRelatedEntityType,
    YuxuCoinTransactionType,
)

MARKDOWN_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "markdown")
PRIVACY_POLICY_PATH = os.path.join(MARKDOWN_DIR, "privacy_policy.md")
USER_AGREEMENT_PATH = os.path.join(MARKDOWN_DIR, "user_agreement.md")


class UserService:
    def __init__(self) -> None:
        self.user_client = user_client
        self.order_service = order_service

    async def get_user_by_phone(self, phone_number: str) -> Optional[User]:
        """
        Get a user by phone number
        """
        return await self.user_client.get_by_phone_number(phone_number)

    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """
        Get a user by user_id
        """
        return await self.user_client.get_by_user_id(user_id)

    async def get_user_by_apple_user_id(
        self,
        apple_user_id: str,
    ) -> Optional[User]:
        """
        Get a user by apple_user_id, or create a new one if not found.
        """
        return await self.user_client.get_by_apple_user_id(apple_user_id)

    async def create_user(
        self,
        phone_number: Optional[str] = None,  # Made optional
        apple_user_id: Optional[str] = None,  # Added apple_user_id
        username_override: Optional[str] = None,  # Added for specific username
        email_override: Optional[str] = None,  # Added for specific email
    ) -> User:
        """
        Create a new user with phone number or Apple ID
        """
        if not phone_number and not apple_user_id:
            raise ValueError("Either phone_number or apple_user_id must be provided")

        # generate username
        if username_override:
            username = username_override
        elif phone_number:
            username = f"user_{phone_number[-4:]}"
        elif (
            apple_user_id
        ):  # Fallback username generation for Apple ID if no name provided
            username = f"apple_user_{apple_user_id[:8]}"
        else:
            # This case should not be reached due to the check above, but as a safeguard:
            username = f"user_{str(uuid.uuid4())[:8]}"

        user = User(
            user_id=str(uuid.uuid4()),
            username=username,
            phone_number=phone_number,
            apple_user_id=apple_user_id,  # Set apple_user_id
            email=email_override,  # Set email
        )

        return await self.user_client.create_user(user)

    async def get_user_profiles(self, user_id: str) -> List[ProfileResponse]:
        """
        Get a user profile by user_id
        """
        profiles = await self.user_client.get_profiles_by_user_id(user_id)
        return [ProfileResponse.model_validate(profile) for profile in profiles]

    # 在 UserService 类中添加新方法
    async def get_user_info(
        self, user_id: str, profile_id: Optional[str] = None
    ) -> UserInfoResponse:
        """
        获取用户信息，包括个人资料和已购买的产品
        """
        user = await self.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 获取用户的个人资料
        profiles = await self.user_client.get_profiles_by_user_id(user_id)

        # 如果指定了profile_id，则只返回该profile的信息
        if profile_id:
            profiles = [p for p in profiles if p.profile_id == profile_id]
            if not profiles:
                raise HTTPException(status_code=404, detail="个人资料不存在")

        # 获取用户已购买的产品信息
        profile_orders = await order_service.get_purchased_reports(user_id, profile_id)
        profiles_with_products = []
        for profile in profiles:
            purchased_products = []

            # 获取该profile的所有订单和产品
            if profile.profile_id in profile_orders:
                for item in profile_orders[profile.profile_id]:
                    order = item.order
                    product = item.product
                    if order.order_status is not None:
                        # 计算解锁进度
                        unlock_progress = 0
                        if order.order_status in [
                            OrderStatus.PURCHASED.value,
                            OrderStatus.PROCESSING.value,
                        ]:
                            # 计算订单更新时间到现在的时间差（秒）
                            time_diff = (
                                datetime.now() - order.updated_at
                            ).total_seconds()
                            # 预计解锁时间为10分钟（600秒）
                            unlock_progress = min(int(time_diff / 600 * 100), 99)
                        elif order.order_status in ["SUCCESS"]:
                            # 已完成订单，进度为100%
                            unlock_progress = 100

                        purchased_products.append(
                            PurchasedProductResponse(
                                product_id=product.product_id,
                                apple_product_id=product.apple_product_id,
                                order_status=order.order_status,
                                unlock_progress=unlock_progress,
                            )
                        )

            # 创建包含已购买产品的个人资料响应
            profile_with_products = ProfileWithProductsResponse(
                profile_id=profile.profile_id,
                user_id=profile.user_id,
                birth_year=profile.birth_year,
                birth_month=profile.birth_month,
                birth_day=profile.birth_day,
                birth_hour=profile.birth_hour,
                birth_minutes=profile.birth_minutes,
                gender=profile.gender,
                name=profile.name,
                role_group=getattr(profile, "role_group", "other"),
                created_at=profile.created_at,
                updated_at=profile.updated_at,
                purchased_products=purchased_products,
            )

            profiles_with_products.append(profile_with_products)

        # 直接返回UserInfoResponse对象
        return UserInfoResponse(
            user_id=user.user_id,
            account_id=user.account_id,
            username=user.username,
            profiles=profiles_with_products,
        )

    async def create_profile(
        self, profile: ProfileRequest, user_id: str
    ) -> ProfileResponse:
        """
        Create a profile for a user
        """
        profile_schema = UserProfile(
            profile_id=str(uuid.uuid4()),
            user_id=user_id,
            **profile.model_dump(),
        )
        profile_schema_result = await self.user_client.create_profile(profile_schema)
        return ProfileResponse.model_validate(profile_schema_result)

    async def get_profiles_by_profile_params(
        self, profile_params: ProfileParams
    ) -> List[ProfileResponse]:
        """
        Get a profile by profile_id
        """
        profile_schema = UserProfile(**profile_params.model_dump())
        profiles: List[UserProfile] = (
            await self.user_client.get_profile_by_profile_params(profile_schema)
        )
        return [ProfileResponse.model_validate(profile) for profile in profiles]

    async def delete_profile(self, profile_id: str, user_id: str) -> None:
        """
        Delete a profile for a user
        """
        await self.user_client.delete_profile(profile_id, user_id)

    async def delete_profiles(self, profile_ids: List[str], user_id: str) -> None:
        """Batch delete profiles for a user"""
        await self.user_client.delete_profiles(profile_ids, user_id)

    async def update_user(
        self, user_id: str, user_update: UserUpdateRequest
    ) -> Optional[User]:
        """
        Update user information
        """
        user = await self.user_client.get_by_user_id(user_id)
        if not user:
            return None

        update_data = user_update.model_dump(exclude_unset=True)
        if not update_data:
            # No fields to update
            return user

        for field, value in update_data.items():
            setattr(user, field, value)

        user.updated_at = datetime.utcnow()  # Ensure updated_at is set
        return await self.user_client.update_user(user)

    async def delete_user(self, user_id: str) -> None:
        """
        Delete a user and all associated data
        """
        async with get_session() as session:
            # Check if user exists
            user = await self.user_client.get_by_user_id_with_session(session, user_id)
            if not user:
                logger.error(f"User {user_id} not found")
                return
            # Then delete the user
            user.is_active = 0
            logger.info(f"Successfully deactivated user {user_id}")

    async def update_profile(
        self, profile_id: str, user_id: str, update_req: ProfileUpdateRequest
    ) -> Optional[UserProfile]:
        """
        更新个人资料的name和role_group
        """
        return await self.user_client.update_profile(
            profile_id=profile_id,
            user_id=user_id,
            name=update_req.name,
            role_group=update_req.role_group,
        )

    async def get_privacy_policy(self) -> dict:
        """
        获取隐私政策(markdown格式)
        """
        try:
            async with aiofiles.open(
                PRIVACY_POLICY_PATH, mode="r", encoding="utf-8"
            ) as f:
                privacy_policy = await f.read()
            return {"kind": "ok", "data": privacy_policy}
        except FileNotFoundError:
            raise HTTPException(status_code=404, detail="Privacy policy not found")
        except Exception:
            # Log the exception e
            raise HTTPException(status_code=500, detail="Error reading privacy policy")

    async def get_user_agreement(self) -> dict:
        """
        获取用户协议(markdown格式)
        """
        try:
            async with aiofiles.open(
                USER_AGREEMENT_PATH, mode="r", encoding="utf-8"
            ) as f:
                user_agreement = await f.read()
            return {"kind": "ok", "data": user_agreement}
        except FileNotFoundError:
            raise HTTPException(status_code=404, detail="User agreement not found")
        except Exception:
            # Log the exception e
            raise HTTPException(status_code=500, detail="Error reading user agreement")

    async def get_user_by_account_id(self, account_id: str) -> Optional[User]:
        """
        通过account_id获取用户
        """
        return await self.user_client.get_by_account_id(account_id)

    async def get_user_balance(self, user_id: str) -> float:
        user_wallet = await user_wallet_client.get_by_user_id(user_id)
        if not user_wallet:
            logger.error(f"User wallet not found for user {user_id}")
            raise Exception("User wallet not found")
        return user_wallet.balance

    async def activate_user(self, user_id: str) -> None:
        """
        Activate a user
        """
        user = await self.user_client.get_by_user_id(user_id)
        if not user:
            return
        user.is_active = 1
        await self.user_client.update_user(user)

    async def init_user_wallet(self, user_id: str) -> None:
        """
        Init user wallet
        """
        async with get_session() as session:
            user_wallet = await user_wallet_client.get_user_wallet_with_lock(
                session, user_id
            )
            if not user_wallet:
                user_wallet = UserWallet(
                    user_id=user_id,
                    balance=0,
                    wallet_id=str(uuid.uuid4()),
                )
                session.add(user_wallet)

    async def give_yuxu_coin(
        self, request: GiveYuxuCoinRequest
    ) -> GiveYuxuCoinResponse:
        """
        赠送玉虚币给指定用户

        Args:
            request: 赠送请求，包含用户ID、金额和描述

        Returns:
            GiveYuxuCoinResponse: 赠送结果
        """
        # 验证用户是否存在
        user = await self.get_user_by_id(request.user_id)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        async with get_session() as session:
            # 使用新的方法更新钱包余额并创建交易记录
            user_wallet, yuxu_coin_transaction = (
                await user_wallet_client.update_wallet_balance_with_transaction(
                    session=session,
                    user_id=request.user_id,
                    amount=request.amount,
                    transaction_type=YuxuCoinTransactionType.GIVE.value,
                    related_entity_type=YuxuCoinTransactionRelatedEntityType.OTHER.value,
                    related_entity_id=None,
                    description=request.description
                    or f"管理员赠送 {request.amount} 玉虚币",
                )
            )

            logger.info(
                f"Successfully gave {request.amount} yuxu coins to user {request.user_id}. "
                f"Balance: {yuxu_coin_transaction.balance_before} -> {yuxu_coin_transaction.balance_after}"
            )

            return GiveYuxuCoinResponse(
                success=True,
                message=f"成功赠送 {request.amount} 玉虚币给用户 {user.username}",
                data=GiveYuxuCoinData(
                    user_id=request.user_id,
                    username=user.username,
                    amount=request.amount,
                    balance_before=yuxu_coin_transaction.balance_before,
                    balance_after=yuxu_coin_transaction.balance_after,
                    transaction_id=yuxu_coin_transaction.transaction_log_id,
                ),
            )


# Create singleton instance
user_service = UserService()
