from src.client.database.product_client import product_client
from src.models.product_model import ProductGroup, ProductResponse, CSVImportResponse
from typing import Optional, List
import csv
import io
from src.models.database.products import Product
from datetime import datetime


class ProductService:
    def __init__(self) -> None:
        self.product_client = product_client

    async def get_product_by_product_id(
        self, product_id: str
    ) -> Optional[ProductResponse]:
        product = await self.product_client.get_active_product_by_product_id(product_id)
        if product is None:
            return None
        return ProductResponse.model_validate(product)

    async def get_all_products(self) -> List[ProductResponse]:
        """Get all products"""
        products = await self.product_client.get_all_products()
        return [ProductResponse.model_validate(product) for product in products]

    async def export_products_to_csv(self) -> str:
        """Export all products to CSV format and return as string"""
        products = await self.product_client.get_all_products()

        # Define CSV headers based on Product model
        headers = [
            "product_id",
            "apple_product_id",
            "product_name",
            "description",
            "product_type",
            "product_group",
            "payment_method",
            "price",
            "currency",
            "localized_title",
            "localized_desc",
            "is_active",
            "sale_type",
            "created_at",
            "updated_at",
        ]

        # Create CSV content
        output = io.StringIO()
        writer = csv.writer(output)

        # Write headers
        writer.writerow(headers)

        # Write product data
        for product in products:
            row = [
                product.product_id,
                product.apple_product_id or "",
                product.product_name,
                product.description or "",
                product.product_type,
                product.product_group,
                product.payment_method,
                str(product.price),
                product.currency,
                product.localized_title,
                product.localized_desc,
                str(product.is_active),
                str(product.sale_type),
                product.created_at.isoformat() if product.created_at else "",
                product.updated_at.isoformat() if product.updated_at else "",
            ]
            writer.writerow(row)

        return output.getvalue()

    async def import_products_from_csv(self, csv_content: str) -> CSVImportResponse:
        """Import products from CSV content and return summary"""
        try:
            # Parse CSV content
            csv_reader = csv.DictReader(io.StringIO(csv_content))

            created_count = 0
            updated_count = 0
            errors = []

            for row_num, row in enumerate(
                csv_reader, start=2
            ):  # Start from 2 because row 1 is header
                try:
                    # Validate required fields
                    if not row.get("product_id") or not row.get("product_name"):
                        errors.append(
                            f"Row {row_num}: Missing required fields (product_id or product_name)"
                        )
                        continue

                    # Check if product exists
                    existing_product = (
                        await self.product_client.get_active_product_by_product_id(
                            row["product_id"]
                        )
                    )

                    # Prepare update data
                    update_data = {}

                    # Process each field
                    if row.get("apple_product_id"):
                        update_data["apple_product_id"] = row["apple_product_id"]
                    if row.get("product_name"):
                        update_data["product_name"] = row["product_name"]
                    if row.get("description"):
                        update_data["description"] = row["description"]
                    if row.get("product_type"):
                        # Validate product_type enum
                        if row["product_type"] not in [
                            "consumable",
                            "non_consumable",
                            "subscription",
                        ]:
                            errors.append(
                                f"Row {row_num}: Invalid product_type '{row['product_type']}'"
                            )
                            continue
                        update_data["product_type"] = row["product_type"]
                    if row.get("product_group"):
                        update_data["product_group"] = row["product_group"]
                    if row.get("payment_method"):
                        update_data["payment_method"] = row["payment_method"]
                    if row.get("price"):
                        try:
                            update_data["price"] = float(row["price"])
                        except ValueError:
                            errors.append(
                                f"Row {row_num}: Invalid price '{row['price']}'"
                            )
                            continue
                    if row.get("currency"):
                        update_data["currency"] = row["currency"]
                    if row.get("localized_title"):
                        update_data["localized_title"] = row["localized_title"]
                    if row.get("localized_desc"):
                        update_data["localized_desc"] = row["localized_desc"]
                    if row.get("is_active"):
                        try:
                            update_data["is_active"] = int(row["is_active"])
                        except ValueError:
                            errors.append(
                                f"Row {row_num}: Invalid is_active '{row['is_active']}'"
                            )
                            continue
                    if row.get("sale_type"):
                        try:
                            update_data["sale_type"] = int(row["sale_type"])
                        except ValueError:
                            errors.append(
                                f"Row {row_num}: Invalid sale_type '{row['sale_type']}'"
                            )
                            continue

                    # Set updated_at to current time
                    update_data["updated_at"] = datetime.now()

                    if existing_product:
                        # Update existing product
                        success = (
                            await self.product_client.update_product_by_product_id(
                                row["product_id"], update_data
                            )
                        )
                        if success:
                            updated_count += 1
                        else:
                            errors.append(
                                f"Row {row_num}: Failed to update product {row['product_id']}"
                            )
                    else:
                        # Create new product
                        try:
                            # Create Product instance
                            product = Product(
                                product_id=row["product_id"],
                                apple_product_id=row.get("apple_product_id") or "",
                                product_name=row["product_name"],
                                description=row.get("description"),
                                product_type=row.get("product_type", "consumable"),
                                product_group=row.get("product_group", "REPORT"),
                                payment_method=row.get("payment_method", ""),
                                price=float(row.get("price", 0)),
                                currency=row.get("currency", "CNY"),
                                localized_title=row.get(
                                    "localized_title", row["product_name"]
                                ),
                                localized_desc=row.get("localized_desc", ""),
                                is_active=int(row.get("is_active", 1)),
                                sale_type=int(row.get("sale_type", 0)),
                                created_at=datetime.now(),
                                updated_at=datetime.now(),
                            )

                            await self.product_client.create_product(product)
                            created_count += 1
                        except Exception as e:
                            errors.append(
                                f"Row {row_num}: Failed to create product - {str(e)}"
                            )

                except Exception as e:
                    errors.append(f"Row {row_num}: Unexpected error - {str(e)}")

            return CSVImportResponse(
                success=True,
                message=f"Successfully processed {created_count + updated_count} products. Created: {created_count}, Updated: {updated_count}"
                + (f". {len(errors)} errors occurred." if errors else ""),
                created_count=created_count,
                updated_count=updated_count,
                errors=errors,
                total_processed=created_count + updated_count,
            )

        except Exception as e:
            return CSVImportResponse(
                success=False,
                message=f"Failed to parse CSV: {str(e)}",
                created_count=0,
                updated_count=0,
                total_processed=0,
                errors=[],
            )

    async def get_products(
        self, product_group: Optional[ProductGroup] = None
    ) -> List[ProductResponse]:
        """Get all products"""
        products = await self.product_client.get_all_products(product_group)
        return [ProductResponse.model_validate(product) for product in products]


product_service = ProductService()
