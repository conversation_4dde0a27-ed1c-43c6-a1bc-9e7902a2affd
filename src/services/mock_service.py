import json
import asyncio
from typing import AsyncGenerator, Optional, List
from pathlib import Path
from uuid import uuid4


class MockDataService:
    """Mock数据服务，支持根据用户问题返回相应的mock数据流"""

    def __init__(self) -> None:
        self.mock_data_dir = Path(__file__).parent.parent / "chat" / "mock"
        # 定义问题到mock文件的映射
        self.question_mock_mapping = {
            "生成我的生辰排盘报告": "eight_char_mock.txt",
            # 可以在这里添加更多的映射关系
            # "生成我的运势报告": "fortune_mock.txt",
            # "生成我的塔罗牌占卜": "tarot_mock.txt",
        }

    def should_use_mock(self, messages: List[dict]) -> Optional[str]:
        """
        检查是否应该使用mock数据

        Args:
            messages: 聊天消息列表

        Returns:
            如果应该使用mock数据，返回对应的mock文件名，否则返回None
        """
        if not messages:
            return None

        # 获取最后一条用户消息
        last_user_message = None
        for message in reversed(messages):
            if message["role"] == "user":
                last_user_message = message
                break

        if not last_user_message:
            return None

        # 检查是否匹配任何mock问题
        user_content = str(last_user_message["content"]).strip()
        for question, mock_file in self.question_mock_mapping.items():
            if question in user_content:
                return mock_file

        return None

    async def stream_mock_data(
        self, mock_file: str, thread_id: str, delay_between_chunks: float = 0.0
    ) -> AsyncGenerator[str, None]:
        """
        流式输出mock数据

        Args:
            mock_file: mock文件名
            thread_id: 线程ID
            delay_between_chunks: 数据块之间的延迟(秒)

        Yields:
            SSE格式的字符串
        """
        mock_file_path = self.mock_data_dir / mock_file

        if not mock_file_path.exists():
            # 如果mock文件不存在，返回错误消息
            error_data = {
                "thread_id": thread_id,
                "agent": "system",
                "id": str(uuid4()),
                "role": "assistant",
                "content": f"Mock文件 {mock_file} 不存在",
                "finish_reason": "error",
            }
            yield f"event: message_chunk\ndata: {json.dumps(error_data, ensure_ascii=False)}\n\n"
            return

        try:
            with open(mock_file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 解析SSE格式的数据
            events = self._parse_sse_content(content)

            for event in events:
                # 更新thread_id为当前的thread_id
                updated_event = self._update_thread_id(event, thread_id)
                yield updated_event

                # 添加延迟以模拟真实的流式输出
                await asyncio.sleep(delay_between_chunks)

        except Exception as e:
            # 发送错误信息
            error_data = {
                "thread_id": thread_id,
                "agent": "system",
                "id": str(uuid4()),
                "role": "assistant",
                "content": f"读取mock数据时发生错误: {str(e)}",
                "finish_reason": "error",
            }
            yield f"event: message_chunk\ndata: {json.dumps(error_data, ensure_ascii=False)}\n\n"

    def _parse_sse_content(self, content: str) -> List[str]:
        """
        解析SSE格式的内容

        Args:
            content: 原始SSE内容

        Returns:
            SSE事件字符串列表
        """
        events = []
        lines = content.strip().split("\n")
        current_event: List[str] = []

        for line in lines:
            if line.strip() == "":
                if current_event:
                    events.append("\n".join(current_event) + "\n\n")
                    current_event = []
            else:
                current_event.append(line)

        # 处理最后一个事件
        if current_event:
            events.append("\n".join(current_event) + "\n\n")

        return events

    def _update_thread_id(self, event_str: str, new_thread_id: str) -> str:
        """
        更新事件中的thread_id

        Args:
            event_str: 原始事件字符串
            new_thread_id: 新的thread_id

        Returns:
            更新后的事件字符串
        """
        lines = event_str.strip().split("\n")
        updated_lines = []

        for line in lines:
            if line.startswith("data: "):
                try:
                    data_content = line[6:]  # 去掉 'data: ' 前缀
                    data = json.loads(data_content)
                    data["thread_id"] = new_thread_id
                    updated_lines.append(
                        f"data: {json.dumps(data, ensure_ascii=False)}"
                    )
                except json.JSONDecodeError:
                    # 如果解析失败，保持原样
                    updated_lines.append(line)
            else:
                updated_lines.append(line)

        return "\n".join(updated_lines) + "\n\n"
