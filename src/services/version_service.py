import json
import os
from typing import Dict, Any
from packaging import version
from src.models.version_model import (
    VersionCheckRequest,
    VersionCheckData,
    UpdatePolicy,
)
from src.utils.log_util import logger


class VersionService:
    """Service for handling version checking logic"""

    def __init__(self, config_path: str = "data/version_config.json"):
        self.config_path = config_path
        self.config: Dict[str, Any] = {}
        self.load_config()

    def load_config(self) -> None:
        """Load version configuration from JSON file"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, "r", encoding="utf-8") as f:
                    self.config = json.load(f)
                logger.info(f"Version config loaded from {self.config_path}")
            else:
                logger.warning(f"Version config file not found: {self.config_path}")
                self.config = {"platforms": {}}
        except Exception as e:
            logger.error(f"Error loading version config: {e}")
            self.config = {"platforms": {}}

    def reload_config(self) -> None:
        """Reload configuration from file"""
        self.load_config()

    def compare_versions(self, current: str, minimum_required_version: str) -> bool:
        """
        Compare two version strings
        Returns True if current version is <= max_version
        """
        try:
            return version.parse(current) < version.parse(minimum_required_version)
        except Exception as e:
            logger.error(
                f"Error comparing versions {current} and {minimum_required_version}: {e}"
            )
            return False

    def get_localized_text(self, text_dict: Dict[str, str], language: str) -> str:
        """Get localized text based on language preference"""
        if not text_dict:
            return ""

        # Try exact language match first
        if language in text_dict:
            return text_dict[language]

        # Try language without region (e.g., 'zh' from 'zh-CN')
        lang_code = language.split("-")[0] if "-" in language else language
        for key in text_dict:
            if key.startswith(lang_code):
                return text_dict[key]

        # Fallback to first available language
        return next(iter(text_dict.values()), "")

    def check_version(self, request: VersionCheckRequest) -> VersionCheckData:
        """
        Check version and return update policy
        """
        platform = request.platform.value
        app_version = request.appVersion
        language = request.language or "zh-CN"

        # Get platform configuration
        platform_config = self.config.get("platforms", {}).get(platform)
        if not platform_config:
            logger.warning(f"Platform {platform} not found in config")
            return VersionCheckData(
                policy=UpdatePolicy.NO_UPDATE,
                latestVersion=None,
                updateUrl=None,
                title=None,
                description=None,
                extra={},
            )

        latest_version = platform_config.get("latestVersion")
        update_url = platform_config.get("updateUrl")
        rules = platform_config.get("rules", [])

        # Check rules to determine update policy
        for rule in rules:
            minimum_required_version = rule.get("minimumRequiredVersion")
            if minimum_required_version and self.compare_versions(
                app_version, minimum_required_version
            ):
                policy = rule.get("policy", UpdatePolicy.NO_UPDATE)
                title_dict = rule.get("title", {})
                description_dict = rule.get("description", {})

                title = self.get_localized_text(title_dict, language)
                description = self.get_localized_text(description_dict, language)

                return VersionCheckData(
                    policy=UpdatePolicy(policy),
                    latestVersion=latest_version,
                    updateUrl=update_url if policy > UpdatePolicy.NO_UPDATE else None,
                    title=title if policy > UpdatePolicy.NO_UPDATE else None,
                    description=(
                        description if policy > UpdatePolicy.NO_UPDATE else None
                    ),
                    extra={},
                )

        # No rules matched, no update needed
        return VersionCheckData(
            policy=UpdatePolicy.NO_UPDATE,
            latestVersion=latest_version,
            updateUrl=None,
            title=None,
            description=None,
            extra={},
        )


# Create singleton instance
version_service = VersionService()
