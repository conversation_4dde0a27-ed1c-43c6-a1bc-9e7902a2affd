from datetime import datetime
import uuid
from typing import Any

from src.client.apple.apple_client import apple_client
from src.models.database.products import Product
from src.client.database.order_client import order_client
from src.client.database.product_client import product_client

from src.utils import time_utils
from src.utils.log_util import logger
from src.client.apple.apple_utils import (
    to_transaction,
    extract_apple_transaction_payload,
)
from src.models.database.orders import Order, Transaction, PURCHASABLE_ORDER_STATUSES
from src.common.session import get_order_session
from src.utils.constants import OrderStatus
from typing import Optional


class ApplePurchaseService:
    """苹果购买服务类，专门处理苹果相关的购买验证"""

    async def callback_verify(self, signed_payload: str) -> Optional[Order]:
        """
        验证苹果购买回调

        Args:
            signed_payload: 苹果发送的签名载荷
        """

        logger.info(
            f"Starting Apple purchase verification, Signed payload: {signed_payload}"
        )
        # 提取交易载荷数据
        payload = extract_apple_transaction_payload(signed_payload)
        logger.info(f"Decoded payload: {payload}")

        order_id = payload.get("appAccountToken")
        if not order_id:
            logger.warn("No order ID found in transaction payload")
            return None

        # 将载荷数据转换为Transaction实例
        transaction = to_transaction(payload, order_id)
        if transaction.transaction_type != "purchase":
            logger.info(
                f"order_id: {order_id}, transaction_id: {transaction.transaction_id} type is not PURCHASE: {transaction.transaction_type}"
            )
            return None

        # 使用事务处理器进行操作
        async with get_order_session(
            "Apple callback purchase verification", order_id
        ) as session:
            # 查询订单并锁定
            order = await order_client.get_order_with_lock(session, order_id)
            if not order:
                logger.error(
                    f"order_id: {order_id}, transaction_id: {transaction.transaction_id} order not found"
                )
                return None

            # 验证订单状态
            if order.status not in PURCHASABLE_ORDER_STATUSES:
                logger.info(
                    f"order_id: {order_id}, transaction_id: {transaction.transaction_id} "
                    f"order already in status {order.status}, skipping verification"
                )
                return order

            # 保存交易记录并更新订单
            session.add(transaction)
            self._apply_transaction_to_order(order, transaction)

            # 确保修改后的订单对象被添加到session中进行持久化
            session.add(order)
            logger.info(
                f"Transaction and order verification completed successfully: transaction_id={transaction.transaction_id}, order_id={order_id}"
            )
            return order

    def _apply_transaction_to_order(
        self, order: Order, transaction: Transaction
    ) -> None:
        """
        将交易信息应用到订单中

        Args:
            order: 订单对象
            transaction: 交易对象
        """
        order.status = OrderStatus.PURCHASED.value
        order.total_price = transaction.price
        order.currency = transaction.currency
        order.environment = transaction.environment
        order.single_price = transaction.price
        order.amount = 1

    @time_utils.timing()
    async def _get_or_mock_apple_transaction(
        self, transaction_id: str, product: Product
    ) -> Any:
        """
        获取苹果交易信息，如果交易ID为mock_transaction_id，则返回模拟交易信息

        Args:
            transaction_id: 交易ID
            product: 产品对象

        Returns:
            dict: 验证后的交易信息

        Raises:
            Exception: 当交易验证失败时
        """
        # 处理模拟交易
        if transaction_id == "mock_transaction_id":
            transaction_id = "mock_" + uuid.uuid4().hex
            return {
                "productId": product.apple_product_id,
                "transactionId": transaction_id,
                "originalTransactionId": transaction_id,
                "purchaseDate": int(datetime.now().timestamp() * 1000),
                "environment": "Sandbox",
                "price": 0,
                "currency": "CNY",
            }

        # 获取真实交易信息
        apple_transaction = await apple_client.get_transaction(transaction_id)
        if not apple_transaction:
            raise Exception("交易不存在")

        # 验证交易信息
        if apple_transaction.get("productId") != product.apple_product_id:
            raise Exception("交易信息验证失败")

        logger.info(f"Apple transaction verified: {apple_transaction}")
        return apple_transaction

    @time_utils.timing()
    async def verify(
        self, order_id: str, transaction_id: str, product_id: str, user_id: str
    ) -> Order:
        """
        验证苹果购买，前端调用

        Args:
            order_id: 订单ID
            transaction_id: 交易ID
            product_id: 产品ID
            user_id: 用户ID

        Returns:
            Order: 验证后的订单对象
        """
        logger.info(
            f"verify_purchase, order_id: {order_id}, transaction_id: {transaction_id}, product_id: {product_id}, user_id: {user_id}"
        )

        # 获取产品信息
        product = await product_client.get_product_by_product_id(product_id)
        if not product:
            raise Exception("产品不存在")

        if not product.apple_product_id:
            raise Exception("非苹果产品")

        # 验证苹果交易
        apple_transaction = await self._get_or_mock_apple_transaction(
            transaction_id, product
        )

        async with get_order_session(
            "Apple purchase verification", order_id
        ) as session:
            # 查询订单并验证
            order = await order_client.get_order_with_lock(session, order_id, user_id)
            if not order:
                raise Exception("订单不存在")

            # 验证订单状态
            if order.status not in PURCHASABLE_ORDER_STATUSES:
                logger.info(
                    f"order_id: {order_id}, transaction_id: {transaction_id} order already in status {order.status}, skipping verification"
                )
                return order

            # 创建交易记录
            transaction = to_transaction(apple_transaction, order_id)
            session.add(transaction)

            order.status = OrderStatus.PURCHASED.value
            order.total_price = transaction.price
            order.currency = transaction.currency
            order.environment = transaction.environment
            order.single_price = transaction.price
            order.amount = 1
            # 确保修改后的订单对象被添加到session中进行持久化
            session.add(order)
            return order


# 创建服务实例
apple_purchase_service = ApplePurchaseService()
