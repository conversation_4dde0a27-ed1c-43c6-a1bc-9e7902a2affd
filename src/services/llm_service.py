import httpx
import time
import uuid
from src.config import settings
from src.models.chat_model import ChatRequest, ChatResponse


class LLMService:
    async def get_completion(self, request: ChatRequest) -> ChatResponse:
        if request.provider.lower() == "deepseek":
            return await self._call_deepseek(request)
        raise ValueError(f"Unsupported provider: {request.provider}")

    async def _call_deepseek(self, request: ChatRequest) -> ChatResponse:
        async with httpx.AsyncClient() as client:
            headers = {
                "Authorization": f"Bearer {settings.DEEPSEEK_API_KEY}",
                "Content-Type": "application/json",
            }

            payload = {
                "messages": [msg.dict() for msg in request.messages],
                "temperature": request.temperature,
                "max_tokens": request.max_tokens,
            }

            response = await client.post(
                f"{settings.DEEPSEEK_API_BASE}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30.0,
            )

            if response.status_code != 200:
                raise Exception(f"DeepSeek API error: {response.text}")

            data = response.json()

            return ChatResponse(
                id=str(uuid.uuid4()),
                choices=data["choices"],
                created=int(time.time()),
                model=data.get("model", "deepseek"),
            )
