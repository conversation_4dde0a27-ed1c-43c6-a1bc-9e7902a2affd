from src.client.database.order_client import order_client
from src.client.database.user_wallet_client import user_wallet_client
from src.common.session import get_order_session
from src.models.database import Order, Product
from src.models.database.yuxucoin_transaction import (
    YuxuCoinTransactionRelatedEntityType,
    YuxuCoinTransactionType,
)
from src.utils.constants import OrderStatus


class YuxuCoinService:
    async def deliver_yuxu_coin(self, order: Order, product: Product) -> None:
        async with get_order_session(
            operation_name="deliver_yuxu_coin",
            order_id=order.order_id,
            error_order_status=OrderStatus.FAILED,
        ) as session:
            # 加锁
            order_id = order.order_id
            lock_order = await order_client.get_order_with_lock(session, order_id)
            if not lock_order:
                raise ValueError(f"Order with id {order_id} not found")

            # 使用新的方法更新钱包余额并创建交易记录
            await user_wallet_client.update_wallet_balance_with_transaction(
                session=session,
                user_id=lock_order.user_id,
                amount=product.price,
                transaction_type=YuxuCoinTransactionType.RECHARGE.value,
                related_entity_type=YuxuCoinTransactionRelatedEntityType.ORDER.value,
                related_entity_id=lock_order.order_id,
                description=f"recharge {product.price} yuxu coin by order {lock_order.order_id}",
            )
            lock_order.status = OrderStatus.SUCCESS.value


yuxu_coin_service = YuxuCoinService()
