import json
from typing import Optional, List
from uuid import uuid4
from src.utils.log_util import logger
from src.client.database.activity_client import activity_client
from src.client.database.user_wallet_client import user_wallet_client
from src.common.session import get_session
from src.models.database.activity import ActivityType
from src.models.database.yuxucoin_transaction import (
    YuxuCoinTransaction,
    YuxuCoinTransactionRelatedEntityType,
    YuxuCoinTransactionType,
)
from src.models.database.activity_record import ActivityRecord
from src.models.activity_model import ActivityResponse, ActivityListResponse
from src.common.pagination import PaginationHelper


class ActivityService:

    async def give_first_register_gift(self, user_id: str) -> Optional[int]:
        activity = await activity_client.get_activity_by_type(
            ActivityType.FIRST_REGISTER_GIFT
        )
        if not activity:
            logger.info("First register gift activity not found")
            return None
        # 现阶段写死为发放玉虚币
        yuxu_coin_reward = int(activity.rewards.get("YUXU_COIN", 0))
        if yuxu_coin_reward <= 0:
            logger.info("First register gift activity has no yuxu coin reward")
            return None

        # 发放玉虚币到用户钱包
        async with get_session() as session:
            user_wallet = await user_wallet_client.get_user_wallet_with_lock(
                session=session, user_id=user_id
            )
            if not user_wallet:
                logger.info("User wallet not found")
                return None

            yuxu_coin_transaction = YuxuCoinTransaction(
                user_id=user_id,
                transaction_log_id=uuid4(),
                amount=yuxu_coin_reward,
                transaction_type=YuxuCoinTransactionType.GIVE.value,
                wallet_id=user_wallet.wallet_id,
                balance_before=user_wallet.balance,
                balance_after=user_wallet.balance + yuxu_coin_reward,
                related_entity_type=YuxuCoinTransactionRelatedEntityType.ACTIVITY.value,
                related_entity_id=activity.activity_id,
                description=f"First register gift activity has been given to user {user_id}",
            )
            user_wallet.balance += yuxu_coin_reward
            activity_record = ActivityRecord(
                user_id=user_id,
                user_activity_id=uuid4(),
                activity_id=activity.activity_id,
                status="SUCCESS",
                reward_details=json.dumps(activity.rewards),
                notes=f"First register gift activity has been given to user {user_id}",
            )
            session.add(activity_record)
            session.add(user_wallet)
            session.add(yuxu_coin_transaction)
            await session.commit()
            logger.info(
                f"First register gift yuxu_coin {yuxu_coin_reward} activity has been given to user {user_id}"
            )
            return yuxu_coin_reward

    async def get_user_activities(
        self,
        user_id: str,
        activity_status: Optional[str] = None,
        page: int = 1,
        limit: int = 20
    ) -> ActivityListResponse:
        """
        Get user activities with participation status

        Args:
            user_id: User ID to get activities for
            activity_status: Optional filter by activity status
            page: Page number (1-based)
            limit: Items per page

        Returns:
            ActivityListResponse with paginated activity data
        """
        try:
            # Get activities with user records from database
            activities_with_records, total = await activity_client.get_activities_with_user_records(
                user_id=user_id,
                activity_status=activity_status,
                page=page,
                limit=limit
            )

            # Convert to response models
            activity_responses = []
            for activity, activity_record in activities_with_records:
                # Convert rules and rewards to JSON strings if they are dicts
                rules_str = None
                if activity.rules:
                    rules_str = json.dumps(activity.rules) if isinstance(activity.rules, dict) else str(activity.rules)

                rewards_str = json.dumps(activity.rewards) if isinstance(activity.rewards, dict) else str(activity.rewards)

                activity_response = ActivityResponse(
                    # Activity fields
                    activity_id=activity.activity_id,
                    activity_name=activity.activity_name,
                    description=activity.description,
                    activity_type=activity.activity_type,
                    activity_start_time=activity.start_time,
                    activity_end_time=activity.end_time,
                    activity_status=activity.status,
                    rules=rules_str,
                    rewards=rewards_str,

                    # Activity record fields (if user participated)
                    user_id=activity_record.user_id if activity_record else None,
                    user_activity_id=activity_record.user_activity_id if activity_record else None,
                    activity_record_status=activity_record.status if activity_record else None,
                    reward_details=activity_record.reward_details if activity_record else None,
                    notes=activity_record.notes if activity_record else None,
                    record_created_at=activity_record.created_at if activity_record else None,
                    record_updated_at=activity_record.updated_at if activity_record else None,
                )
                activity_responses.append(activity_response)

            # Create pagination response
            pagination = PaginationHelper.create_pagination_response(total, page, limit)

            return ActivityListResponse(
                data=activity_responses,
                pagination=pagination
            )

        except Exception as e:
            logger.error(f"Error getting user activities for user {user_id}: {e}", exc_info=True)
            raise


activity_service = ActivityService()
