import os
from dotenv import load_dotenv
from typing import Dict, Any
from arq.connections import RedisSettings

# Load the appropriate .env file based on environment
env = os.getenv("ENV", "dev").lower()
env_files = {"dev": ".env.dev", "staging": ".env.staging", "prod": ".env.prod"}

env_file = env_files.get(env, ".env.dev")
load_dotenv(env_file)


def get_required_env(key: str) -> str:
    """获取必需的环境变量"""
    value = os.getenv(key)
    if value is None:
        raise ValueError(f"Environment variable {key} is required but not set")
    return value


class ARQConfig:
    """ARQ配置类，支持多环境配置"""

    def __init__(self, env: str = "dev"):
        self.env = env.lower()
        self.redis_settings = self._get_redis_settings()
        self.worker_settings = self._get_worker_settings()

    def _get_redis_settings(self) -> RedisSettings:
        """获取Redis连接配置"""
        # 从环境变量读取Redis配置
        redis_host = get_required_env("REDIS_HOST")
        redis_port = int(get_required_env("REDIS_PORT"))
        redis_password = os.getenv("REDIS_PASSWORD", "")
        redis_broker_db = int(get_required_env("REDIS_BROKER_DB"))

        return RedisSettings(
            host=redis_host,
            port=redis_port,
            password=redis_password if redis_password else None,
            database=redis_broker_db,
            conn_timeout=5,
            conn_retries=5,
            conn_retry_delay=1,
        )

    def _get_worker_settings(self) -> Dict[str, Any]:
        """获取Worker配置"""
        # 从环境变量读取Worker配置
        queue_name = get_required_env("ARQ_QUEUE_NAME")  # 复用现有环境变量
        worker_concurrency = int(get_required_env("ARQ_WORKER_CONCURRENCY"))

        return {
            "queue_name": queue_name,
            "max_jobs": worker_concurrency,
            "job_timeout": 86400,  # 默认24小时超时
            "keep_result": 86400 * 3,  # 结果保留72小时 (增加保留时间)
            "keep_result_forever": False,
            "poll_delay": 0.5,
            "max_tries": 5,  # 默认最大重试次数
            "health_check_interval": 3600,
            "retry_jobs": True,
            "allow_abort_jobs": True,
        }

    def get_redis_settings(self) -> RedisSettings:
        """获取Redis设置"""
        return self.redis_settings

    def get_worker_settings(self) -> Dict[str, Any]:
        """获取Worker设置"""
        return self.worker_settings

    def get_queue_name(self) -> str:
        """获取队列名称"""
        return str(self.worker_settings["queue_name"])


# 创建ARQ配置实例
arq_config = ARQConfig(env)
