import os
from dotenv import load_dotenv
from typing import Literal, Any, Dict

# Load the appropriate .env file based on environment
env = os.getenv("ENV", "dev").lower()
env_files = {"dev": ".env.dev", "staging": ".env.staging", "prod": ".env.prod"}

env_file = env_files.get(env, ".env.dev")
load_dotenv(env_file)
LLMType = Literal["basic", "reasoning", "vision"]


def get_required_env(key: str) -> str:
    value = os.getenv(key)
    if value is None:
        raise ValueError(f"Environment variable {key} is required but not set")
    return value


class Settings:
    ENV = env
    DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
    DEEPSEEK_API_BASE = os.getenv("DEEPSEEK_API_BASE", "https://api.deepseek.com/v1")

    DB_HOST = get_required_env("DB_HOST")
    DB_PORT = get_required_env("DB_PORT")
    DB_USER = get_required_env("DB_USER")
    DB_PASSWORD = get_required_env("DB_PASSWORD")
    DB_NAME = get_required_env("DB_NAME")

    # JWT Settings
    JWT_SECRET_KEY = get_required_env("JWT_SECRET_KEY")
    JWT_ALGORITHM = get_required_env("JWT_ALGORITHM")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES = int(
        get_required_env("JWT_ACCESS_TOKEN_EXPIRE_MINUTES")
    )

    KONGWANG_AGENT_ID = get_required_env("KONGWANG_AGENT_ID")
    YONGSHEN_AGENT_ID = get_required_env("YONGSHEN_AGENT_ID")
    SHISHEN_AGENT_ID = get_required_env("SHISHEN_AGENT_ID")
    GEJV_AGENT_ID = get_required_env("GEJV_AGENT_ID")
    SHENSHA_AGENT_ID = get_required_env("SHENSHA_AGENT_ID")
    BAZI_AGENT_ID = get_required_env("BAZI_AGENT_ID")
    FIX_JSON_AGENT_ID = get_required_env("FIX_JSON_AGENT_ID")
    DAYUN_AGENT_ID = os.getenv("DAYUN_AGENT_ID")
    SHIYE_AGENT_ID = os.getenv("SHIYE_AGENT_ID")
    YINYUAN_AGENT_ID = os.getenv("YINYUAN_AGENT_ID")
    XUEYE_AGENT_ID = os.getenv("XUEYE_AGENT_ID")
    CAIYUN_AGENT_ID = os.getenv("CAIYUN_AGENT_ID")
    JIANKANG_AGENT_ID = os.getenv("JIANKANG_AGENT_ID")

    ARK_API_KEY = get_required_env("ARK_API_KEY")
    ARK_BASE_URL = get_required_env("ARK_BASE_URL")

    # Aliyun OSS settings
    ALIYUN_ACCESS_KEY_ID: str = get_required_env("ALIYUN_ACCESS_KEY_ID")
    ALIYUN_ACCESS_KEY_SECRET: str = get_required_env("ALIYUN_ACCESS_KEY_SECRET")
    ALIYUN_OSS_ENDPOINT: str = get_required_env("ALIYUN_OSS_ENDPOINT")
    ALIYUN_BUCKET_NAME: str = get_required_env("ALIYUN_BUCKET_NAME")
    ALIYUN_BUCKET_ENV: str = get_required_env("ALIYUN_BUCKET_ENV")

    APPLE_KEY_ID: str = get_required_env("APPLE_KEY_ID")
    APPLE_ISSUER_ID: str = get_required_env("APPLE_ISSUER_ID")
    APPLE_BUNDLE_ID: str = get_required_env("APPLE_BUNDLE_ID")

    ALIYUN_WORKFLOW_ENDPOINT: str = get_required_env("ALIYUN_WORKFLOW_ENDPOINT")
    AGENT_LLM_MAP: dict[str, LLMType] = {
        "coordinator": "basic",
        "taoist_knowledge_agent": "basic",
    }

    BASIC_MODEL_BASE_URL = get_required_env("BASIC_MODEL_BASE_URL")
    BASIC_MODEL_NAME = get_required_env("BASIC_MODEL_NAME")
    BASIC_MODEL_KEY = get_required_env("BASIC_MODEL_KEY")

    REASON_MODEL_BASE_URL = get_required_env("REASON_MODEL_BASE_URL")
    REASON_MODEL_NAME = get_required_env("REASON_MODEL_NAME")
    REASON_MODEL_KEY = get_required_env("REASON_MODEL_KEY")

    # LLM Generation Parameters
    BASIC_MODEL_TEMPERATURE: float = float(os.getenv("BASIC_MODEL_TEMPERATURE", "0.1"))
    BASIC_MODEL_TOP_P: float = float(os.getenv("BASIC_MODEL_TOP_P", "0.1"))
    BASIC_MODEL_MAX_TOKENS: int = int(os.getenv("BASIC_MODEL_MAX_TOKENS", "8000"))
    BASIC_MODEL_FREQUENCY_PENALTY: float = float(
        os.getenv("BASIC_MODEL_FREQUENCY_PENALTY", "0.0")
    )
    BASIC_MODEL_PRESENCE_PENALTY: float = float(
        os.getenv("BASIC_MODEL_PRESENCE_PENALTY", "0.0")
    )

    REASON_MODEL_TEMPERATURE: float = float(
        os.getenv("REASON_MODEL_TEMPERATURE", "0.1")
    )
    REASON_MODEL_TOP_P: float = float(os.getenv("REASON_MODEL_TOP_P", "0.1"))
    REASON_MODEL_MAX_TOKENS: int = int(os.getenv("REASON_MODEL_MAX_TOKENS", "8000"))
    REASON_MODEL_FREQUENCY_PENALTY: float = float(
        os.getenv("REASON_MODEL_FREQUENCY_PENALTY", "0.0")
    )
    REASON_MODEL_PRESENCE_PENALTY: float = float(
        os.getenv("REASON_MODEL_PRESENCE_PENALTY", "0.0")
    )

    BASIC_MODEL_CONFIG: Dict[str, Any] = {
        "base_url": BASIC_MODEL_BASE_URL,
        "model": BASIC_MODEL_NAME,
        "api_key": BASIC_MODEL_KEY,
        "temperature": BASIC_MODEL_TEMPERATURE,
        "top_p": BASIC_MODEL_TOP_P,
        # Frequency Penalty: 频率惩罚，控制重复词汇的使用 (-2.0 to 2.0)
        # - 0.0: 不惩罚重复（默认）
        # - 0.1-0.5: 轻微减少重复
        # - 0.6-1.0: 显著减少重复
        # - 负值: 增加重复的可能性
        "frequency_penalty": BASIC_MODEL_FREQUENCY_PENALTY,
        # Presence Penalty: 存在惩罚，鼓励或抑制新话题的引入 (-2.0 to 2.0)
        # - 0.0: 不调整话题倾向（默认）
        # - 0.1-0.5: 鼓励谈论新话题
        # - -0.1--0.5: 鼓励深入当前话题
        # - 高正值: 强制探索新话题，可能导致不连贯
        "presence_penalty": BASIC_MODEL_PRESENCE_PENALTY,
        # 根据服务提供商要求，max_tokens 需要放在 extra_body 中
        "extra_body": {
            "max_tokens": BASIC_MODEL_MAX_TOKENS,
        },
    }

    REASON_MODEL_CONFIG: Dict[str, Any] = {
        "base_url": REASON_MODEL_BASE_URL,
        "model": REASON_MODEL_NAME,
        "api_key": REASON_MODEL_KEY,
        "temperature": REASON_MODEL_TEMPERATURE,
        "top_p": REASON_MODEL_TOP_P,
        # Frequency Penalty: 频率惩罚，控制重复词汇的使用 (-2.0 to 2.0)
        # - 0.0: 不惩罚重复（默认）
        # - 0.1-0.5: 轻微减少重复
        # - 0.6-1.0: 显著减少重复
        # - 负值: 增加重复的可能性
        "frequency_penalty": REASON_MODEL_FREQUENCY_PENALTY,
        # Presence Penalty: 存在惩罚，鼓励或抑制新话题的引入 (-2.0 to 2.0)
        # - 0.0: 不调整话题倾向（默认）
        # - 0.1-0.5: 鼓励谈论新话题
        # - -0.1--0.5: 鼓励深入当前话题
        # - 高正值: 强制探索新话题，可能导致不连贯
        "presence_penalty": REASON_MODEL_PRESENCE_PENALTY,
        # 根据服务提供商要求，max_tokens 需要放在 extra_body 中
        "extra_body": {
            "max_tokens": REASON_MODEL_MAX_TOKENS,
        },
    }

    DAILY_CHAT_LIMIT = int(get_required_env("DAILY_CHAT_LIMIT"))

    # WHETHER USE ARQ
    # TODO: remove this after ARQ is fully launched
    USE_ARQ_TO_GENERATE_REPORT = (
        os.getenv("USE_ARQ_TO_GENERATE_REPORT", "false").lower() == "true"
    )


settings = Settings()
