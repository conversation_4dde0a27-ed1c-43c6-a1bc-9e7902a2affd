# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from typing import List, Optional, Union

from pydantic import BaseModel, Field


class ContentItem(BaseModel):
    type: str = Field(..., description="The type of content (text, image, etc.)")
    text: Optional[str] = Field(None, description="The text content if type is 'text'")
    image_url: Optional[str] = Field(
        None, description="The image URL if type is 'image'"
    )


class ChatMessage(BaseModel):
    role: str = Field(
        ..., description="The role of the message sender (user or assistant)"
    )
    content: Union[str, List[ContentItem]] = Field(
        ...,
        description="The content of the message, either a string or a list of content items",
    )


class StreamChatRequest(BaseModel):
    messages: Optional[List[ChatMessage]] = Field(
        default_factory=lambda: [],
        description="History of messages between the user and the assistant",
    )
    thread_id: Optional[str] = Field(
        "__default__", description="A specific conversation identifier"
    )
    user_id: Optional[int] = Field(None, description="The user ID making the request")
