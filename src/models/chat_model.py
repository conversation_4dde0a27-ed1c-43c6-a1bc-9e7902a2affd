from pydantic import BaseModel
from typing import List, Optional, Literal


class Message(BaseModel):
    role: Literal["system", "user", "assistant"]
    content: str


class ChatRequest(BaseModel):
    messages: List[Message]
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 1000
    provider: str = "deepseek"


class ChatResponse(BaseModel):
    id: str
    choices: List[dict]
    created: int
    model: str
