from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel
from src.common.pagination import PaginationResponse, PaginatedResponse


class ActivityResponse(BaseModel):
    """Response model for activity data combined with user participation record"""
    # Activity fields
    activity_id: str
    activity_name: str
    description: Optional[str] = None
    activity_type: Optional[str] = None
    activity_start_time: datetime
    activity_end_time: Optional[datetime] = None
    activity_status: str
    rules: Optional[str] = None  # JSON string
    rewards: str  # JSON string
    
    # User activity record fields (optional - only present if user participated)
    user_id: Optional[str] = None
    user_activity_id: Optional[str] = None
    activity_record_status: Optional[str] = None
    reward_details: Optional[str] = None
    notes: Optional[str] = None
    record_created_at: Optional[datetime] = None
    record_updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ActivityListResponse(BaseModel):
    """Response model for paginated activity list"""
    data: List[ActivityResponse]
    pagination: PaginationResponse

    class Config:
        from_attributes = True


class ActivityQueryParams(BaseModel):
    """Query parameters for activity endpoint"""
    activity_status: Optional[str] = None
    page: Optional[int] = 1
    limit: Optional[int] = 20
    params: Optional[str] = None  # Additional filter parameters as JSON string

    def __post_init__(self):
        # Ensure page is at least 1
        if self.page and self.page < 1:
            self.page = 1
        # Ensure limit is between 1 and 100
        if self.limit:
            if self.limit < 1:
                self.limit = 1
            elif self.limit > 100:
                self.limit = 100
