from typing import List, Optional
from enum import Enum
from src.utils.constants import BaziResultStatus
from pydantic import BaseModel
from src.models.product_model import ProductResponse
from src.models.order_model import OrderResponse


class PreReportType(str, Enum):
    # this indicates all the pre reports
    PRE_REPORT = "pre_report"

    KONGWANG_REPORT = "kongwang_report"
    YONGSHEN_REPORT = "yongshen_report"
    SHISHEN_REPORT = "shishen_report"
    SHENSHA_REPORT = "shensha_report"
    GEJV_REPORT = "gejv_report"


class ReportType(str, Enum):
    DAYUN = "dayun"
    XUEYE = "xueye"
    BASIC = "basic"
    YINYUAN = "yinyuan"
    CAIYUN = "caiyun"
    SHIYE = "shiye"


PRODUCT_NAME_TO_REPORT_TYPE = {
    "八字算命-大运报告": [ReportType.DAYUN],
    "八字算命-学业报告": [ReportType.XUEYE],
    "八字基础报告": [ReportType.BASIC],
    "八字算命-姻缘报告": [ReportType.YINYUAN],
    "八字算命-财运报告": [ReportType.CAIYUN],
    "八字算命-事业报告": [ReportType.SHIYE],
    "八字算命-解锁全部报告": [
        ReportType.XUEYE,
        ReportType.YINYUAN,
        ReportType.CAIYUN,
        ReportType.SHIYE,
    ],
}


class BAZIBaseDataRequest(BaseModel):
    year: str
    month: str
    day: str
    hour: str
    minute: str
    # 0 for female, 1 for male
    gender: int


class BAZIReportRequest(BAZIBaseDataRequest):
    report_version: Optional[str] = "1.0"


class BAZIBaseDataResponse(BaseModel):
    data: dict


class BAZIAttemptModel(BaseModel):
    attempt_id: str
    year: int
    month: int
    day: int
    hour: int
    gender: int

    class Config:
        from_attributes = True


class BAZIResultModel(BaseModel):
    result_id: str
    attempt_id: str
    report_version: str = "1.0"
    result_type: str
    status: BaziResultStatus
    output: Optional[dict] = None
    error: Optional[dict] = None

    class Config:
        from_attributes = True


class DaYunResponse(BaseModel):
    da_yun_list: List[dict]


class DaYunReportRequest(BaseModel):
    bazi_data: str
    bazi_report: str
    dayun_data: str


class SpecificReportType(str, Enum):
    XUEYE = "学业"
    SHIYE = "事业"
    YINYUAN = "姻缘"
    JIANKANG = "健康"
    CAIYUN = "财运"
    BASIC = "基础"
    DAYUN = "大运"

    def get_report_type_str(self) -> str:
        mapping = {
            SpecificReportType.YINYUAN: ReportType.YINYUAN,
            SpecificReportType.XUEYE: ReportType.XUEYE,
            SpecificReportType.SHIYE: ReportType.SHIYE,
            SpecificReportType.CAIYUN: ReportType.CAIYUN,
            SpecificReportType.BASIC: ReportType.BASIC,
            SpecificReportType.DAYUN: ReportType.DAYUN,
        }
        if self in mapping:
            return mapping[self].value
        raise ValueError(f"Invalid specific report type: {self}")

    @classmethod
    def get_report_type(cls, result_type: str) -> "SpecificReportType":
        if result_type == "xueye":
            return cls.XUEYE
        elif result_type == "shiye":
            return cls.SHIYE
        elif result_type == "yinyuan":
            return cls.YINYUAN
        elif result_type == "jiankang":
            return cls.JIANKANG
        elif result_type == "caiyun":
            return cls.CAIYUN
        elif result_type == "basic":
            return cls.BASIC
        elif result_type == "dayun":
            return cls.DAYUN
        raise ValueError(f"Invalid specific report type: {result_type}")

    @classmethod
    def get_prompt_template(cls, specific_type: "SpecificReportType") -> str:
        if specific_type == cls.XUEYE:
            return "xueye_report"
        elif specific_type == cls.SHIYE:
            return "shiye_report"
        elif specific_type == cls.YINYUAN:
            return "yinyuan_report"
        elif specific_type == cls.CAIYUN:
            return "caiyun_report"
        elif specific_type == cls.BASIC:
            return "basic_report"
        elif specific_type == cls.DAYUN:
            return "dayun_report"
        raise ValueError(f"Invalid specific report type: {specific_type}")


class SpecificReportRequest(BaseModel):
    bazi_data: str
    bazi_report: str
    da_yun_report: str
    specific_type: SpecificReportType


class SpecificReportV2Request(BaseModel):
    bazi_data: str  # 八字基础数据
    dayun_data: str  # 大运数据
    shensha_data: str  # 神煞数据
    shishen_data: str  # 十神数据
    gejv_data: str  # 格局数据
    kongwang_data: str  # 空亡数据
    yongshen_data: str  # 用神喜忌数据
    specific_type: SpecificReportType


class BaziReportAfterPurchaseRequest(BaseModel):
    profile_id: str
    product_id: Optional[str] = None


class BaziReportAfterPurchaseItem(BaseModel):
    product: ProductResponse
    order: OrderResponse
    bazi_result: Optional[BAZIResultModel] = None


class BaziReportAfterPurchaseResponse(BaseModel):
    profile_id: str
    bazi_product_order_result_list: List[BaziReportAfterPurchaseItem]


class ProcessBaziReportsRequest(BaseModel):
    bazi_data: str
    kongwang: str
    yongshen: str
    shishen: str
    shensha: str
    gejv: str
