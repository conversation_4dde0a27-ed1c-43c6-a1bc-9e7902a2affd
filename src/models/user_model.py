from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List
from datetime import datetime


class ProfileRequest(BaseModel):
    birth_year: str
    birth_month: str
    birth_day: str
    birth_hour: str
    birth_minutes: str
    gender: int
    name: str
    role_group: str = "other"


class ProfileParams(BaseModel):
    profile_id: Optional[str] = None
    user_id: Optional[str] = None
    birth_year: Optional[int] = None
    birth_month: Optional[int] = None
    birth_day: Optional[int] = None
    birth_hour: Optional[int] = None
    birth_minutes: Optional[int] = None
    gender: Optional[int] = None
    name: Optional[str] = None
    role_group: Optional[str] = None


class ProfileResponse(BaseModel):
    profile_id: str
    user_id: str
    birth_year: str
    birth_month: str
    birth_day: str
    birth_hour: str
    birth_minutes: str
    gender: int
    name: str
    role_group: str = "other"
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UserBase(BaseModel):
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    phone_number: Optional[str] = Field(None, min_length=5, max_length=20)


class UserCreate(UserBase):
    phone_number: str = Field(..., min_length=5, max_length=20)


class UserResponse(UserBase):
    user_id: str
    account_id: Optional[str] = None
    created_at: datetime
    profiles: List[ProfileResponse]

    class Config:
        from_attributes = True


class UserUpdateRequest(BaseModel):
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = None


class FirstRegisterGiftResponse(BaseModel):
    yuxu_coin: int
    title: str
    description: str


class LoginResponse(BaseModel):
    access_token: str
    token_type: str
    user_id: str
    username: str
    first_register_gift: Optional[FirstRegisterGiftResponse] = None


class SendVerificationRequest(BaseModel):
    phone_number: str = Field(..., min_length=5, max_length=20)


class VerifyCodeRequest(BaseModel):
    phone_number: str = Field(..., min_length=5, max_length=20)
    verification_code: str = Field(..., min_length=6, max_length=6)


class LoginRequest(BaseModel):
    phone_number: str = Field(..., min_length=5, max_length=20)
    verification_code: str = Field(..., min_length=6, max_length=6)


# 在文件中添加以下新模型


class PurchasedProductResponse(BaseModel):
    product_id: str
    apple_product_id: str
    order_status: str
    unlock_progress: int = 0  # 添加解锁进度字段，默认为0


class ProfileWithProductsResponse(BaseModel):
    profile_id: str
    user_id: str
    birth_year: str
    birth_month: str
    birth_day: str
    birth_hour: str
    birth_minutes: str
    gender: int
    name: str
    role_group: str = "other"
    created_at: datetime
    updated_at: datetime
    purchased_products: List[PurchasedProductResponse] = []

    class Config:
        from_attributes = True


class UserInfoResponse(BaseModel):
    user_id: str
    account_id: Optional[str] = None
    username: str
    profiles: List[ProfileWithProductsResponse]


class AppleLoginRequest(BaseModel):
    apple_user_id: str = Field(..., description="Apple User ID")
    name: Optional[str] = Field(None, description="User's name from Apple")
    email: Optional[EmailStr] = Field(None, description="User's email from Apple")


class ProfileUpdateRequest(BaseModel):
    profile_id: str
    name: str
    role_group: str


class GiveYuxuCoinRequest(BaseModel):
    user_id: str
    amount: int
    description: Optional[str] = None


class GiveYuxuCoinData(BaseModel):
    user_id: str
    username: str
    amount: int
    balance_before: int
    balance_after: int
    transaction_id: str


class GiveYuxuCoinResponse(BaseModel):
    success: bool
    message: str
    data: Optional[GiveYuxuCoinData] = None
