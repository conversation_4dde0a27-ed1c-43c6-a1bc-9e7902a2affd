from typing import Any, Optional
from pydantic import BaseModel

from src.utils.constants import ErrorCodes


class CommonResponse(BaseModel):
    status: int
    message: str
    data: Optional[Any] = None

    @classmethod
    def success(cls, data: Any = None) -> "CommonResponse":
        return cls(status=0, message="success", data=data)

    @classmethod
    def error(
        cls, error_code: int = ErrorCodes.SERVER_ERROR, message: str = "error"
    ) -> "CommonResponse":
        return cls(status=error_code, message=message, data=None)
