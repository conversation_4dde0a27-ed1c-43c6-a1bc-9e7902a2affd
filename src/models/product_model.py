from pydantic import BaseModel
from typing import List, Any, Dict
from enum import Enum
from pydantic import field_validator
import json


class ProductGroup(Enum):
    REPORT = "REPORT"
    YUXU_COIN = "YUXU_COIN"


class ProductResponse(BaseModel):
    product_id: str
    apple_product_id: str
    product_name: str
    description: str
    product_type: str
    price: float
    currency: str
    localized_title: str
    localized_desc: str
    is_active: bool
    sale_type: int
    product_group: str
    payment_method: Dict[str, Any]  # 修改为 dict

    @field_validator("payment_method", mode="before")
    @classmethod
    def parse_payment_method(cls, v: Any) -> Any:
        if isinstance(v, str):
            try:
                return json.loads(v)
            except Exception:
                raise ValueError("payment_method 字段不是合法的 JSON 字符串")
        return v

    class Config:
        from_attributes = True


class CSVImportResponse(BaseModel):
    success: bool
    message: str
    created_count: int
    updated_count: int
    total_processed: int
    errors: List[str]
