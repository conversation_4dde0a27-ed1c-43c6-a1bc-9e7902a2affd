from datetime import datetime
from enum import Enum

from sqlalchemy import Integer, String, DATETIME
from sqlalchemy.orm import Mapped, mapped_column
from typing import Optional
from src.models.database.base import Base


class YuxuCoinTransaction(Base):
    __tablename__ = "yuxucoin_transactions"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    transaction_log_id: Mapped[str] = mapped_column(
        String(36), unique=True, nullable=False
    )
    wallet_id: Mapped[str] = mapped_column(String(36), nullable=False, index=True)
    user_id: Mapped[str] = mapped_column(String(36), nullable=False, index=True)
    transaction_type: Mapped[str] = mapped_column(
        String(50), nullable=False, index=True
    )
    amount: Mapped[int] = mapped_column(Integer, nullable=False)
    balance_before: Mapped[int] = mapped_column(Integer, nullable=False)
    balance_after: Mapped[int] = mapped_column(Integer, nullable=False)
    related_entity_type: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True
    )
    related_entity_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    description: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    created_at: Mapped[datetime] = mapped_column(DATETIME, default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(DATETIME, default=datetime.now)


class YuxuCoinTransactionType(str, Enum):
    # 购买
    PURCHASE = "purchase"
    # 提现
    WITHDRAW = "withdraw"
    # 转账
    TRANSFER = "transfer"
    # 充值
    RECHARGE = "recharge"
    # 赠送
    GIVE = "give"
    # 退款
    REFUND = "refund"
    OTHER = "other"


class YuxuCoinTransactionRelatedEntityType(str, Enum):
    ORDER = "order"
    ACTIVITY = "activity"
    OTHER = "other"
