from datetime import datetime
from sqlalchemy import Integer, String, TIMESTAMP, SMALLINT
from sqlalchemy.orm import Mapped, mapped_column
from src.models.database.base import Base


class User(Base):
    __tablename__ = "users"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    user_id: Mapped[str] = mapped_column(
        String(36), unique=True, nullable=False, index=True
    )
    phone_number: Mapped[str] = mapped_column(String(20), nullable=True, index=True)
    apple_user_id: Mapped[str] = mapped_column(
        String(255), unique=True, nullable=True, index=True
    )
    account_id: Mapped[str] = mapped_column(
        String(10), unique=True, nullable=True, index=True
    )
    is_active: Mapped[int] = mapped_column(SMALLINT, default=1)
    username: Mapped[str] = mapped_column(String(50), nullable=False)
    email: Mapped[str] = mapped_column(String(100), nullable=True)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP, default=datetime.now, onupdate=datetime.now
    )


class UserProfile(Base):
    __tablename__ = "user_profiles"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    profile_id: Mapped[str] = mapped_column(
        String(36), unique=True, nullable=False, index=True
    )
    user_id: Mapped[str] = mapped_column(String(36), nullable=False, index=True)
    birth_year: Mapped[str] = mapped_column(String(4), nullable=True)
    birth_month: Mapped[str] = mapped_column(String(2), nullable=True)
    birth_day: Mapped[str] = mapped_column(String(2), nullable=True)
    birth_hour: Mapped[str] = mapped_column(String(2), nullable=True)
    birth_minutes: Mapped[str] = mapped_column(String(2), nullable=True)
    gender: Mapped[int] = mapped_column(SMALLINT, nullable=True)
    name: Mapped[str] = mapped_column(String(50), nullable=False)
    role_group: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        default="other",
        comment="角色分组",
        server_default="other",
    )
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP, default=datetime.now, onupdate=datetime.now
    )
