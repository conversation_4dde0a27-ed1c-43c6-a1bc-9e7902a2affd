from datetime import datetime
from sqlalchemy import Inte<PERSON>, String, JSON, TIMESTAMP, SMALLINT, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column
from src.models.database.base import Base
from src.utils.constants import BaziResultStatus


class BaziAttempt(Base):
    __tablename__ = "bazi_attempt"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    attempt_id: Mapped[str] = mapped_column(
        String(64), unique=True, nullable=False, index=True
    )
    year: Mapped[int] = mapped_column(Integer)
    month: Mapped[int] = mapped_column(Integer)
    day: Mapped[int] = mapped_column(Integer)
    hour: Mapped[int] = mapped_column(Integer)
    gender: Mapped[int] = mapped_column(SMALLINT)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP, default=datetime.now, onupdate=datetime.now
    )

    __table_args__ = (
        UniqueConstraint(
            "year", "month", "day", "hour", "gender", name="uq_bazi_attempt_date_gender"
        ),
    )


class BaziResult(Base):
    __tablename__ = "bazi_result"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    result_id: Mapped[str] = mapped_column(String, primary_key=True)
    bazi_attempt_id: Mapped[str] = mapped_column(String(64), nullable=False, index=True)
    version: Mapped[str] = mapped_column(String(64), nullable=False)
    result_type: Mapped[str] = mapped_column(String(64), nullable=False)
    status: Mapped[str] = mapped_column(
        String(64), nullable=False, default=BaziResultStatus.PENDING.value
    )
    output: Mapped[dict] = mapped_column(JSON)
    error: Mapped[dict] = mapped_column(JSON)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP, default=datetime.now, onupdate=datetime.now
    )
