from datetime import datetime
from sqlalchemy import Integer, String, TIMESTAMP, Enum, JSO<PERSON>
from sqlalchemy.orm import Mapped, mapped_column
from typing import Optional
from src.models.database.base import Base
from src.utils.constants import OrderStatus


# 定义可以被支付的订单状态
PURCHASABLE_ORDER_STATUSES = [
    OrderStatus.INIT.value,
    OrderStatus.PURCHASE_FAILED.value,
    OrderStatus.CANCELED.value,
]


class Order(Base):
    __tablename__ = "orders"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    order_type: Mapped[Optional[str]] = mapped_column(String(60), nullable=True)
    order_id: Mapped[str] = mapped_column(String(36), unique=True, nullable=False)
    user_id: Mapped[str] = mapped_column(String(36), nullable=False, index=True)
    profile_id: Mapped[str] = mapped_column(String(36), nullable=False, default="")
    product_id: Mapped[str] = mapped_column(String(255), nullable=False)
    status: Mapped[Optional[str]] = mapped_column(String(64), nullable=True, index=True)
    amount: Mapped[int] = mapped_column(Integer, nullable=False)
    single_price: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    total_price: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    currency: Mapped[Optional[str]] = mapped_column(String(3), nullable=True)
    environment: Mapped[str] = mapped_column(
        Enum("production", "sandbox", name="environment_enum"), nullable=True
    )
    extra_info: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP, default=datetime.now, onupdate=datetime.now
    )


class Transaction(Base):
    __tablename__ = "transactions"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    transaction_id: Mapped[str] = mapped_column(
        String(255), unique=True, nullable=False
    )
    original_transaction_id: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True
    )
    transaction_type: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    order_id: Mapped[str] = mapped_column(String(36), nullable=False, index=True)
    environment: Mapped[str] = mapped_column(String(36), nullable=False)
    price: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    currency: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True, default="CNY"
    )
    purchase_date: Mapped[datetime] = mapped_column(TIMESTAMP, nullable=False)
    expires_date: Mapped[Optional[datetime]] = mapped_column(
        TIMESTAMP, nullable=True, index=True
    )
    revocation_date: Mapped[Optional[datetime]] = mapped_column(
        TIMESTAMP, nullable=True
    )
    revocation_reason: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP, default=datetime.now, onupdate=datetime.now
    )


class Subscription(Base):
    __tablename__ = "subscriptions"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    subscription_id: Mapped[str] = mapped_column(
        String(36), unique=True, nullable=False
    )
    user_id: Mapped[str] = mapped_column(String(36), nullable=False, index=True)
    product_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    latest_order_id: Mapped[str] = mapped_column(String(255), nullable=False)
    start_date: Mapped[datetime] = mapped_column(TIMESTAMP, nullable=False)
    expires_date: Mapped[datetime] = mapped_column(
        TIMESTAMP, nullable=False, index=True
    )
    cancellation_reason: Mapped[Optional[str]] = mapped_column(
        Enum(
            "user_canceled", "price_increase", "other", name="cancellation_reason_enum"
        ),
        nullable=True,
    )
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP, default=datetime.now, onupdate=datetime.now
    )
