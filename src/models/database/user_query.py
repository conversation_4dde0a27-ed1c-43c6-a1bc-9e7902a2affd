from sqlalchemy import Column, String, Text, DateTime, BigInteger, Index
from sqlalchemy.sql import func
from src.models.database.base import Base


class UserQuery(Base):
    __tablename__ = "user_query"

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="消息ID")
    user_id = Column(String(50), nullable=False, comment="用户ID")
    query = Column(Text, nullable=False, comment="用户查询内容")
    created_time = Column(
        DateTime, nullable=False, default=func.current_timestamp(), comment="创建时间"
    )
    updated_time = Column(
        DateTime,
        nullable=False,
        default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间",
    )

    __table_args__ = (Index("idx_user_id", "user_id"),)
