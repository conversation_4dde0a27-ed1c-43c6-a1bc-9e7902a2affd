from sqlalchemy.orm import DeclarativeBase
from typing import Dict, Any


class Base(DeclarativeBase):
    def to_filter_dict(self) -> Dict[str, Any]:
        return {
            k: v
            for k, v in self.__dict__.items()
            if v is not None and k != "_sa_instance_state"
        }

    def update(self, values_dict: Dict[str, Any]) -> "Base":
        """Update the model with values from a dictionary.

        Args:
            values_dict: A dictionary containing attribute names and values
        """
        for key, value in values_dict.items():
            if hasattr(self, key) and value is not None:
                setattr(self, key, value)
        return self
