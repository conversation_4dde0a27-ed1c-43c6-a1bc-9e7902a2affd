from datetime import datetime
from sqlalchemy import Integer, String, TIMESTAMP, SMALLINT, Text
from sqlalchemy.orm import Mapped, mapped_column
from typing import Optional
from src.models.database.base import Base


class Product(Base):
    __tablename__ = "products"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    product_id: Mapped[str] = mapped_column(String(255), unique=True, nullable=False)
    apple_product_id: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True, unique=True, default=""
    )
    product_name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    product_type: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    product_group: Mapped[str] = mapped_column(
        String(32), nullable=False, default="REPORT"
    )
    payment_method: Mapped[str] = mapped_column(String(512), nullable=False)
    price: Mapped[int] = mapped_column(Integer, nullable=False)
    currency: Mapped[str] = mapped_column(String(10), nullable=False, default="")
    localized_title: Mapped[str] = mapped_column(String(255), nullable=False)
    localized_desc: Mapped[str] = mapped_column(Text, nullable=False)
    is_active: Mapped[int] = mapped_column(SMALLINT, default=1)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP, default=datetime.now, onupdate=datetime.now
    )
    sale_type: Mapped[int] = mapped_column(Integer, nullable=False)
