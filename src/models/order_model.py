from datetime import datetime
from typing import Optional, List, Dict, Any

from src.models.product_model import ProductResponse

from pydantic import BaseModel, field_validator  # 更新导入

from src.utils.constants import OrderStatus


class CreateOrderRequest(BaseModel):
    product_id: str
    profile_id: Optional[str] = None
    report_version: Optional[str] = "1.0"


class CreateOrderResponse(BaseModel):
    order_id: str


# 定义响应模型
# In src/models/order_model.py, update the OrderResponse class:


class OrderResponse(BaseModel):
    order_id: str
    user_id: Optional[str] = None
    profile_id: Optional[str] = None
    product_id: str
    order_status: Optional[str] = None
    order_type: Optional[str]
    amount: int
    single_price: Optional[int]
    total_price: Optional[int]
    currency: Optional[str]
    created_at: datetime
    updated_at: datetime
    extra_info: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True
        populate_by_name = True

    @classmethod
    def model_validate(cls, obj: Any, *args: Any, **kwargs: Any) -> "OrderResponse":
        if hasattr(obj, "status") and not hasattr(obj, "order_status"):
            obj.order_status = obj.status
        return super().model_validate(obj, *args, **kwargs)


class OrderListResponse(BaseModel):
    orders: List[OrderResponse]


class OrderQueryParams(BaseModel):
    user_id: Optional[str] = None
    order_id: Optional[str] = None
    order_type: Optional[str] = None
    status: Optional[str] = None
    profile_id: Optional[str] = None
    product_id: Optional[str] = None
    environment: Optional[str] = None

    def to_filter_dict(self) -> Dict[str, Any]:
        return {k: v for k, v in self.dict().items() if v is not None}


class VerifyPurchaseRequest(BaseModel):
    order_id: str
    transaction_id: str
    product_id: str


class VerifyPurchaseResponse(BaseModel):
    order_status: str = "INIT"  # Provide a default value


class UpdateOrderStatusRequest(BaseModel):
    status: str

    @field_validator("status")
    @classmethod  # 需要添加 classmethod 装饰器
    def validate_status(cls, v: str) -> str:
        if v not in [status.value for status in OrderStatus]:
            raise ValueError(
                f"无效的订单状态。可选值: {[status.value for status in OrderStatus]}"
            )
        return v


class OrderWithProductResponse(BaseModel):
    order: OrderResponse
    product: ProductResponse

    class Config:
        from_attributes = True


class OrderCountResponse(BaseModel):
    """订单数量响应模型"""

    order_count: int
