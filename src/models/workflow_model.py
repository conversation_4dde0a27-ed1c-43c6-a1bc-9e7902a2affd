from typing import Optional, Dict, Any, List
from pydantic import BaseModel
from src.utils.constants import WorkflowTaskStatus


class AliyunWorkflow(BaseModel):
    workflow_name: Optional[str] = None
    workflow_execution_id: Optional[str] = None
    callback_token: Optional[str] = None
    workflow_input: Optional[Dict[str, Any]] = None
    workflow_output: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True


class ArqWorkflowTaskStatusRequest(BaseModel):
    """ARQ工作流任务状态查询请求"""

    task_id: str


class ArqWorkflowTaskStatusResponse(BaseModel):
    """ARQ工作流任务状态响应"""

    task_id: str
    status: WorkflowTaskStatus
    result: Optional[dict] = None
    error: Optional[str] = None
    progress: Optional[dict] = None


class ArqWorkflowTaskListResponse(BaseModel):
    """ARQ用户工作流任务列表响应"""

    tasks: List[Dict[str, Any]]
    total: int


class RetryTaskRequest(BaseModel):
    """单个任务重试请求"""

    order_id: str
    force_retry_unfailed: bool = False


class RetryTaskResult(BaseModel):
    """单个任务重试结果"""

    order_id: str
    task_id: Optional[str] = None
    status: str  # "success" or "failed"
    error: Optional[str] = None


class RetryAllFailedTasksResponse(BaseModel):
    """重试所有失败任务的响应"""

    total_failed_orders: int
    successful_retries: int
    failed_retries: int
    retry_results: List[RetryTaskResult]
    message: str
