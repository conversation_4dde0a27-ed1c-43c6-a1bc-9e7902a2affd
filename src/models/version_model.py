from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class Platform(str, Enum):
    """Platform enumeration"""

    IOS = "ios"
    ANDROID = "android"


class UpdatePolicy(int, Enum):
    """Update policy enumeration"""

    NO_UPDATE = 0  # 无需更新
    SUGGESTED_UPDATE = 1  # 建议更新
    FORCED_UPDATE = 2  # 强制更新


class VersionCheckRequest(BaseModel):
    """Version check request model"""

    platform: Platform = Field(..., description="Platform type: ios or android")
    appVersion: str = Field(..., alias="appVersion", description="Current app version")
    language: Optional[str] = Field(
        "zh-CN", description="Current app language for localized messages"
    )

    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "platform": Platform.IOS,
                "appVersion": "1.0.0",
                "language": "zh-CN",
            }
        }


class VersionCheckData(BaseModel):
    """Version check response data model"""

    policy: UpdatePolicy = Field(
        ..., description="Update policy: 0=no update, 1=suggested, 2=forced"
    )
    latestVersion: Optional[str] = Field(
        None, alias="latestVersion", description="Latest available version"
    )
    updateUrl: Optional[str] = Field(
        None, alias="updateUrl", description="App store or download URL"
    )
    title: Optional[str] = Field(None, description="Update dialog title")
    description: Optional[str] = Field(None, description="Update dialog description")
    extra: Dict[str, Any] = Field(
        default_factory=dict, description="Extra data for features, hotfix, etc."
    )

    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "policy": UpdatePolicy.SUGGESTED_UPDATE,
                "latestVersion": "1.1.0",
                "updateUrl": "itms-apps://itunes.apple.com/app/id6742788816",
                "title": "发现重要更新",
                "description": "修复了一些问题...",
                "extra": {},
            }
        }
