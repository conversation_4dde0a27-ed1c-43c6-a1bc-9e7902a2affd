class UnretryableException(Exception):
    pass


class ProductNotAvailableException(UnretryableException):
    pass


class PurchaseFailedException(Exception):
    def __init__(self, error_code: int, message: str):
        self.error_code = error_code
        self.message = message
        super().__init__(self.message)

    def __str__(self) -> str:
        return f"PurchaseFailedException: {self.message}"
