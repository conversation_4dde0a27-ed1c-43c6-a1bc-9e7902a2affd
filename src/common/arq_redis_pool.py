"""
Redis pool management for ARQ application

This module provides a singleton Redis pool manager for efficient connection reuse
across the ARQ-based asynchronous task system.
"""

import asyncio
from typing import Optional
from arq import create_pool
from arq.connections import ArqRedis
from src.configure.arq_config import arq_config
from src.utils.log_util import logger


class RedisPoolManager:
    """Singleton Redis pool manager for the application"""

    _instance: Optional["RedisPoolManager"] = None
    _pool: Optional[ArqRedis] = None
    _lock = asyncio.Lock()

    def __new__(cls) -> "RedisPoolManager":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def get_pool(self) -> ArqRedis:
        """Get the Redis pool, creating it if it doesn't exist"""
        if self._pool is None:
            async with self._lock:
                if self._pool is None:  # Double-check locking
                    logger.info("Creating Redis pool for application")
                    self._pool = await create_pool(arq_config.get_redis_settings())
                    logger.info("Redis pool created successfully")
        return self._pool

    async def close_pool(self) -> None:
        """Close the Redis pool"""
        if self._pool is not None:
            async with self._lock:
                if self._pool is not None:
                    logger.info("Closing Redis pool")
                    await self._pool.aclose()
                    self._pool = None
                    logger.info("Redis pool closed")

    async def health_check(self) -> bool:
        """Check if the Redis pool is healthy"""
        try:
            if self._pool is None:
                return False
            await self._pool.ping()
            return True
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return False


# Global Redis pool manager instance
redis_pool_manager = RedisPoolManager()


# Convenience functions for accessing the shared Redis pool
async def get_redis_pool() -> ArqRedis:
    """Get the shared Redis pool instance"""
    return await redis_pool_manager.get_pool()


async def close_redis_pool() -> None:
    """Close the shared Redis pool instance"""
    await redis_pool_manager.close_pool()


async def redis_health_check() -> bool:
    """Check if the Redis pool is healthy"""
    return await redis_pool_manager.health_check()
