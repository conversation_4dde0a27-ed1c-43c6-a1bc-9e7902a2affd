from functools import wraps
from fastapi import <PERSON><PERSON>PException
from typing import Any, Callable
from src.utils.log_util import logger


def handle_exceptions(error_message: str = "Operation failed") -> Callable:
    """
    Decorator to handle exceptions in API endpoints.
    Passes through HTTPExceptions but wraps other exceptions in HTTPException with status 500.
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return await func(*args, **kwargs)
            except HTTPException as e:
                logger.error(
                    f"HTTPException in {func.__name__}: {str(e)}", exc_info=True
                )
                raise
            except Exception as e:
                logger.error(f"Error in {func.__name__}: {str(e)}", exc_info=True)
                raise HTTPException(
                    status_code=500, detail=f"{error_message}: {str(e)}"
                )

        return wrapper

    return decorator
