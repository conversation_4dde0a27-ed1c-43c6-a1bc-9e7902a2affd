from typing import AsyncGenerator, Optional
from contextlib import asynccontextmanager
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from src.config import settings
from src.models.database.bazi import Base
from src.utils.constants import OrderStatus
from src.utils.log_util import logger


# Create engine
connection_string = (
    f"mysql+aiomysql://{settings.DB_USER}:{settings.DB_PASSWORD}@"
    f"{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}"
)
engine = create_async_engine(connection_string, pool_pre_ping=True)
SessionLocal = async_sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)


async def init_db() -> None:
    """Initialize database and create tables"""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


@asynccontextmanager
async def get_session() -> AsyncGenerator[AsyncSession, None]:
    """Async context manager for database sessions"""
    session = SessionLocal()
    try:
        yield session
        await session.commit()
    except Exception as e:
        logger.error(f"Database session error: {str(e)}")
        await session.rollback()
        raise
    finally:
        await session.close()


@asynccontextmanager
async def get_order_session(
    operation_name: str,
    order_id: Optional[str] = None,
    error_order_status: OrderStatus = OrderStatus.PURCHASE_FAILED,
) -> AsyncGenerator[AsyncSession, None]:
    """
    通用的数据库事务执行上下文管理器

    Args:
        operation_name: 操作名称，用于日志
        order_id: 订单ID，用于失败时更新订单状态
    """
    async with get_session() as session:
        try:
            yield session
            await session.commit()
            logger.info(f"{operation_name} completed successfully")
        except Exception as e:
            logger.error(f"{operation_name} failed: {str(e)}")
            await session.rollback()

            # 如果有订单ID，尝试更新订单状态为失败
            if order_id:
                # 避免循环导入，在这里进行懒加载
                from src.client.database.order_client import order_client

                await order_client.update_order_status(order_id, error_order_status)
            raise
