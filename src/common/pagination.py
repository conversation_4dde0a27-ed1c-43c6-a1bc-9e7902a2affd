from typing import Any, Dict, List, Optional, TypeVar, Generic
from pydantic import BaseModel
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import Select

T = TypeVar('T')


class PaginationResponse(BaseModel, Generic[T]):
    """Generic pagination response model"""
    total: int
    page: int
    limit: int
    has_next: bool
    has_prev: bool


class PaginatedResponse(BaseModel, Generic[T]):
    """Generic paginated response wrapper"""
    data: List[T]
    pagination: PaginationResponse[T]


class PaginationParams(BaseModel):
    """Standard pagination parameters"""
    page: int = 1
    limit: int = 20
    
    def __post_init__(self):
        # Ensure page is at least 1
        if self.page < 1:
            self.page = 1
        # Ensure limit is between 1 and 100
        if self.limit < 1:
            self.limit = 1
        elif self.limit > 100:
            self.limit = 100


class PaginationHelper:
    """Helper class for handling pagination logic"""
    
    @staticmethod
    def calculate_offset(page: int, limit: int) -> int:
        """Calculate the offset for SQL queries"""
        return (page - 1) * limit
    
    @staticmethod
    def create_pagination_response(
        total: int, 
        page: int, 
        limit: int
    ) -> PaginationResponse:
        """Create a pagination response object"""
        has_next = (page * limit) < total
        has_prev = page > 1
        
        return PaginationResponse(
            total=total,
            page=page,
            limit=limit,
            has_next=has_next,
            has_prev=has_prev
        )
    
    @staticmethod
    async def paginate_query(
        session: AsyncSession,
        query: Select,
        page: int,
        limit: int
    ) -> tuple[List[Any], PaginationResponse]:
        """
        Execute a paginated query and return results with pagination info
        
        Args:
            session: Database session
            query: SQLAlchemy select query
            page: Page number (1-based)
            limit: Items per page
            
        Returns:
            Tuple of (results, pagination_response)
        """
        # Validate pagination parameters
        page = max(1, page)
        limit = max(1, min(100, limit))
        
        # Get total count
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await session.execute(count_query)
        total = total_result.scalar() or 0
        
        # Apply pagination to the original query
        offset = PaginationHelper.calculate_offset(page, limit)
        paginated_query = query.offset(offset).limit(limit)
        
        # Execute paginated query
        result = await session.execute(paginated_query)
        items = result.scalars().all()
        
        # Create pagination response
        pagination = PaginationHelper.create_pagination_response(total, page, limit)
        
        return items, pagination


def paginate_params(
    page: Optional[int] = 1,
    limit: Optional[int] = 20
) -> PaginationParams:
    """
    FastAPI dependency for pagination parameters
    
    Usage:
        @router.get("/items")
        async def get_items(pagination: PaginationParams = Depends(paginate_params)):
            ...
    """
    return PaginationParams(
        page=page or 1,
        limit=limit or 20
    )
