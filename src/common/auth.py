from fastapi import HTT<PERSON><PERSON>x<PERSON>, Header
from datetime import timed<PERSON>ta
from typing import Optional, Dict, Any, Callable
import jwt
from functools import wraps

from src.config import Settings
from src.client.database.user_client import user_client


class JWTAuth:
    """
    JWT Authentication utility class for FastAPI
    """

    SECRET_KEY = Settings.JWT_SECRET_KEY
    ALGORITHM = Settings.JWT_ALGORITHM
    ACCESS_TOKEN_EXPIRE_MINUTES = Settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES

    @classmethod
    def create_access_token(
        cls, data: Dict[str, Any], expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create a JWT access token
        """
        to_encode = data.copy()
        # 注释掉过期时间设置，让token永不过期
        # if expires_delta:
        #     expire = datetime.utcnow() + expires_delta
        # else:
        #     expire = datetime.utcnow() + timedelta(
        #         minutes=cls.ACCESS_TOKEN_EXPIRE_MINUTES
        #     )
        # to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, cls.SECRET_KEY, algorithm=cls.ALGORITHM)
        return str(encoded_jwt)

    @classmethod
    async def get_current_user_id(
        cls, authorization: Optional[str] = Header(None)
    ) -> str:
        """
        Get the current user from the JWT token
        """

        credentials_exception = HTTPException(
            status_code=401,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

        if not authorization or not authorization.startswith("Bearer "):
            raise credentials_exception

        token = authorization.replace("Bearer ", "")

        try:
            payload = jwt.decode(token, cls.SECRET_KEY, algorithms=[cls.ALGORITHM])
            user_id: str = payload.get("sub")
            if user_id is None:
                raise credentials_exception
            user = await user_client.get_by_user_id(user_id)
            if user is None or user.is_active == 0:
                raise credentials_exception
        except jwt.PyJWTError:
            raise credentials_exception

        return user_id

    @classmethod
    async def get_current_user_id_optional(
        cls, authorization: Optional[str] = Header(None)
    ) -> Optional[str]:
        """
        Get the current user from the JWT token, return None if not authenticated
        """
        if not authorization or not authorization.startswith("Bearer "):
            return None

        token = authorization.replace("Bearer ", "")

        try:
            payload = jwt.decode(token, cls.SECRET_KEY, algorithms=[cls.ALGORITHM])
            user_id: str = payload.get("sub")
            return user_id
        except jwt.PyJWTError:
            return None

    @classmethod
    def jwt_required(cls, func: Callable) -> Callable:
        """
        Decorator to protect endpoints with JWT authentication
        """

        @wraps(func)
        async def wrapper(
            *args: Any, authorization: Optional[str] = Header(None), **kwargs: Any
        ) -> Any:
            if not authorization or not authorization.startswith("Bearer "):
                raise HTTPException(
                    status_code=401,
                    detail="Invalid authentication credentials",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            token = authorization.replace("Bearer ", "")
            try:
                payload = jwt.decode(token, cls.SECRET_KEY, algorithms=[cls.ALGORITHM])
                user_id: str = payload.get("sub")
                if user_id is None:
                    raise HTTPException(
                        status_code=401,
                        detail="Invalid token payload",
                        headers={"WWW-Authenticate": "Bearer"},
                    )
            except jwt.PyJWTError:
                raise HTTPException(
                    status_code=401,
                    detail="Invalid token",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            return await func(*args, **kwargs)

        return wrapper


if __name__ == "__main__":
    print(
        JWTAuth.create_access_token(
            data={"sub": "5cef9569-094d-4d10-9ff2-b10ba66ee5c8"},
            expires_delta=timedelta(minutes=JWTAuth.ACCESS_TOKEN_EXPIRE_MINUTES),
        )
    )
