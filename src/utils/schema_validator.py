# -*- coding: utf-8 -*-
import json
from typing import Dict, Any
from pathlib import Path
import jsonschema
from src.utils.log_util import logger


class SchemaValidator:
    """简单的JSON Schema校验器"""

    def __init__(self) -> None:
        self.schemas_dir = Path(__file__).parent.parent / "schemas"
        self._schema_cache: Dict[str, Dict[str, Any]] = {}

    def load_schema(self, schema_name: str) -> Dict[str, Any]:
        """加载schema文件"""
        if schema_name in self._schema_cache:
            return self._schema_cache[schema_name]

        schema_path = self.schemas_dir / f"{schema_name}.json"
        if not schema_path.exists():
            raise FileNotFoundError(f"Schema file not found: {schema_path}")

        try:
            with open(schema_path, "r", encoding="utf-8") as f:
                schema: Dict[str, Any] = json.load(f)
            self._schema_cache[schema_name] = schema
            return schema
        except (OSError, json.JSONDecodeError) as e:
            raise ValueError(
                f"Failed to load or parse schema {schema_name}: {e}"
            ) from e

    def validate_report(
        self, data: Dict[str, Any], report_type: str, version: str = "v2"
    ) -> bool:
        """
        校验报告数据是否符合schema

        Args:
            data: 要校验的JSON数据
            report_type: 报告类型，如 'yinyuan'
            version: 版本，如 'v2'

        Returns:
            bool: 校验是否通过

        Raises:
            ValidationError: 校验失败时抛出异常
        """
        schema_name = f"{report_type}_{version}"

        try:
            schema = self.load_schema(schema_name)
            jsonschema.validate(instance=data, schema=schema)
            logger.info(f"Schema validation passed for {schema_name}")
            return True
        except Exception as e:
            logger.error(f"Schema validation failed for {schema_name}", e)
            raise e


# 创建全局实例
schema_validator = SchemaValidator()
