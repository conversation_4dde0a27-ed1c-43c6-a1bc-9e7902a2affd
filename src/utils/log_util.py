import logging
import os
import threading
import time
from logging.handlers import TimedRotatingFileHandler
from typing import Optional


def _is_arq_worker_context() -> bool:
    """
    Detect if the current process is running in ARQ worker context.

    Returns:
        bool: True if running in ARQ worker context, False otherwise
    """
    if os.getenv("ARQ_WORKER_CONTEXT") == "true":
        return True
    return False


class FlushingTimedRotatingFileHandler(TimedRotatingFileHandler):
    """Custom TimedRotatingFileHandler that flushes immediately after each log."""

    def emit(self, record: logging.LogRecord) -> None:
        """Emit a record and immediately flush to disk."""
        super().emit(record)
        if self.stream:
            self.stream.flush()


class Logger:
    _instance: Optional["Logger"] = None
    _lock = threading.Lock()
    _logger: Optional[logging.Logger] = None

    def __new__(cls) -> "Logger":
        # Double-checked locking pattern for thread safety
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(Logger, cls).__new__(cls)
                    cls._instance._initialize_logger()
        return cls._instance

    def _initialize_logger(self) -> None:
        """Initialize the logger with appropriate handlers based on environment."""
        # Get environment
        env = os.getenv("ENV", "dev")

        # Detect if running in ARQ worker context
        is_arq_worker = _is_arq_worker_context()

        # Create logs directory
        log_dir = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "logs"
        )
        os.makedirs(log_dir, exist_ok=True)

        # Set log file path with conditional prefix
        if is_arq_worker:
            log_filename = "yuxugong-arq-worker.log"
            logger_name = "yuxugong-arq-worker"
        else:
            log_filename = "yuxugong.log"
            logger_name = "yuxugong"

        log_file = os.path.join(log_dir, log_filename)

        # Configure logger
        self._logger = logging.getLogger(logger_name)
        self._logger.setLevel(
            logging.DEBUG
        )  # Base level set to DEBUG to allow all levels

        # Clear any existing handlers
        if self._logger.handlers:
            self._logger.handlers.clear()

        # Create formatter
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )

        # Configure handlers based on environment
        if env.lower() == "dev":
            # Development: console output only with DEBUG level
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.DEBUG)
            console_handler.setFormatter(formatter)
            self._logger.addHandler(console_handler)
        else:
            # Staging/Production: use custom TimedRotatingFileHandler with INFO level
            # This will rotate logs daily and keep logs for 30 days
            file_handler = FlushingTimedRotatingFileHandler(
                log_file,
                when="midnight",  # Rotate every day at midnight
                interval=1,  # Interval is 1 day
                backupCount=30,  # Keep 30 days of logs
                encoding="utf-8",  # Ensure proper encoding
                utc=False,  # Use local time (Asia/Shanghai)
            )

            # Custom namer to ensure rotated files have proper date suffix
            def custom_namer(_default_name: str) -> str:
                """Custom namer that ensures rotated files have date suffix."""
                # Get the rotation time from the handler
                rotation_time = file_handler.rolloverAt - file_handler.interval
                date_suffix = time.strftime("%Y-%m-%d", time.localtime(rotation_time))
                return os.path.join(log_dir, f"{log_filename}.{date_suffix}")

            # Custom rotator to ensure proper file rotation
            def custom_rotator(source: str, dest: str) -> None:
                """Custom rotator that archives the current log file."""
                if os.path.exists(source):
                    # Archive the current log file with date suffix
                    os.rename(source, dest)

            file_handler.namer = custom_namer
            file_handler.rotator = custom_rotator
            file_handler.setLevel(logging.INFO)
            file_handler.setFormatter(formatter)

            self._logger.addHandler(file_handler)

    @property
    def logger(self) -> logging.Logger:
        """Get the logger instance."""
        assert self._logger is not None, "Logger not initialized"
        return self._logger


# Create a default instance for easy import
logger = Logger().logger
