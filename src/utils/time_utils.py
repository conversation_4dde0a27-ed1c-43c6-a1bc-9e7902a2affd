import asyncio
import functools
import time
from datetime import datetime
from typing import Any, Callable, Optional, TypeVar

from .log_util import logger


def parse_timestamp_to_datetime(
    timestamp_ms: Optional[Any], default_value: Optional[datetime] = None
) -> Optional[datetime]:
    """
    将毫秒时间戳转换为datetime对象
    :param timestamp_ms: 毫秒时间戳
    :param default_value: 默认值，如果时间戳无效则返回此值
    :return: datetime对象或默认值
    """
    if timestamp_ms is not None and isinstance(timestamp_ms, (int, float)):
        return datetime.fromtimestamp(timestamp_ms / 1000)
    return default_value


# 定义泛型类型
F = TypeVar("F", bound=Callable[..., Any])


# TODO 切换成已有包
def timing(func_name: Optional[str] = None) -> Callable[[F], F]:
    """
    时间装饰器，记录方法的执行时间

    Args:
        func_name: 可选的函数名称，用于日志显示。如果不提供，使用原函数名
        log_args: 是否记录函数参数，默认False

    Usage:
        @timing()
        def my_function():
            pass

        @timing("自定义函数名")
        async def my_async_function():
            pass

        @timing(log_args=True)
        def function_with_args(a, b):
            pass
    """

    def decorator(func: F) -> F:
        display_name = func_name or func.__name__

        if asyncio.iscoroutinefunction(func):
            # 异步函数装饰器
            @functools.wraps(func)
            async def async_wrapper(*args: Any, **kwargs: Any) -> Any:
                start_time = time.perf_counter()

                try:
                    result = await func(*args, **kwargs)
                    end_time = time.perf_counter()
                    execution_time = end_time - start_time

                    # 记录成功完成日志
                    logger.info(
                        f"{display_name} 执行完成，耗时: {execution_time:.4f}秒"
                    )
                    return result

                except Exception as e:
                    end_time = time.perf_counter()
                    execution_time = end_time - start_time

                    # 记录异常日志
                    logger.error(
                        f"{display_name} 执行失败，耗时: {execution_time:.4f}秒，异常: {str(e)}"
                    )
                    raise

            return async_wrapper  # type: ignore
        else:
            # 同步函数装饰器
            @functools.wraps(func)
            def sync_wrapper(*args: Any, **kwargs: Any) -> Any:
                start_time = time.perf_counter()

                try:
                    result = func(*args, **kwargs)
                    end_time = time.perf_counter()
                    execution_time = end_time - start_time

                    # 记录成功完成日志
                    logger.info(
                        f"{display_name} 执行完成，耗时: {execution_time:.4f}秒"
                    )
                    return result

                except Exception as e:
                    end_time = time.perf_counter()
                    execution_time = end_time - start_time

                    # 记录异常日志
                    logger.error(
                        f"{display_name} 执行失败，耗时: {execution_time:.4f}秒，异常: {str(e)}"
                    )
                    raise

            return sync_wrapper  # type: ignore

    return decorator
