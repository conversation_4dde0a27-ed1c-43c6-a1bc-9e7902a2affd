from enum import Enum


class OrderStatus(Enum):
    INIT = "INIT"  # 初始状态
    PURCHASED = "PURCHASED"  # 已支付
    PROCESSING = "PROCESSING"  # 处理中（生成报告）
    SUCCESS = "SUCCESS"  # 成功
    FAILED = "FAILED"  # 失败
    PURCHASE_FAILED = "PURCHASE_FAILED"  # 支付失败
    CANCELED = "CANCELED"  # 已取消


class OrderType(Enum):
    NEW = ("NEW",)  # 新购
    RENEW = "RENEW"  # 续费


class BaziResultStatus(str, Enum):
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"

    @classmethod
    def is_final_status(cls, status: "BaziResultStatus") -> bool:
        return status in [cls.SUCCESS, cls.FAILED]


class WorkflowTaskStatus(str, Enum):
    """ARQ工作流任务状态枚举"""

    PENDING = "PENDING"  # 等待中
    STARTED = "STARTED"  # 已开始
    SUCCESS = "SUCCESS"  # 成功
    FAILURE = "FAILURE"  # 失败
    RETRY = "RETRY"  # 重试中
    REVOKED = "REVOKED"  # 已撤销

    @classmethod
    def is_final_status(cls, status: "WorkflowTaskStatus") -> bool:
        """检查是否为最终状态"""
        return status in [cls.SUCCESS, cls.FAILURE, cls.REVOKED]

    @classmethod
    def is_active_status(cls, status: "WorkflowTaskStatus") -> bool:
        """检查是否为活跃状态（正在执行或等待）"""
        return status in [cls.PENDING, cls.STARTED, cls.RETRY]


class ErrorCodes(int, Enum):
    """支付失败错误码"""

    BALANCE_NOT_ENOUGH = 10000  # 余额不足
    SERVER_ERROR = 10001  # 服务器错误
    ORDER_NOT_FOUND = 10002  # 订单不存在
    ORDER_ALREADY_PURCHASED = 10003  # 订单已支付
    ORDER_NOT_BELONG_TO_USER = 10004  # 订单不属于当前用户
