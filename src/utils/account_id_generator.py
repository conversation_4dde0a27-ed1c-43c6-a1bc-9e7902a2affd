import time
import secrets
import string


class AccountIdGenerator:
    """账户ID生成器，使用时间戳 + 随机码的方案"""

    # 基准时间：2024-01-01 00:00:00 UTC (毫秒级)
    BASE_TIMESTAMP = *************

    # Base62字符集
    CHARSET = string.ascii_letters + string.digits  # a-z, A-Z, 0-9

    @classmethod
    def generate(cls) -> str:
        """
        生成account_id
        在相同毫秒内：
        生成1个ID：冲突概率 = 0
        生成100个ID：冲突概率 ≈ 0.00034%
        生成1000个ID：冲突概率 ≈ 0.034%
        Returns:
            格式：7位时间戳（毫秒级）+ 4位随机码
        """
        # 固定使用7位时间戳 + 4位随机码
        time_length = 7
        random_length = 4

        # 生成时间戳部分（毫秒级）
        current_time = int(time.time_ns() // 1000000)
        relative_time = current_time - cls.BASE_TIMESTAMP
        time_part = cls._encode_base62(relative_time, time_length)

        # 生成随机码部分
        random_part = "".join(secrets.choice(cls.CHARSET) for _ in range(random_length))

        return time_part + random_part

    @classmethod
    def _encode_base62(cls, number: int, length: int) -> str:
        """将数字编码为指定长度的Base62字符串"""
        if number == 0:
            return cls.CHARSET[0] * length

        # 检查数字是否超出指定长度的表示范围
        max_value = 62**length - 1
        if number > max_value:
            raise ValueError(
                f"Number {number} exceeds maximum value {max_value} for length {length}"
            )

        result = ""
        while number > 0:
            result = cls.CHARSET[number % 62] + result
            number //= 62

        # 左侧补零到指定长度
        while len(result) < length:
            result = cls.CHARSET[0] + result

        return result


# 创建单例实例
account_id_generator = AccountIdGenerator()
