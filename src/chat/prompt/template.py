import os
from datetime import datetime
from typing import Any, Dict, List
from jinja2 import Environment, FileSystemLoader, select_autoescape
from langgraph.prebuilt.chat_agent_executor import AgentState
from langchain_core.messages import BaseMessage

# Initialize Jinja2 environment
env = Environment(
    loader=FileSystemLoader(os.path.dirname(__file__)),
    autoescape=select_autoescape(),
    trim_blocks=True,
    lstrip_blocks=True,
)


def get_prompt_template(prompt_name: str) -> str:
    """
    Load and return a prompt template using Jinja2.

    Args:
        prompt_name: Name of the prompt template file (without .md extension)

    Returns:
        The template string with proper variable substitution syntax
    """
    try:
        template = env.get_template(f"{prompt_name}.md")
        return template.render()
    except Exception as e:
        raise ValueError(f"Error loading template {prompt_name}: {e}")


def apply_prompt_template(
    prompt_name: str, state: AgentState | Any
) -> List[Dict[str, Any]]:
    """
    Apply template variables to a prompt template and return formatted messages.

    Args:
        prompt_name: Name of the prompt template to use
        state: Current agent state containing variables to substitute

    Returns:
        List of messages with the system prompt as the first message
    """
    # Convert state to dict for template rendering
    state_vars = {
        "CURRENT_TIME": datetime.now().strftime("%a %b %d %Y %H:%M:%S %z"),
        **state,
    }

    try:
        template = env.get_template(f"{prompt_name}.md")
        system_prompt = template.render(**state_vars)
        system_message = [{"role": "system", "content": system_prompt}]

        # Convert BaseMessage objects to dict format if needed
        messages = state.get("messages", [])
        converted_messages: List[Dict[str, Any]] = []

        for msg in messages:
            if isinstance(msg, BaseMessage):
                converted_messages.append(
                    {"role": getattr(msg, "type", "user"), "content": msg.content}
                )
            elif isinstance(msg, dict):
                converted_messages.append(msg)
            else:
                # Handle tuple format (role, content)
                if isinstance(msg, (list, tuple)) and len(msg) == 2:
                    converted_messages.append({"role": msg[0], "content": msg[1]})

        return system_message + converted_messages
    except Exception as e:
        raise ValueError(f"Error applying template {prompt_name}: {e}")


def get_system_prompt(prompt_name: str) -> str:
    template = env.get_template(f"{prompt_name}.md")
    variables = {
        "CURRENT_TIME": datetime.now().strftime("%a %b %d %Y %H:%M:%S %z"),
    }
    system_prompt = template.render(**variables)
    return system_prompt


def get_messages(system_prompt: str, user_prompt: str) -> List[Dict[str, Any]]:
    return [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt},
    ]
