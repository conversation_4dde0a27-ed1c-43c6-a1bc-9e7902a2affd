# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from typing import Any
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
import async<PERSON>

from .types import GraphState
from .nodes import coordinator_node, taoist_knowledge_agent, eight_char_agent


def _build_base_graph() -> StateGraph:
    """Build and return the base state graph with all nodes and edges."""
    builder = StateGraph(GraphState)
    builder.add_node("coordinator", coordinator_node)
    builder.add_node("taoist_knowledge_agent", taoist_knowledge_agent)
    builder.add_node("eight_char_agent", eight_char_agent)
    builder.add_edge(START, "coordinator")
    builder.add_edge("taoist_knowledge_agent", END)
    builder.add_edge("eight_char_agent", END)
    return builder


def build_graph_with_memory() -> Any:
    """Build and return the agent workflow graph with memory."""
    # use persistent memory to save conversation history
    # TODO: be compatible with SQLite / PostgreSQL
    memory = MemorySaver()

    # build state graph
    builder = _build_base_graph()
    return builder.compile(checkpointer=memory)


def build_graph() -> Any:
    """Build and return the agent workflow graph without memory."""
    # build state graph
    builder = _build_base_graph()
    return builder.compile()


graph = build_graph()


async def test_graph() -> None:
    async for msg_chunk, metadata in graph.astream(
        input={"messages": [("user", "如何评价道德经")]},
        stream_mode=["messages", "updates"],
    ):
        print(msg_chunk)
        print(metadata)


if __name__ == "__main__":
    # make output as stream
    asyncio.run(test_graph())
