# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import logging

from langchain_core.messages import AIMessage
from langchain_core.runnables import RunnableConfig
from langgraph.types import Command
from langchain_core.tools import tool
from src.chat.llms.llm import get_llm_by_type
from src.chat.prompt.template import apply_prompt_template
from .types import GraphState
from src.config import settings
from typing import Annotated, Literal

logger = logging.getLogger(__name__)


@tool
def handoff_to_specialized_agent(
    agent_name: Annotated[
        str,
        "专门智能体的名称，可选值包括：'taoist_knowledge_agent'（道教知识智能体，用于回答道教经典、历史、修炼、算命）,"
        " 'eight_char_agent'（八字算命智能体，用于回答任何和八字有关的问题）"
        "",
    ],
) -> None:
    """Handoff to specialized agent"""
    # This tool is not returning anything: we're just using it
    # as a way for LLM to signal that it needs to hand off to specialized agent
    return


async def eight_char_agent(
    state: GraphState,
    config: RunnableConfig,
) -> Command[Literal["__end__"]]:
    """Eight char agent that answers questions about eight char."""
    logger.info("Eight char agent talking.")
    return Command(goto="__end__")


async def coordinator_node(
    state: GraphState,
    config: RunnableConfig,
) -> Command[Literal["taoist_knowledge_agent", "eight_char_agent", "__end__"]]:
    """Coordinator node that communicate with customers."""
    logger.info("Coordinator talking.")
    # Convert State to AgentState-compatible format for template
    agent_state = {"messages": state.get("messages", []), **state}
    messages = apply_prompt_template("coordinator", agent_state)
    response = await (
        get_llm_by_type(settings.AGENT_LLM_MAP["coordinator"])
        .bind_tools([handoff_to_specialized_agent])
        .ainvoke(messages, config)
    )
    logger.debug(f"Current state messages: {state['messages']}")
    goto: Literal["taoist_knowledge_agent", "eight_char_agent", "__end__"] = "__end__"

    # Check if response has tool_calls attribute (AIMessage)
    if (
        isinstance(response, AIMessage)
        and hasattr(response, "tool_calls")
        and len(response.tool_calls) > 0
    ):
        try:
            for tool_call in response.tool_calls:
                agent_name = tool_call.get("args", {}).get("agent_name", "")
                logger.info(f"Coordinator handoff to {agent_name}")
                if agent_name == "taoist_knowledge_agent":
                    goto = "taoist_knowledge_agent"
                    break
                elif agent_name == "eight_char_agent":
                    goto = "eight_char_agent"
                    break
                else:
                    logger.warning(f"Unknown agent name: {agent_name}")
        except Exception as e:
            logger.error(f"Error processing tool calls: {e}")
    else:
        logger.warning(
            "Coordinator response contains no tool calls. Terminating workflow execution."
        )
        logger.debug(f"Coordinator response: {response}")

    return Command(
        goto=goto,
    )


async def taoist_knowledge_agent(
    state: GraphState,
    config: RunnableConfig,
) -> Command[Literal["__end__"]]:
    """Taoist knowledge agent that answers questions about Taoist philosophy, history, and practices."""
    logger.info("Taoist knowledge agent talking.")
    # Convert State to AgentState-compatible format for template
    agent_state = {"messages": state.get("messages", []), **state}
    messages = apply_prompt_template("taoist_knowledge_agent", agent_state)
    response = await get_llm_by_type(
        settings.AGENT_LLM_MAP.get("taoist_knowledge_agent", "basic")
    ).ainvoke(messages, config)
    logger.debug(f"Taoist knowledge agent response: {response}")

    return Command(
        update={
            "messages": AIMessage(
                content=response.content, name="taoist_knowledge_agent"
            )
        },
        goto="__end__",
    )
