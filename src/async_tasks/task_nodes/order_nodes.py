"""
订单相关的任务节点函数
从 tasks.py 中提取的内部函数
"""

import json
import traceback
from typing import Dict, Any, List

from src.client.database.bazi_result_client import bazi_result_client
from src.models.database.bazi import BaziResult
from src.services.product_service import product_service
from src.services.user_service import user_service
from src.services.order_service import order_service
from src.services.bazi_manager import bazi_manager
from src.models.bazi_model import (
    BAZIReportRequest,
    PRODUCT_NAME_TO_REPORT_TYPE,
    ReportType,
    PreReportType,
    BAZIResultModel,
)
from src.models.user_model import ProfileParams
from src.utils.constants import OrderStatus, BaziResultStatus
from src.utils.log_util import logger
from src.config import settings
from src.client.oss_client import oss_client
from src.client.database.order_client import order_client

REPORT_FILE_KEY = "yuxugong/{aliyun_bucket_env}/{attempt_id}/bazi_report_{result_type}_{result_id}.json"


async def generate_report_node(
    bazi_request: BAZIReportRequest, existing_calc: BAZIResultModel, result_type: str
) -> BAZIResultModel:
    """
    生成报告的核心逻辑节点

    Args:
        bazi_request: 八字请求对象
        result_type: 报告类型

    Returns:
        计算结果
    """
    if existing_calc.status == BaziResultStatus.SUCCESS:
        return existing_calc

    if existing_calc.status == BaziResultStatus.PENDING:
        await bazi_result_client.update_not_none_element(
            existing_calc.result_id, BaziResult(status=BaziResultStatus.RUNNING.value)
        )
        if result_type == PreReportType.PRE_REPORT.value:
            report_dict = await bazi_manager.get_pre_report(
                bazi_request, existing_calc.attempt_id, existing_calc.result_id
            )

        else:
            report_dict = await bazi_manager.get_report_multi_version(
                bazi_request,
                existing_calc.attempt_id,
                existing_calc.result_id,
                result_type,
            )

        logger.info(f"bazi_report_dict: {report_dict}, result_type = {result_type}")
        bazi_pre_report_str = json.dumps(report_dict, ensure_ascii=False)

    elif existing_calc.status == BaziResultStatus.RUNNING:
        logger.info(
            f"等待 bazi_attempt 完成: result_id={existing_calc.result_id}, attempt_id={existing_calc.attempt_id}"
        )
        bazi_pre_report_str = await bazi_manager.wait_for_report_finished(
            bazi_request, existing_calc.attempt_id, existing_calc.result_id, result_type
        )
        logger.info(f"bazi_pre_report_str: {bazi_pre_report_str}")

    # upload result to oss
    file_key = REPORT_FILE_KEY.format(
        aliyun_bucket_env=settings.ALIYUN_BUCKET_ENV,
        attempt_id=existing_calc.attempt_id,
        result_type=result_type,
        result_id=existing_calc.result_id,
    )
    logger.info(f"upload bazi_pre_report_str to oss: file_key={file_key}")
    await oss_client.write_data(bazi_pre_report_str, file_key)

    # update result status to success
    existing_calc.status = BaziResultStatus.SUCCESS
    existing_calc.output = {
        f"bazi_{result_type}_report_s3_path": file_key,
    }

    bazi_result_to_update = BaziResult(
        status=existing_calc.status,
        output=existing_calc.output,
    )
    await bazi_result_client.update_not_none_element(
        existing_calc.result_id, bazi_result_to_update
    )

    return existing_calc


async def update_order_status_success_node(order_id: str) -> None:
    """
    更新订单状态为成功的节点

    Args:
        order_id: 订单ID
    """
    # 先获取订单信息以获得user_id
    from src.client.database.order_client import order_client

    order = await order_client.get_order_by_id(order_id)
    if not order:
        raise Exception(f"订单不存在: order_id={order_id}")

    await order_service.update_order_status(
        order_id=order_id,
        status=OrderStatus.SUCCESS.value,
        user_id=order.user_id,
    )


async def set_order_failed_status_node(order_id: str) -> None:
    """
    设置订单状态为失败的节点

    Args:
        order_id: 订单ID
    """

    order = await order_client.get_order_by_id(order_id)
    if order:
        await order_service.update_order_status(
            order_id=order_id,
            status=OrderStatus.FAILED.value,
            user_id=order.user_id,
        )


async def get_workflow_data_node(
    product_id: str, profile_id: str, report_version: str
) -> Dict[str, Any]:
    """
    获取工作流数据的节点

    Args:
        product_id: 产品ID
        profile_id: 用户档案ID
        report_version: 报告版本

    Returns:
        Dict[str, Any]: 包含报告类型列表和八字请求数据的字典
    """
    # 1. 获取产品信息和报告类型
    product = await product_service.get_product_by_product_id(product_id)
    if not product:
        raise Exception(f"产品不存在: product_id={product_id}")

    # 确定产品名称（处理sale_type=1的情况）
    product_name = product.product_name
    if product.sale_type == 1:
        product_name = "八字算命-解锁全部报告"

    # 获取报告类型列表
    if product_name not in PRODUCT_NAME_TO_REPORT_TYPE:
        raise Exception(f"产品未配置报告类型: product_name={product_name}")

    report_types = PRODUCT_NAME_TO_REPORT_TYPE[product_name]

    # 对于非1.0版本的全部报告，添加额外的报告类型
    if product_name == "八字算命-解锁全部报告":
        report_types = report_types + [ReportType.BASIC, ReportType.DAYUN]

    # 2. 获取用户档案信息
    profile_params = ProfileParams(profile_id=profile_id, user_id=None)
    profiles = await user_service.get_profiles_by_profile_params(profile_params)
    if not profiles:
        raise Exception(f"用户档案不存在: profile_id={profile_id}")

    profile = profiles[0]

    # 3. 构建八字请求数据
    bazi_request_data = {
        "year": str(profile.birth_year),
        "month": str(profile.birth_month),
        "day": str(profile.birth_day),
        "hour": str(profile.birth_hour),
        "minute": str(profile.birth_minutes),
        "gender": profile.gender,
        "report_version": report_version,
    }

    return {
        "report_types": report_types,
        "bazi_request_data": bazi_request_data,
    }


async def check_existing_reports_async(
    bazi_request_data: Dict[str, Any],
    formal_report_types: List[str],
    order_id: str,
    task_id: str,
) -> tuple[List[Dict[str, Any]], List[str]]:
    """
    异步检查已存在的成功报告，返回已存在的报告和需要生成的报告类型

    Args:
        bazi_request_data: 八字请求数据
        formal_report_types: 需要生成的正式报告类型列表
        order_id: 订单ID
        task_id: 任务ID

    Returns:
        tuple: (已存在的成功报告列表, 需要生成的报告类型列表)
    """
    existing_successful_reports = []
    reports_to_generate = []

    for report_type in formal_report_types:
        try:
            # 检查是否已存在成功的报告
            bazi_request = BAZIReportRequest(**bazi_request_data)
            existing_result = await bazi_manager.find_existing_calculation(
                bazi_request, report_type
            )

            if existing_result and existing_result.status == BaziResultStatus.SUCCESS:
                logger.info(
                    f"跳过已成功的报告: task_id={task_id}, order_id={order_id}, type={report_type}, result_id={existing_result.result_id}"
                )
                existing_successful_reports.append(
                    {
                        "task_id": f"existing_{existing_result.result_id}",
                        "result_type": report_type,
                        "result_id": existing_result.result_id,
                        "status": "success",
                        "order_id": order_id,
                        "skipped": True,  # 标记为跳过的报告
                    }
                )
            else:
                reports_to_generate.append(report_type)

        except Exception as e:
            logger.warning(
                f"检查已存在报告时出错，将重新生成: task_id={task_id}, order_id={order_id}, type={report_type}, error={str(e)}"
            )
            reports_to_generate.append(report_type)

    return existing_successful_reports, reports_to_generate


async def update_failure_status(
    result_id: str | None, order_id: str | None, error: Exception
) -> None:
    """
    更新失败状态的辅助函数

    Args:
        result_id: 结果ID，用于更新bazi_result状态
        order_id: 订单ID，用于更新订单状态
        error: 错误信息
    """
    # 更新bazi_result状态
    if result_id:
        try:
            result_record = await bazi_result_client.get_by_result_id(result_id)
            if result_record:
                result_record.status = BaziResultStatus.FAILED.value
                result_record.error = {
                    "message": str(error),
                    "stacktrace": traceback.format_exc(),
                }
                await bazi_result_client.update_not_none_element(
                    result_id, result_record
                )
        except Exception as update_error:
            logger.error(f"更新bazi_result失败状态时出错: {str(update_error)}")

    # 更新订单状态
    if order_id:
        try:
            await set_order_failed_status_node(order_id)
        except Exception as update_error:
            logger.error(f"更新订单失败状态时出错 {order_id}: {update_error}")
