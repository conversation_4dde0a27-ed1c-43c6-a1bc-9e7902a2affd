from typing import Dict, Any, Optional
from arq import Retry
from src.utils.log_util import logger


async def handle_task_exception(
    e: Exception,
    ctx: Dict[str, Any],
    task_description: str,
    max_tries: int = 4,
) -> None:
    """
    统一的ARQ任务异常处理函数

    Args:
        e: 捕获的异常
        ctx: ARQ任务上下文
        task_description: 任务描述，用于日志
        result_id: 结果ID，用于更新数据库状态（可选）
        order_id: 订单ID，用于更新订单状态（可选）
        max_tries: 最大重试次数，默认4次
        default_defer_seconds: 默认延迟秒数，如果不提供则使用指数退避

    Raises:
        Exception: 重新抛出原异常或Retry异常
    """
    task_id = ctx["job_id"]
    job_try = ctx.get("job_try", 1)

    error_msg = f"{task_description}失败: task_id={task_id}, error={str(e)}"
    logger.error(error_msg, exc_info=True)

    # 处理可重试异常
    if job_try < max_tries:
        logger.info(f"重试{task_description}: task_id={task_id}, 尝试次数={job_try}")
        raise Retry(defer=job_try * 5)
    else:
        logger.error(f"重试次数已用尽，{task_description}最终失败: task_id={task_id}")
        raise e


async def get_job_result_with_status_check(
    job: Any,
    job_description: str,
    timeout: Optional[int] = None,
    poll_delay: float = 1.0,
) -> Any:
    """
    获取ARQ任务结果的健壮方法，包含重试和详细错误处理

    Args:
        job: ARQ Job对象
        job_description: 任务描述，用于日志
        timeout: 超时时间（秒）
        poll_delay: 轮询延迟（秒）

    Returns:
        任务执行结果

    Raises:
        Exception: 当任务失败或结果不可用时
    """
    try:
        # 首先尝试直接获取结果
        result = await job.result(timeout=timeout, poll_delay=poll_delay)
        return result
    except Exception as e:
        # 如果直接获取结果失败，尝试检查任务状态
        try:
            job_status = await job.status()
            logger.error(
                f"获取{job_description}任务结果失败: job_id={job.job_id}, status={job_status}, error={str(e)}"
            )

            # 根据任务状态提供更详细的错误信息
            if job_status == "complete":
                raise Exception(
                    f"{job_description}任务已完成但结果不可用，可能是结果已过期: job_id={job.job_id}"
                )
            elif job_status == "failed":
                raise Exception(f"{job_description}任务执行失败: job_id={job.job_id}")
            elif job_status == "in_progress":
                raise Exception(
                    f"{job_description}任务仍在执行中，可能需要更长时间: job_id={job.job_id}"
                )
            elif job_status == "queued":
                raise Exception(
                    f"{job_description}任务仍在队列中等待执行: job_id={job.job_id}"
                )
            else:
                raise Exception(
                    f"{job_description}任务状态异常: job_id={job.job_id}, status={job_status}, error={str(e)}"
                )
        except Exception as status_error:
            # 如果连状态检查都失败了，抛出原始错误
            logger.error(
                f"检查{job_description}任务状态也失败: job_id={job.job_id}, error={str(status_error)}"
            )
            raise Exception(
                f"{job_description}任务结果获取失败，且无法检查任务状态: {str(e)}"
            )
