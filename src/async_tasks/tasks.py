"""
异步任务定义
包含所有ARQ任务的实现
"""

import asyncio
import time
from typing import Dict, Any

from src.common.arq_redis_pool import get_redis_pool, close_redis_pool
from src.utils.log_util import logger


async def sleep_task(ctx: Dict[str, Any], seconds: int) -> None:
    """
    异步睡眠任务

    Args:
        seconds: 睡眠秒数
    """
    task_id = ctx["job_id"]
    logger.info(f"开始执行sleep任务: {task_id}, 睡眠秒数: {seconds}")
    logger.info(f"begin sleep: {seconds}s")
    await asyncio.sleep(seconds)
    logger.info(f"end sleep: {seconds}s")


async def test_task(
    ctx: Dict[str, Any], message: str = "Hello World"
) -> Dict[str, Any]:
    """
    测试任务

    Args:
        ctx: ARQ context containing job information
        message: 测试消息

    Returns:
        任务执行结果
    """
    task_id = ctx["job_id"]
    logger.info(f"开始执行测试任务: {task_id}, 消息: {message}")

    try:
        # 模拟耗时操作
        redis_pool = await get_redis_pool()

        sleep_task_job = await redis_pool.enqueue_job(
            "sleep_task", 30, _queue_name="dev_queue"
        )
        if not sleep_task_job:
            raise Exception("Failed to enqueue sleep task")

        result = await sleep_task_job.result()
        print(f"sleep_task_job result: {result}")
        if not result:
            raise Exception("Failed to get sleep task result")

        response = {
            "task_id": task_id,
            "message": message,
            "status": "success",
            "timestamp": time.time(),
        }

        logger.info(f"测试任务完成: {task_id}")
        return response

    except Exception as e:
        logger.error(f"测试任务失败: {task_id}, 错误: {str(e)}")
        raise


async def main() -> None:
    """
    Test function to enqueue a task using the optimized Redis pool
    """
    # Import from dedicated Redis pool module

    redis = await get_redis_pool()
    job = await redis.enqueue_job(
        "test_task", message="Hello World", _queue_name="dev_queue"
    )
    if not job:
        raise Exception("Failed to enqueue test task")
    print(f"Task enqueued successfully: {job.job_id}")

    # Clean up (optional for testing)
    await close_redis_pool()


if __name__ == "__main__":
    asyncio.run(main())
