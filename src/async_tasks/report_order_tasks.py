import asyncio
import time
from typing import Dict, Any, List
from arq.worker import Retry
from src.common.arq_redis_pool import get_redis_pool, close_redis_pool
from src.configure.arq_config import arq_config
from src.models.bazi_model import (
    BAZIReportRequest,
    ReportType,
    PreReportType,
)
from src.utils.constants import BaziResultStatus
from src.utils.log_util import logger
from src.async_tasks.task_nodes.order_nodes import (
    generate_report_node,
    update_order_status_success_node,
    get_workflow_data_node,
    check_existing_reports_async,
    update_failure_status,
)
from src.async_tasks.task_nodes.common_nodes import (
    handle_task_exception,
    get_job_result_with_status_check,
)
from src.services.bazi_manager import bazi_manager
from src.common.custom_exceptions import (
    UnretryableException,
    ProductNotAvailableException,
)
from src.utils.arq_task_utils import generate_job_id_with_order_id_and_type

REPORT_GENERATE_TIMEOUT = 900
TIMEOUT_BUFFER = 120
MAX_RETRIES = 3

# Timeout settings for different report types (in seconds)
REPORT_TIMEOUT_MAP = {
    ReportType.BASIC.value: REPORT_GENERATE_TIMEOUT,
    ReportType.DAYUN.value: REPORT_GENERATE_TIMEOUT,
    ReportType.XUEYE.value: REPORT_GENERATE_TIMEOUT,
    ReportType.SHIYE.value: REPORT_GENERATE_TIMEOUT,
    ReportType.YINYUAN.value: REPORT_GENERATE_TIMEOUT,
    ReportType.CAIYUN.value: REPORT_GENERATE_TIMEOUT,
    PreReportType.PRE_REPORT.value: REPORT_GENERATE_TIMEOUT,
}

JOB_RUNTIME_CONFIG = {
    "generate_report": {
        "timeout": REPORT_GENERATE_TIMEOUT + TIMEOUT_BUFFER,
        "max_tries": MAX_RETRIES,
    },
    "generate_parallel_formal_reports": {
        "timeout": MAX_RETRIES * REPORT_GENERATE_TIMEOUT + TIMEOUT_BUFFER,
        "max_tries": MAX_RETRIES,
    },
    "process_order_workflow": {
        "timeout": MAX_RETRIES * MAX_RETRIES * REPORT_GENERATE_TIMEOUT + TIMEOUT_BUFFER,
        "max_tries": MAX_RETRIES,
    },
}


async def generate_report(
    ctx: Dict[str, Any],
    attempt_id: str,
    bazi_request_data: Dict[str, Any],
    result_type: str,
    order_id: str,
    user_id: str,
    profile_id: str,
) -> Dict[str, Any]:
    """
    统一的报告生成任务，支持预报告和正式报告

    Args:
        bazi_request_data: 八字请求数据
        result_type: 报告类型
        order_id: 订单ID
        user_id: 用户ID
        profile_id: 档案ID

    Returns:
        报告生成结果
    """
    task_id = ctx["job_id"]

    # 构建日志参数
    log_params = {
        "task_id": task_id,
        "result_type": result_type,
        "order_id": order_id,
        "attempt_id": attempt_id,
    }
    log_params["user_id"] = user_id
    log_params["profile_id"] = profile_id

    logger.info(f"{result_type} ARQ 任务开始执行: {log_params}")

    result_id = None
    try:
        # 创建BAZIReportRequest对象
        bazi_request = BAZIReportRequest(**bazi_request_data)

        # 检查是否已存在计算结果
        existing_calc = await bazi_manager.find_existing_calculation_by_attempt(
            attempt_id, result_type, bazi_request.report_version or "2.0"
        )

        if not existing_calc:
            existing_calc = await bazi_manager.create_initial_result(
                attempt_id, result_type, bazi_request.report_version or "2.0"
            )
            logger.info(
                f"创建新的 bazi_attempt: result_id={existing_calc.result_id}, attempt_id={existing_calc.attempt_id}"
            )
        result_id = existing_calc.result_id

        # 设置任务超时
        report_generate_timeout = REPORT_TIMEOUT_MAP.get(result_type, 600)

        # 使用asyncio.wait_for添加超时控制
        result = await asyncio.wait_for(
            generate_report_node(bazi_request, existing_calc, result_type),
            timeout=report_generate_timeout,
        )

        if result.status != BaziResultStatus.SUCCESS:
            raise Exception(f"{result_type}生成失败: status={result.status}")

        logger.info(
            f"{result_type}生成成功: task_id={task_id}, result_id={result.result_id}, result_type={result_type}, order_id={order_id}"
        )

        response_data = {
            "task_id": task_id,
            "result_type": result_type,
            "result_id": result.result_id,
            "status": BaziResultStatus.SUCCESS.value,
        }

        return response_data

    except Exception as e:
        if isinstance(e, UnretryableException):
            await update_failure_status(result_id, None, e)
            raise e

        try:
            await handle_task_exception(
                e=e,
                ctx=ctx,
                task_description=f"{result_type}生成任务",
                max_tries=JOB_RUNTIME_CONFIG["generate_report"]["max_tries"],
            )
        except Retry as e:
            logger.info(f"generate_report任务重试: {e}")
            raise e
        except Exception as e:
            logger.error(f"generate_report任务失败: {e}")
            await update_failure_status(result_id, None, e)
            raise e
        raise


async def generate_parallel_formal_reports(
    ctx: Dict[str, Any],
    attempt_id: str,
    bazi_request_data: Dict[str, Any],
    formal_report_types: List[str],
    order_id: str,
    user_id: str,
    profile_id: str,
) -> List[Dict[str, Any]]:
    """
    处理正式报告的并行生成 (ARQ Task)

    Args:
        bazi_request_data: 八字请求数据
        formal_report_types: 需要生成的正式报告类型列表
        order_id: 订单ID

    Returns:
        所有正式报告的生成结果列表
    """
    task_id = ctx["job_id"]
    logger.info(f"开始执行并行正式报告生成任务: task_id={task_id}, order_id={order_id}")

    try:
        if not formal_report_types:
            logger.info(
                f"无正式报告需要生成: task_id={task_id}, order_id={order_id}, user_id={user_id}, profile_id={profile_id}"
            )
            return []

        logger.info(
            f"开始并行生成正式报告: task_id={task_id}, order_id={order_id}, types={formal_report_types}, user_id={user_id}, profile_id={profile_id}"
        )

        # 异步检查已存在的成功报告
        existing_reports, reports_to_generate = await check_existing_reports_async(
            bazi_request_data=bazi_request_data,
            formal_report_types=formal_report_types,
            order_id=order_id,
            task_id=task_id,
        )

        # 使用ARQ任务队列并行执行所有需要生成的正式报告任务
        new_report_results = []
        if reports_to_generate:
            logger.info(
                f"需要生成的新报告: task_id={task_id}, order_id={order_id}, types={reports_to_generate}, user_id={user_id}, profile_id={profile_id}"
            )

            # 获取Redis连接池
            redis_pool = await get_redis_pool()

            # 创建并行ARQ任务列表
            formal_report_jobs = []
            for report_type in reports_to_generate:
                # 为每个报告类型入队ARQ任务
                job_id = generate_job_id_with_order_id_and_type(
                    order_id, "generate_report", report_type
                )
                logger.info(
                    f"入队ARQ任务: job_id={job_id}, report_type={report_type}, order_id={order_id}"
                )
                job = await redis_pool.enqueue_job(
                    "generate_report",
                    attempt_id=attempt_id,
                    bazi_request_data=bazi_request_data,
                    result_type=report_type,
                    order_id=order_id,
                    user_id=user_id,
                    profile_id=profile_id,
                    _queue_name=arq_config.get_queue_name(),
                    _job_id=job_id,
                )
                formal_report_jobs.append((job, report_type))
                logger.info(
                    f"ARQ任务已入队: job_id={job_id}, report_type={report_type}, order_id={order_id}"
                )

            # 等待所有ARQ任务完成并收集结果
            logger.info(
                f"等待新报告生成完成: task_id={task_id}, order_id={order_id}, task_count={len(formal_report_jobs)}, user_id={user_id}, profile_id={profile_id}"
            )

            # 并行等待所有任务完成
            job_results = []
            for job, report_type in formal_report_jobs:
                try:
                    result = await get_job_result_with_status_check(
                        job, f"正式报告-{report_type}"
                    )
                    job_results.append(result)
                except Exception as e:
                    job_id = job.job_id if job else "unknown"
                    logger.error(
                        f"ARQ任务失败: job_id={job_id}, report_type={report_type}, error={str(e)}"
                    )
                    job_results.append(e)

            new_report_results = job_results

            # 检查新生成的报告是否成功，处理异常
            failed_reports = []
            successful_results = []
            for i, result in enumerate(new_report_results):
                if isinstance(result, Exception):
                    failed_reports.append(
                        f"Report {reports_to_generate[i]}: {str(result)}"
                    )
                elif (
                    isinstance(result, dict)
                    and result.get("status") != BaziResultStatus.SUCCESS.value
                ):
                    failed_reports.append(f"Report {reports_to_generate[i]}: {result}")
                else:
                    successful_results.append(result)

            if failed_reports:
                raise Exception(f"部分新报告生成失败: {failed_reports}")

            new_report_results = successful_results
            logger.info(
                f"新报告生成完成: task_id={task_id}, order_id={order_id}, count={len(new_report_results)}, user_id={user_id}, profile_id={profile_id}"
            )

        # 合并已存在的成功报告和新生成的报告
        all_formal_results = existing_reports + new_report_results

        logger.info(
            f"正式报告处理完成: task_id={task_id}, order_id={order_id}, "
            f"总数={len(all_formal_results)}, 跳过={len(existing_reports)}, 新生成={len(new_report_results)}"
        )

        logger.info(
            f"并行正式报告生成任务完成: task_id={task_id}, order_id={order_id}, count={len(all_formal_results)}"
        )
        return all_formal_results

    except Exception as e:
        try:
            await handle_task_exception(
                e=e,
                ctx=ctx,
                task_description="并行正式报告生成任务",
                max_tries=JOB_RUNTIME_CONFIG["generate_parallel_formal_reports"][
                    "max_tries"
                ],
            )
        except Retry as e:
            logger.info(f"generate_parallel_formal_reports 任务重试: {e}")
            raise e
        except Exception as e:
            logger.error(f"generate_parallel_formal_reports 任务失败: {e}")
            raise e
        raise


async def process_order_workflow(
    ctx: Dict[str, Any],
    order_id: str,
    user_id: str,
    profile_id: str,
    product_id: str,
    report_version: str = "1.0",
) -> Dict[str, Any]:
    """
    处理订单工作流的主任务 - 完整的两阶段执行和状态管理：
    1. 第一阶段：同步生成预报告
    2. 第二阶段：预报告成功后并行生成正式报告
    3. 第三阶段：等待所有报告完成，更新订单状态

    Args:
        order_id: 订单ID
        user_id: 用户ID
        profile_id: 用户档案ID
        product_id: 产品ID
        report_version: 报告版本

    Returns:
        工作流处理结果
    """
    task_id = ctx["job_id"]
    logger.info(
        f"开始处理订单工作流: task_id={task_id}, order_id={order_id}, "
        f"product_id={product_id}, user_id={user_id}, profile_id={profile_id}, "
        f"report_version={report_version}"
    )

    try:
        # 获取工作流数据
        workflow_data = await get_workflow_data_node(
            product_id, profile_id, report_version
        )

        report_types = [rt.value for rt in workflow_data["report_types"]]
        logger.info(
            f"工作流数据获取成功: user_id={user_id}, profile_id={profile_id}, "
            f"product_id={product_id}, order_id={order_id}, report_types={report_types}"
        )

        # 检查report_types是否正确
        if not report_types:
            raise ProductNotAvailableException("没有找到需要生成的报告类型")

        # 尝试创建 bazi_attempt
        bazi_attempt = (
            await bazi_manager.create_bazi_attempt_by_date_and_gender_if_not_exists(
                year=int(workflow_data["bazi_request_data"]["year"]),
                month=int(workflow_data["bazi_request_data"]["month"]),
                day=int(workflow_data["bazi_request_data"]["day"]),
                hour=int(workflow_data["bazi_request_data"]["hour"]),
                gender=int(workflow_data["bazi_request_data"]["gender"]),
            )
        )

        logger.info(
            f"bazi_attempt 创建成功: bazi_attempt={bazi_attempt}, "
            f"user_id={user_id}, profile_id={profile_id}, "
            f"product_id={product_id}, order_id={order_id}"
        )

        # 生成 pre-report
        logger.info(
            f"开始生成预报告: user_id={user_id}, profile_id={profile_id}, product_id={product_id}, order_id={order_id}"
        )

        redis_pool = await get_redis_pool()

        pre_report_job = await redis_pool.enqueue_job(
            "generate_report",
            user_id=user_id,
            attempt_id=bazi_attempt.attempt_id,
            profile_id=profile_id,
            order_id=order_id,
            bazi_request_data=workflow_data["bazi_request_data"],
            result_type=PreReportType.PRE_REPORT.value,
            _queue_name=arq_config.get_queue_name(),
            _job_id=generate_job_id_with_order_id_and_type(
                order_id,
                "generate_report",
                PreReportType.PRE_REPORT.value,
            ),
        )

        # 等待预报告完成
        pre_report_data = await get_job_result_with_status_check(
            pre_report_job, "预报告"
        )

        if pre_report_data.get("status") != BaziResultStatus.SUCCESS.value:
            raise Exception(f"预报告生成失败: {pre_report_data}")

        logger.info(
            f"pre_report 生成成功, result_id={pre_report_data.get('result_id')}"
        )

        # 并行生成正式报告
        formal_report_types = [
            rt for rt in report_types if rt != PreReportType.PRE_REPORT.value
        ]

        logger.info(
            f"开始处理正式报告: user_id={user_id}, profile_id={profile_id}, product_id={product_id}, order_id={order_id}, types={formal_report_types}"
        )

        # 使用专门的ARQ任务处理并行正式报告生成（包含跳过已成功报告的优化）
        formal_reports_job = await redis_pool.enqueue_job(
            "generate_parallel_formal_reports",
            attempt_id=bazi_attempt.attempt_id,
            user_id=user_id,
            profile_id=profile_id,
            order_id=order_id,
            bazi_request_data=workflow_data["bazi_request_data"],
            formal_report_types=formal_report_types,
            _queue_name=arq_config.get_queue_name(),
            _job_id=generate_job_id_with_order_id_and_type(
                order_id, "generate_parallel_formal_reports", "all"
            ),
        )

        # 等待并行正式报告任务完成
        formal_results = await get_job_result_with_status_check(
            formal_reports_job, "正式报告"
        )

        logger.info(f"第二阶段完成: 正式报告处理完成, count={len(formal_results)}")

        # 汇总所有报告结果
        all_results = [pre_report_data] + formal_results

        # 更新订单状态为成功
        logger.info(f"第三阶段：更新订单状态为成功: order_id={order_id}")
        await update_order_status_success_node(order_id)

        # 统计报告生成情况
        skipped_reports = [r for r in formal_results if r.get("skipped", False)]
        new_reports = [r for r in formal_results if not r.get("skipped", False)]

        logger.info(
            f"订单工作流完成: order_id={order_id}, 总报告数={len(all_results)}, 跳过={len(skipped_reports)}"
        )

        return {
            "task_id": task_id,
            "order_id": order_id,
            "status": "workflow_completed_successfully",
            "report_types": report_types,
            "reports_generated": len(all_results),
            "pre_report_result": pre_report_data,
            "formal_reports_count": len(formal_results),
            "skipped_reports_count": len(skipped_reports),
            "new_reports_count": len(new_reports),  # +1 for pre-report
            "optimization_summary": {
                "total_reports": len(all_results),
                "skipped_existing_successful": len(skipped_reports),
                "newly_generated": len(new_reports),
                "efficiency_gain": f"{len(skipped_reports)}/{len(all_results)} reports skipped",
            },
            "timestamp": time.time(),
        }

    except Exception as e:
        if isinstance(e, UnretryableException):
            await update_failure_status(None, order_id, e)
            raise e

        try:
            await handle_task_exception(
                e=e,
                ctx=ctx,
                task_description="订单工作流处理",
                max_tries=JOB_RUNTIME_CONFIG["process_order_workflow"]["max_tries"],
            )
        except Retry as e:
            logger.info(f"process_order_workflow 任务重试: {e}")
            raise e
        except Exception as e:
            await update_failure_status(None, order_id, e)
            logger.error(f"process_order_workflow 任务失败: {e}")
            raise e
        raise


async def main() -> None:
    """
    Test function to enqueue a task using the optimized Redis pool
    """

    redis = await get_redis_pool()
    job = await redis.enqueue_job(
        "process_order_workflow",
        order_id="64affa83-e966-4c84-bc37-1bc77d921b46",
        user_id="69d15c3a-da15-4f72-99eb-d0031737d607",
        profile_id="032a3095-a1fa-4a1a-824a-707b08b7a50b",
        product_id="2c3db71f-6d6d-442b-89a8-6b001b74f4ea",
        report_version="2.0",
        _queue_name=arq_config.get_queue_name(),
        _job_id=generate_job_id_with_order_id_and_type(
            "64affa83-e966-4c84-bc37-1bc77d921b46",
            "process_order_workflow",
            "all",
        ),
    )
    if job:
        print(f"Task enqueued successfully: {job.job_id}")
    else:
        print("Warning: enqueue_job returned None")

    # Clean up (optional for testing)
    await close_redis_pool()


if __name__ == "__main__":
    asyncio.run(main())
