{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["personalLoveProfile", "futureLove<PERSON><PERSON><PERSON>", "redThreadConnection", "destinedPartnerProfile", "maritalLifeAnalysis"], "properties": {"personalLoveProfile": {"type": "object", "required": ["title", "items"], "properties": {"title": {"type": "string", "const": "爱情DNA：性格与感情观解析"}, "items": {"type": "array", "minItems": 3, "maxItems": 3, "items": [{"type": "object", "required": ["aspect", "description", "strength", "weakness"], "properties": {"aspect": {"type": "string", "const": "日主特质(您的本质)"}, "description": {"type": "string"}, "strength": {"type": "string"}, "weakness": {"type": "string"}}}, {"type": "object", "required": ["aspect", "description", "spouseExpectation"], "properties": {"aspect": {"type": "string", "const": "夫妻宫(您的内心期盼)"}, "description": {"type": "string"}, "spouseExpectation": {"type": "string"}}}, {"type": "object", "required": ["aspect", "description", "attitude"], "properties": {"aspect": {"type": "string", "const": "十神看感情态度"}, "description": {"type": "string"}, "attitude": {"type": "string"}}}]}}}, "futureLoveJourney": {"type": "object", "required": ["title", "pattern", "periods"], "properties": {"title": {"type": "string", "const": "感情历程：未来爱情故事预演"}, "pattern": {"type": "object", "required": ["title", "description"], "properties": {"title": {"type": "string", "const": "您的感情模式详解"}, "description": {"type": "string"}}}, "periods": {"type": "array", "minItems": 1, "items": {"type": "object", "required": ["<PERSON><PERSON><PERSON><PERSON>", "description", "challenge", "opportunity"], "properties": {"ageRange": {"type": "string"}, "description": {"type": "string"}, "challenge": {"type": "string"}, "opportunity": {"type": "string"}}}}}}, "redThreadConnection": {"type": "object", "required": ["title", "summary", "details"], "properties": {"title": {"type": "string", "const": "红线牵引：您与正缘的关键连结"}, "summary": {"type": "string"}, "details": {"type": "array", "minItems": 1, "items": [{"type": "object", "required": ["title", "content"], "properties": {"title": {"type": "string", "const": "关键生肖匹配"}, "content": {"type": "string"}}}, {"type": "object", "required": ["title", "content"], "properties": {"title": {"type": "string", "const": "五行能量互动"}, "content": {"type": "string"}}}, {"type": "object", "required": ["title", "content"], "properties": {"title": {"type": "string", "const": "灵魂的共鸣点"}, "content": {"type": "string"}}}]}}}, "destinedPartnerProfile": {"type": "object", "required": ["title", "summary", "appearanceDetails", "personalityAndMindset", "backgroundAndAbilities", "encounterInfo"], "properties": {"title": {"type": "string", "const": "您的正缘：天作之合详细描绘"}, "summary": {"type": "string"}, "appearanceDetails": {"type": "object", "required": ["title", "content"], "properties": {"title": {"type": "string", "const": "外貌与气质特征"}, "content": {"type": "string"}}}, "personalityAndMindset": {"type": "object", "required": ["title", "content"], "properties": {"title": {"type": "string", "const": "性格与内心世界"}, "content": {"type": "string"}}}, "backgroundAndAbilities": {"type": "object", "required": ["title", "content"], "properties": {"title": {"type": "string", "const": "背景与能力"}, "content": {"type": "string"}}}, "encounterInfo": {"type": "object", "required": ["title", "content"], "properties": {"title": {"type": "string", "const": "你们如何相遇"}, "content": {"type": "string"}}}}}, "maritalLifeAnalysis": {"type": "object", "required": ["title", "harmonyLevel", "potentialIssues", "happinessFactors", "postMarriageAdvice"], "properties": {"title": {"type": "string", "const": "执子之手：婚后生活幸福度"}, "harmonyLevel": {"type": "string"}, "potentialIssues": {"type": "array", "items": {"type": "string"}}, "happinessFactors": {"type": "array", "items": {"type": "string"}}, "postMarriageAdvice": {"type": "array", "items": {"type": "string"}}}}}}