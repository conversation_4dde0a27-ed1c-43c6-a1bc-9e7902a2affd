import os
from typing import Any
from arq.worker import func
from src.common.session import init_db
from src.utils.log_util import logger
from src.common.arq_redis_pool import redis_pool_manager
from src.client.database.bazi_result_client import mark_running_results_as_failed

# Import all task functions
from src.async_tasks.tasks import test_task, sleep_task
from src.async_tasks.report_order_tasks import (
    generate_report,
    generate_parallel_formal_reports,
    process_order_workflow,
    JOB_RUNTIME_CONFIG,
)
from src.configure.arq_config import arq_config

# 获取环境变量
env = os.getenv("ENV", "dev").lower()


async def startup(_ctx: Any) -> None:
    """ARQ worker startup function"""
    # Log ARQ worker startup with context information
    logger.info("=== ARQ Worker Starting ===")
    logger.info(f"Environment: {env}")
    logger.info(f"ARQ Worker Context: {os.getenv('ARQ_WORKER_CONTEXT', 'false')}")

    await init_db()
    logger.info("Database initialized for ARQ worker")

    # Initialize the Redis pool for the worker
    await redis_pool_manager.get_pool()
    logger.info("Redis pool initialized for ARQ worker")
    logger.info("=== ARQ Worker Startup Complete ===")


async def shutdown(_ctx: Any) -> None:
    """ARQ worker shutdown function"""
    logger.info("=== ARQ Worker Shutting Down ===")

    # Mark all RUNNING bazi_result records as FAILED before shutdown
    try:
        updated_count = await mark_running_results_as_failed()
        if updated_count > 0:
            logger.info(
                f"Marked {updated_count} RUNNING bazi_result records as FAILED during shutdown"
            )
        else:
            logger.info("No RUNNING bazi_result records to update during shutdown")
    except Exception as e:
        logger.error(
            f"Failed to mark RUNNING records as FAILED during shutdown: {str(e)}",
            exc_info=True,
        )
        # Continue with shutdown even if this fails

    # Close the Redis pool
    await redis_pool_manager.close_pool()
    logger.info("Redis pool closed for ARQ worker")
    logger.info("=== ARQ Worker Shutdown Complete ===")


# ARQ Worker Settings
class WorkerSettings:
    """ARQ Worker configuration"""

    # Task functions to register
    functions = [
        func(test_task),
        func(sleep_task),
        func(
            generate_report,
            max_tries=JOB_RUNTIME_CONFIG["generate_report"]["max_tries"],
            timeout=JOB_RUNTIME_CONFIG["generate_report"]["timeout"],
        ),
        func(
            generate_parallel_formal_reports,
            max_tries=JOB_RUNTIME_CONFIG["generate_parallel_formal_reports"][
                "max_tries"
            ],
            timeout=JOB_RUNTIME_CONFIG["generate_parallel_formal_reports"]["timeout"],
        ),
        func(
            process_order_workflow,
            max_tries=JOB_RUNTIME_CONFIG["process_order_workflow"]["max_tries"],
            timeout=JOB_RUNTIME_CONFIG["process_order_workflow"]["timeout"],
        ),
    ]

    # Redis connection settings
    redis_settings = arq_config.get_redis_settings()

    # Worker configuration
    queue_name = arq_config.get_queue_name()
    max_jobs = arq_config.get_worker_settings()["max_jobs"]
    job_timeout = arq_config.get_worker_settings()["job_timeout"]
    keep_result = arq_config.get_worker_settings()["keep_result"]
    keep_result_forever = arq_config.get_worker_settings()["keep_result_forever"]
    poll_delay = arq_config.get_worker_settings()["poll_delay"]
    max_tries = arq_config.get_worker_settings()["max_tries"]
    health_check_interval = arq_config.get_worker_settings()["health_check_interval"]
    retry_jobs = arq_config.get_worker_settings()["retry_jobs"]
    allow_abort_jobs = arq_config.get_worker_settings()["allow_abort_jobs"]

    # Startup and shutdown hooks
    on_startup = startup
    on_shutdown = shutdown


# Convenience functions moved to src.common.arq_redis_pool
