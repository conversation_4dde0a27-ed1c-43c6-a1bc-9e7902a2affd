import pytest
from unittest.mock import AsyncMock, patch
from fastapi.testclient import TestClient
from datetime import datetime

from main import app
from src.models.activity_model import ActivityResponse, ActivityListResponse
from src.common.pagination import PaginationResponse


class TestActivityAPI:
    """Test cases for the activity API endpoint"""
    
    def setup_method(self):
        """Set up test client"""
        self.client = TestClient(app)
        self.test_user_id = "test-user-123"
        self.mock_token = "Bearer test-token"
    
    @patch('src.common.auth.JWTAuth.get_current_user_id')
    @patch('src.services.activity_service.activity_service.get_user_activities')
    def test_get_user_activities_success(self, mock_get_activities, mock_get_user_id):
        """Test successful retrieval of user activities"""
        # Mock authentication
        mock_get_user_id.return_value = self.test_user_id
        
        # Mock service response
        mock_activity = ActivityResponse(
            activity_id="activity-123",
            activity_name="Test Activity",
            description="Test Description",
            activity_type="REGISTER_GIFT",
            activity_start_time=datetime.now(),
            activity_end_time=None,
            activity_status="ACTIVE",
            rules='{"min_level": 1}',
            rewards='{"YUXU_COIN": 100}',
            user_id=self.test_user_id,
            user_activity_id="user-activity-123",
            activity_record_status="SUCCESS",
            reward_details='{"YUXU_COIN": 100}',
            notes="Test participation",
            record_created_at=datetime.now(),
            record_updated_at=datetime.now()
        )
        
        mock_pagination = PaginationResponse(
            total=1,
            page=1,
            limit=20,
            has_next=False,
            has_prev=False
        )
        
        mock_response = ActivityListResponse(
            data=[mock_activity],
            pagination=mock_pagination
        )
        
        mock_get_activities.return_value = mock_response
        
        # Make request
        response = self.client.get(
            "/v1/activity",
            headers={"Authorization": self.mock_token}
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == 0
        assert data["message"] == "success"
        assert "data" in data["data"]
        assert "pagination" in data["data"]
        assert len(data["data"]["data"]) == 1
        assert data["data"]["data"][0]["activity_id"] == "activity-123"
        assert data["data"]["pagination"]["total"] == 1
        
        # Verify service was called with correct parameters
        mock_get_activities.assert_called_once_with(
            user_id=self.test_user_id,
            activity_status=None,
            page=1,
            limit=20
        )
    
    @patch('src.common.auth.JWTAuth.get_current_user_id')
    @patch('src.services.activity_service.activity_service.get_user_activities')
    def test_get_user_activities_with_filters(self, mock_get_activities, mock_get_user_id):
        """Test activity retrieval with filters and pagination"""
        # Mock authentication
        mock_get_user_id.return_value = self.test_user_id
        
        # Mock empty response
        mock_response = ActivityListResponse(
            data=[],
            pagination=PaginationResponse(
                total=0,
                page=2,
                limit=10,
                has_next=False,
                has_prev=True
            )
        )
        mock_get_activities.return_value = mock_response
        
        # Make request with filters
        response = self.client.get(
            "/v1/activity?activity_status=ACTIVE&page=2&limit=10",
            headers={"Authorization": self.mock_token}
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == 0
        assert len(data["data"]["data"]) == 0
        assert data["data"]["pagination"]["page"] == 2
        assert data["data"]["pagination"]["limit"] == 10
        
        # Verify service was called with correct parameters
        mock_get_activities.assert_called_once_with(
            user_id=self.test_user_id,
            activity_status="ACTIVE",
            page=2,
            limit=10
        )
    
    def test_get_user_activities_unauthorized(self):
        """Test activity retrieval without authentication"""
        response = self.client.get("/v1/activity")
        
        # Should return 401 or 422 depending on auth implementation
        assert response.status_code in [401, 422]
    
    @patch('src.common.auth.JWTAuth.get_current_user_id')
    @patch('src.services.activity_service.activity_service.get_user_activities')
    def test_get_user_activities_service_error(self, mock_get_activities, mock_get_user_id):
        """Test handling of service errors"""
        # Mock authentication
        mock_get_user_id.return_value = self.test_user_id
        
        # Mock service error
        mock_get_activities.side_effect = Exception("Database error")
        
        # Make request
        response = self.client.get(
            "/v1/activity",
            headers={"Authorization": self.mock_token}
        )
        
        # Should return 500 error
        assert response.status_code == 500
        data = response.json()
        assert "Failed to get user activities" in data["detail"]
    
    @patch('src.common.auth.JWTAuth.get_current_user_id')
    @patch('src.services.activity_service.activity_service.get_user_activities')
    def test_pagination_parameter_validation(self, mock_get_activities, mock_get_user_id):
        """Test pagination parameter validation"""
        # Mock authentication
        mock_get_user_id.return_value = self.test_user_id
        
        # Mock response
        mock_response = ActivityListResponse(
            data=[],
            pagination=PaginationResponse(
                total=0,
                page=1,
                limit=20,
                has_next=False,
                has_prev=False
            )
        )
        mock_get_activities.return_value = mock_response
        
        # Test with invalid parameters (should be corrected)
        response = self.client.get(
            "/v1/activity?page=0&limit=200",
            headers={"Authorization": self.mock_token}
        )
        
        # Should succeed with corrected parameters
        assert response.status_code == 200
        
        # Verify service was called with corrected parameters
        mock_get_activities.assert_called_once_with(
            user_id=self.test_user_id,
            activity_status=None,
            page=1,  # Corrected from 0 to 1
            limit=100  # Corrected from 200 to 100
        )
