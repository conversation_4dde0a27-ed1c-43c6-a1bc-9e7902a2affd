import asyncio
import os
from typing import Optional, Type, Any
from types import TracebackType
import redis.asyncio as redis
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    before_sleep_log,
)
import logging
from src.utils.log_util import logger


class RedisClient:
    """Redis client for distributed operations"""

    _instance: Optional["RedisClient"] = None
    _redis_client: Optional[redis.Redis] = None
    _initialization_lock = asyncio.Lock()

    def __new__(cls) -> "RedisClient":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self) -> None:
        if not hasattr(self, "_initialized"):
            self._initialized = True
            self._redis_client = None

    async def _get_redis_client(self) -> redis.Redis:
        """Get or create Redis client with lazy initialization and thread safety"""
        if self._redis_client is None:
            async with self._initialization_lock:
                # Double-check pattern to avoid race conditions
                if self._redis_client is None:
                    redis_host = os.getenv("REDIS_HOST")
                    redis_port = int(os.getenv("REDIS_PORT", "6379"))
                    redis_password = os.getenv("REDIS_PASSWORD", "")
                    redis_db = int(os.getenv("REDIS_LOCK_DB", "6"))

                    if not redis_host:
                        raise ValueError("REDIS_HOST environment variable is required")

                    # Build connection URL
                    if redis_password:
                        redis_url = f"redis://:{redis_password}@{redis_host}:{redis_port}/{redis_db}"
                    else:
                        redis_url = f"redis://{redis_host}:{redis_port}/{redis_db}"

                    # Create Redis client with connection pool
                    self._redis_client = redis.Redis.from_url(
                        redis_url,
                        max_connections=20,
                        retry_on_timeout=True,
                        socket_keepalive=True,
                        socket_keepalive_options={},
                        health_check_interval=30,
                        decode_responses=True,
                    )

                    # Do a ping check once after client creation
                    try:
                        result = await self._redis_client.ping()
                        if not result:
                            raise Exception("Redis ping failed after client creation")
                    except Exception as e:
                        logger.error(
                            f"Redis ping failed after client creation: {str(e)}"
                        )
                        await self._redis_client.close()
                        self._redis_client = None
                        raise

                    logger.info(
                        f"Redis client created for {redis_host}:{redis_port}/{redis_db}"
                    )

        return self._redis_client

    async def set_with_expiry(self, key: str, value: str, expiry_seconds: int) -> bool:
        """Set a key with expiry time"""
        try:
            redis_client = await self._get_redis_client()
            result = await redis_client.setex(key, expiry_seconds, value)
            return result is not None
        except Exception as e:
            logger.error(f"Redis set_with_expiry failed for key {key}: {str(e)}")
            return False

    async def set_if_not_exists(
        self, key: str, value: str, expiry_seconds: int
    ) -> bool:
        """Set a key only if it doesn't exist (atomic operation for locking)"""
        try:
            redis_client = await self._get_redis_client()
            # Use SET with NX (only if not exists) and EX (expiry in seconds)
            result = await redis_client.set(key, value, nx=True, ex=expiry_seconds)
            return result is not None
        except Exception as e:
            logger.error(f"Redis set_if_not_exists failed for key {key}: {str(e)}")
            return False

    async def delete_key(self, key: str) -> bool:
        """Delete a key"""
        try:
            redis_client = await self._get_redis_client()
            result = await redis_client.delete(key)
            return int(result) > 0
        except Exception as e:
            logger.error(f"Redis delete_key failed for key {key}: {str(e)}")
            return False

    async def get_value(self, key: str) -> Optional[str]:
        """Get value for a key"""
        try:
            redis_client = await self._get_redis_client()
            result = await redis_client.get(key)
            return result if result is not None else None
        except Exception as e:
            logger.error(f"Redis get_value failed for key {key}: {str(e)}")
            return None

    async def eval(self, script: str, num_keys: int, *keys_and_args: Any) -> Any:
        """Execute a Lua script on Redis server"""
        try:
            redis_client = await self._get_redis_client()
            result = await redis_client.eval(script, num_keys, *keys_and_args)  # type: ignore
            return result
        except Exception as e:
            logger.error(f"Redis eval failed for script: {str(e)}")
            raise

    async def close(self) -> None:
        """Close Redis client and its connection pool"""
        if self._redis_client:
            await self._redis_client.close()
            self._redis_client = None
            logger.info("Redis client closed")

    async def register_script(self, script: str) -> None:
        """Register a Lua script on Redis server"""
        redis_client = await self._get_redis_client()
        redis_client.register_script(script)


class RedisDistributedLock:
    """Redis-based distributed lock implementation"""

    def __init__(
        self,
        redis_client: RedisClient,
        lock_key: str,
        timeout_seconds: int = 20,
        retry_interval: float = 1,
        max_retries: int = 20,
    ):
        self.redis_client = redis_client
        self.lock_key = f"lock:{lock_key}"
        self.timeout_seconds = timeout_seconds
        self.retry_interval = retry_interval
        self.max_retries = max_retries
        current_task = asyncio.current_task()
        if current_task:
            self.lock_value = f"{os.getpid()}:{current_task.get_name()}"
        else:
            self.lock_value = f"{os.getpid()}:unknown"
        self._acquired = False
        self._release_script = """
            local lock_value = redis.call("GET", KEYS[1])
            if lock_value == ARGV[1] then
                return redis.call("DEL", KEYS[1])
            else
                return lock_value
            end
            """

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((redis.ConnectionError, redis.TimeoutError)),
        before_sleep=before_sleep_log(logger, log_level=logging.INFO),
    )
    async def acquire(self, wait_timeout: Optional[float] = None) -> bool:
        """Acquire the distributed lock with comprehensive error handling

        Args:
            wait_timeout: Maximum time to wait for lock (seconds). If None, uses max_retries * retry_interval
        """
        start_time = asyncio.get_event_loop().time()
        max_wait_time = wait_timeout or (self.max_retries * self.retry_interval)

        attempt = 0
        while True:
            # Try to acquire lock using SET NX EX
            acquired = await self.redis_client.set_if_not_exists(
                self.lock_key, self.lock_value, self.timeout_seconds
            )

            if acquired:
                self._acquired = True
                elapsed_time = asyncio.get_event_loop().time() - start_time
                logger.info(
                    f"Acquired distributed lock: {self.lock_key} after {elapsed_time:.2f}s (attempt {attempt + 1})"
                )
                return True

            attempt += 1
            elapsed_time = asyncio.get_event_loop().time() - start_time

            # 检查是否超时或达到最大重试次数
            if elapsed_time >= max_wait_time or attempt >= self.max_retries:
                break

            # 计算下次重试的等待时间，确保不超过总等待时间
            remaining_time = max_wait_time - elapsed_time
            wait_time = min(self.retry_interval, remaining_time)

            if wait_time > 0:
                await asyncio.sleep(wait_time)

        elapsed_time = asyncio.get_event_loop().time() - start_time
        logger.warning(
            f"Failed to acquire lock {self.lock_key} after {attempt} attempts ({elapsed_time:.2f}s)"
        )
        return False

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((redis.ConnectionError, redis.TimeoutError)),
        before_sleep=before_sleep_log(logger, log_level=logging.INFO),
    )
    async def _release_lock_with_retry(self) -> bool:
        """Release lock with retry mechanism"""
        # Use Lua script for atomic check-and-delete
        result = await self.redis_client.eval(
            self._release_script, 1, self.lock_key, self.lock_value
        )

        if result == 1:
            self._acquired = False
            logger.info(f"Released distributed lock: {self.lock_key}")
            return True
        else:
            logger.warning(
                f"Lock {self.lock_key} was not owned by this process or already expired"
            )
            self._acquired = False  # Mark as not acquired since we don't own it
            return False

    async def release(self) -> bool:
        """Release the distributed lock with comprehensive error handling"""
        if not self._acquired:
            return True

        try:
            return await self._release_lock_with_retry()
        except Exception as e:
            # If we couldn't release after all attempts, mark as not acquired to prevent issues
            logger.error(
                f"Failed to release lock {self.lock_key}: {str(e)}",
                exc_info=True,
            )
            self._acquired = False
            return False

    async def __aenter__(self) -> "RedisDistributedLock":
        """Async context manager entry"""
        acquired = await self.acquire()
        if not acquired:
            raise redis.RedisError(
                f"Failed to acquire distributed lock: {self.lock_key}"
            )
        return self

    async def __aexit__(
        self,
        exc_type: Optional[Type[BaseException]],
        exc_val: Optional[BaseException],
        exc_tb: Optional[TracebackType],
    ) -> bool:
        """Async context manager exit"""
        await self.release()
        return False  # Don't suppress exceptions


# Create singleton instance
redis_client = RedisClient()
