from typing import Optional, BinaryIO, Union
import oss2
import asyncio
from src.config import settings
from src.utils.log_util import logger


class OSSClient:
    _instance = None

    def __new__(cls) -> "OSSClient":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self) -> None:
        self.access_key_id = settings.ALIYUN_ACCESS_KEY_ID
        self.access_key_secret = settings.ALIYUN_ACCESS_KEY_SECRET
        self.endpoint = settings.ALIYUN_OSS_ENDPOINT
        self.bucket_name = settings.ALIYUN_BUCKET_NAME

        # Create auth and bucket instances
        self.auth = oss2.Auth(self.access_key_id, self.access_key_secret)
        self.bucket = oss2.Bucket(self.auth, self.endpoint, self.bucket_name)

    async def upload_file(
        self,
        file_data: Union[bytes, BinaryIO],
        file_key: str,
        content_type: Optional[str] = None,
    ) -> str:
        """
        Upload a file to OSS bucket

        Args:
            file_data: The file content in bytes or file-like object
            file_key: The key (path) where the file will be stored in OSS
            content_type: The MIME type of the file

        Returns:
            str: The URL of the uploaded file
        """
        headers = {}
        if content_type:
            headers["Content-Type"] = content_type

        # Run in a thread pool to avoid blocking
        loop = asyncio.get_running_loop()
        await loop.run_in_executor(
            None, lambda: self.bucket.put_object(file_key, file_data, headers=headers)
        )

        url = f"https://{self.bucket_name}.{self.endpoint}/{file_key}"
        logger.info(f"Successfully uploaded file to OSS: {url}")
        return url

    async def download_file(self, file_key: str) -> bytes:
        """
        Download a file from OSS bucket

        Args:
            file_key: The key (path) of the file in OSS

        Returns:
            bytes: The file content
        """
        # Run in a thread pool to avoid blocking
        loop = asyncio.get_running_loop()
        object_stream = await loop.run_in_executor(
            None, lambda: self.bucket.get_object(file_key)
        )

        data = bytes(object_stream.read())
        logger.info(f"Successfully downloaded file from OSS: {file_key}")
        return data

    async def delete_file(self, file_key: str) -> None:
        """
        Delete a file from OSS bucket

        Args:
            file_key: The key (path) of the file to delete
        """
        # Run in a thread pool to avoid blocking
        loop = asyncio.get_running_loop()
        await loop.run_in_executor(None, lambda: self.bucket.delete_object(file_key))

        logger.info(f"Successfully deleted file from OSS: {file_key}")

    async def get_file_url(self, file_key: str, expiration: int = 3600) -> str:
        """
        Generate a presigned URL for a file

        Args:
            file_key: The key (path) of the file in OSS
            expiration: URL expiration time in seconds (default: 1 hour)

        Returns:
            str: Presigned URL for the file
        """
        # Run in a thread pool to avoid blocking
        loop = asyncio.get_running_loop()
        url = await loop.run_in_executor(
            None, lambda: self.bucket.sign_url("GET", file_key, expiration)
        )

        url = url.replace("http://", "https://").replace(
            "ai-smart-thing.oss-cn-beijing-internal",
            "ai-smart-thing.oss-cn-beijing",
        )
        logger.info(f"Generated presigned URL for file: {file_key}")
        return str(url)

    async def write_data(self, data: str, file_key: str) -> str:
        """
        Write data to a file in OSS

        Args:
            data: The data to write
            file_key: The key (path) where the file will be stored in OSS

        Returns:
            str: The URL of the uploaded file
        """
        return await self.upload_file(file_data=data.encode("utf-8"), file_key=file_key)

    async def read_data(self, file_key: str) -> str:
        """
        Read data from a file in OSS

        Args:
            file_key: The key (path) of the file in OSS

        Returns:
            str: The data read from the file
        """
        data = await self.download_file(file_key)
        return data.decode("utf-8")


# Create singleton instance
oss_client = OSSClient()
