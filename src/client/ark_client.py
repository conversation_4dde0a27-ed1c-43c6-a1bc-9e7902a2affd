from typing import Optional, Dict, Any
from volcenginesdkarkruntime import AsyncArk
from src.config import settings
from src.utils.log_util import logger


class ArkClient:
    def __init__(self) -> None:
        self.api_key = settings.ARK_API_KEY
        if not self.api_key:
            raise ValueError("ARK_API_KEY environment variable is not set")

        self.client = AsyncArk(
            api_key=self.api_key,
            base_url=settings.ARK_BASE_URL,
        )

    async def chat_with_agent(
        self,
        agent_id: str,
        user_prompt: str,
        system_message: Optional[str] = None,
        response_format: Optional[str] = None,
        llm_thinking: Optional[str] = None,
    ) -> str:
        if not agent_id:
            raise ValueError("agent_id cannot be None or empty")

        try:
            logger.info(f"开始处理请求 - agent_id: {agent_id}")
            messages = []
            if system_message:
                messages.append({"role": "system", "content": system_message})
                logger.debug(f"添加系统消息: {system_message[:50]}...")

            messages.append({"role": "user", "content": user_prompt})
            logger.debug(f"添加用户消息: {user_prompt[:50]}...")

            # 如果llm_thinking不为None，添加一条大模型消息
            if llm_thinking:
                messages.append({"role": "assistant", "content": llm_thinking})
                logger.debug(f"添加大模型思考消息: {llm_thinking[:50]}...")

            params: Dict[str, Any] = {
                "model": agent_id,
                "messages": messages,
                "stream": True,  # 启用流式输出
                "max_tokens": settings.REASON_MODEL_MAX_TOKENS,
            }

            if response_format:
                params["response_format"] = {"type": response_format}

            # 使用流式调用
            stream = await self.client.bot_chat.completions.create(**params)
            response_content = ""
            log_interval = 100  # 每100个字符打印一次日志
            last_log_length = 0

            # 逐步接收流式响应
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    content_piece = chunk.choices[0].delta.content
                    response_content += content_piece

                    # 每当累积长度超过间隔时打印日志
                    if len(response_content) - last_log_length >= log_interval:
                        logger.info(
                            f"流式输出中... 当前长度: {len(response_content)} 字符"
                        )
                        last_log_length = len(response_content)

            logger.info(f"流式响应完成，总长度: {len(response_content)}")
            return response_content

        except Exception as e:
            logger.error(f"处理请求时发生错误: {str(e)}", exc_info=True)
            raise

    # 实现一个方法，这个方法是流式调用deepseek r1模型，只保留<think></think>中h标签内的内容
    async def get_llm_thinking(
        self,
        model_id: str,
        user_prompt: str,
        system_message: Optional[str] = None,
    ) -> str:
        if not model_id:
            raise ValueError("model_id cannot be None or empty")
        try:
            logger.info(
                f"[get_llm_thinking] 开始处理请求 - model_id: {model_id}, "
                f"user_prompt: {user_prompt[:50]}..., system_message: {system_message[:50] if system_message else ''}..."
            )
            messages = []
            if system_message:
                messages.append({"role": "system", "content": system_message})
                logger.debug(f"添加系统消息: {system_message[:50]}...")
            messages.append({"role": "user", "content": user_prompt})
            logger.debug(f"添加用户消息: {user_prompt[:50]}...")
            stream = await self.client.chat.completions.create(
                model=model_id,
                messages=messages,
                stream=True,
            )
            reasoning_content = ""
            content = ""
            reasoning_finished = False
            # Change from regular for loop to async for loop
            async for chunk in stream:
                if (
                    hasattr(chunk.choices[0].delta, "reasoning_content")
                    and chunk.choices[0].delta.reasoning_content
                ):
                    reasoning_content += chunk.choices[0].delta.reasoning_content
                    reasoning_finished = False
                    print(chunk.choices[0].delta.reasoning_content, end="")
                elif (
                    hasattr(chunk.choices[0].delta, "content")
                    and chunk.choices[0].delta.content
                ):
                    # 如果开始生成正常内容，并且之前已经有推理内容，则认为推理已结束
                    if reasoning_content and not reasoning_finished:
                        reasoning_finished = True
                        # 停止流式生成
                        await stream.close()
                        break
                    content += chunk.choices[0].delta.content
                    logger.info(
                        chunk.choices[0].delta.content,
                    )
            return reasoning_content
        except Exception as e:
            logger.error(f"处理请求时发生错误: {str(e)}", exc_info=True)
            raise


ark_client = ArkClient()
