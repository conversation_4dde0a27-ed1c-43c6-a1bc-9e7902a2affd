from typing import Any, Optional
import asyncio

from appstoreserverlibrary.api_client import (
    AsyncAppStoreServerAPIClient,
)
from appstoreserverlibrary.models.Environment import Environment
from src.client.apple.apple_utils import decode_jws_transaction, private_key
from src.config import settings
from src.utils.log_util import logger


class AppleClient:
    """Apple App Store Server API 异步客户端"""

    def __init__(self) -> None:
        """初始化客户端，创建生产和沙盒环境的异步客户端实例"""
        self.key_id = settings.APPLE_KEY_ID
        self.issuer_id = settings.APPLE_ISSUER_ID
        self.bundle_id = settings.APPLE_BUNDLE_ID

        # 创建生产和沙盒环境的异步客户端
        self._production_client = AsyncAppStoreServerAPIClient(
            private_key,
            self.key_id,
            self.issuer_id,
            self.bundle_id,
            Environment.PRODUCTION,
        )
        self._sandbox_client = AsyncAppStoreServerAPIClient(
            private_key,
            self.key_id,
            self.issuer_id,
            self.bundle_id,
            Environment.SANDBOX,
        )

    def _get_client(
        self, environment: str = "production"
    ) -> AsyncAppStoreServerAPIClient:
        """获取指定环境的异步客户端"""
        if environment.lower() == "production":
            return self._production_client
        else:
            return self._sandbox_client

    async def get_single_transaction_info(
        self, transaction_id: str, environment: str = "production"
    ) -> Optional[dict[str, Any]]:
        """
        获取单个交易信息

        Args:
            transaction_id: 交易ID
            environment: 环境 ("production" 或 "sandbox")

        Returns:
            交易信息或None
        """

        client = self._get_client(environment)
        response = await client.get_transaction_info(transaction_id)

        # 检查 signedTransactionInfo 是否为空
        if response.signedTransactionInfo is None:
            return None

        # 解码签名的交易信息
        decoded_transaction = decode_jws_transaction(response.signedTransactionInfo)

        # 明确返回字典类型
        payload = decoded_transaction.get("payload")
        if isinstance(payload, dict):
            return payload
        return None

    async def get_transaction(self, transaction_id: str) -> Any:
        """
        获取交易信息，先尝试生产环境，失败后尝试沙盒环境

        Args:
            transaction_id: 交易ID

        Returns:
            交易信息或None
        """
        try:
            return await self.get_single_transaction_info(
                transaction_id=transaction_id, environment="production"
            )
        except Exception as e1:
            logger.error(
                f"get_transaction from prod failed: {e1}, transaction_id: {transaction_id}"
            )
            try:
                return await self.get_single_transaction_info(
                    transaction_id=transaction_id, environment="sandbox"
                )
            except Exception as e2:
                logger.error(
                    f"get_transaction from sandbox failed: {e2}, transaction_id: {transaction_id}"
                )
                return None

    async def close(self) -> None:
        """关闭异步客户端连接"""
        try:
            await self._production_client.async_close()
            await self._sandbox_client.async_close()
        except Exception as e:
            logger.warning(f"Error closing Apple clients: {e}")


# 创建全局客户端实例
apple_client = AppleClient()


# 本地调试
async def main() -> None:
    try:
        result = await apple_client.get_transaction("2000000956352745")
        print(result)
    finally:
        # 确保在程序结束时关闭客户端
        await apple_client.close()


if __name__ == "__main__":
    asyncio.run(main())
