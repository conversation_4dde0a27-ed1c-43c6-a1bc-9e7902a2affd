import base64
import json
import time
import os
from typing import Any, Dict

from jose import jwt
from src.config import settings
from src.models.database.orders import Transaction
from src.utils.time_utils import parse_timestamp_to_datetime
from datetime import datetime


def read_private_key() -> bytes:
    """读取Apple私钥文件"""
    private_key_path = os.path.join(
        os.path.dirname(__file__), "SubscriptionKey_2DV5F5KA83.p8"
    )
    with open(private_key_path, "rb") as f:
        return f.read()


class AppStoreJWTConfig:
    def __init__(self) -> None:
        self.key_id = settings.APPLE_KEY_ID
        self.issuer_id = settings.APPLE_ISSUER_ID
        self.bundle_id = settings.APPLE_BUNDLE_ID
        self.private_key = private_key


def generate_app_store_jwt() -> Any:
    config = AppStoreJWTConfig()

    # 创建 header
    headers = {"alg": "ES256", "kid": config.key_id, "typ": "JWT"}

    # 创建 payload
    now = int(time.time())
    payload = {
        "iss": config.issuer_id,
        "iat": now,
        "exp": now + 3600,  # token 有效期 1 小时
        "aud": "appstoreconnect-v1",
        "bid": config.bundle_id,
    }

    # 生成并签名 JWT
    token = jwt.encode(
        claims=payload, key=config.private_key, algorithm="ES256", headers=headers
    )

    return token


def decode_jws_transaction(jws_transaction: str) -> dict:
    """
    解码并验证 JWS Transaction

    Args:
        jws_transaction: JWS 格式的交易信息字符串

    Returns:
        dict: 解码后的交易信息
    """
    try:
        # 将 JWS 字符串分割成三个部分
        header_b64, payload_b64, signature = jws_transaction.split(".")

        # 解码 header
        header_json = base64.urlsafe_b64decode(
            header_b64 + "=" * (-len(header_b64) % 4)
        )
        header = json.loads(header_json)

        # 解码 payload
        payload_json = base64.urlsafe_b64decode(
            payload_b64 + "=" * (-len(payload_b64) % 4)
        )
        payload = json.loads(payload_json)

        # TODO: 验证签名
        # 实际使用时需要使用 Apple 的公钥验证签名
        # 可以从 header 中的 x5c 字段获取证书链

        return {
            "header": header,
            "payload": payload,
            "signatureValid": True,  # 这里需要实际验证签名
        }
    except Exception as e:
        raise ValueError(f"Invalid JWS transaction format: {str(e)}")


def extract_apple_transaction_payload(signed_payload: str) -> Any:
    """
    从苹果签名载荷中提取交易数据

    Args:
        signed_payload: 苹果发送的签名载荷

    Returns:
        dict: 解析后的交易载荷数据

    Raises:
        ValueError: 当载荷数据无效时
    """
    # 解码载荷数据
    decoded_data = decode_jws_transaction(signed_payload)

    # 安全地获取嵌套数据，添加空值检查
    payload_data = decoded_data.get("payload")
    if not payload_data:
        raise ValueError("Invalid decoded data: missing payload")

    data_section = payload_data.get("data")
    if not data_section:
        raise ValueError("Invalid decoded data: missing data section")

    signed_transaction_info = data_section.get("signedTransactionInfo")
    if not signed_transaction_info:
        raise ValueError("Invalid decoded data: missing signedTransactionInfo")

    transaction_data = decode_jws_transaction(signed_transaction_info)
    payload = transaction_data.get("payload")

    if not payload:
        raise ValueError("Invalid payload data")

    return payload


def to_transaction(payload: Dict[str, Any], order_id: str) -> Transaction:
    """
    将苹果支付载荷数据转换为Transaction模型实例

    Args:
        payload: 苹果支付载荷数据
        order_id: 关联的订单ID

    Returns:
        Transaction: 交易模型实例
    """
    # 处理时间戳转换
    purchase_date = parse_timestamp_to_datetime(
        payload.get("purchaseDate"), datetime.now()
    )
    expires_date = parse_timestamp_to_datetime(payload.get("expiresDate"))
    revocation_date = parse_timestamp_to_datetime(payload.get("revocationDate"))

    # 确定交易类型
    transaction_type = payload.get(
        "transactionReason", payload.get("type", "PURCHASE")
    ).lower()

    # 处理环境信息 - 确保首字母大写
    environment = payload.get("environment", "Sandbox")
    if environment.lower() == "sandbox":
        environment = "Sandbox"
    elif environment.lower() == "production":
        environment = "Production"

    # 创建Transaction实例
    transaction = Transaction(
        transaction_id=payload.get("transactionId"),
        original_transaction_id=payload.get("originalTransactionId"),
        transaction_type=transaction_type,
        order_id=order_id,
        environment=environment,
        price=payload.get("price"),
        currency=payload.get("currency", "CNY"),
        purchase_date=purchase_date,
        expires_date=expires_date,
        revocation_date=revocation_date,
        revocation_reason=payload.get("revocationReason"),
    )

    return transaction


private_key = read_private_key()

if __name__ == "__main__":
    print(generate_app_store_jwt())
