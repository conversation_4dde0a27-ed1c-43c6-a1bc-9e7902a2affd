from alibabacloud_fnf20190315.client import Client as fnf20190315Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_fnf20190315 import models as fnf_20190315_models
from alibabacloud_tea_util import models as util_models
from src.config import settings
import json
from typing import Any, Optional, Dict


class AliyunWorkflowClient:
    __instance = None

    def __new__(cls, *args: Any, **kwargs: Any) -> "AliyunWorkflowClient":
        if cls.__instance is None:
            cls.__instance = super().__new__(cls)
        return cls.__instance

    def __init__(self) -> None:
        config = open_api_models.Config(
            # 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。,
            access_key_id=settings.ALIYUN_ACCESS_KEY_ID,
            # 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。,
            access_key_secret=settings.ALIYUN_ACCESS_KEY_SECRET,
        )
        config.endpoint = settings.ALIYUN_WORKFLOW_ENDPOINT
        self.client = fnf20190315Client(config)

    async def start_execution(
        self,
        workflow_name: Optional[str],
        execution_id: Optional[str],
        input: Optional[Dict[str, Any]],
    ) -> None:
        request = fnf_20190315_models.StartExecutionRequest(
            flow_name=workflow_name,
            execution_name=execution_id,
            input=json.dumps(input),
        )
        runtime = util_models.RuntimeOptions()
        await self.client.start_execution_with_options_async(request, runtime)

    async def report_task_success(
        self, task_token: str, output: Optional[Dict[str, Any]] = None
    ) -> None:
        if not output:
            output = {}
        request = fnf_20190315_models.ReportTaskSucceededRequest(
            task_token=task_token,
            output=json.dumps(output),
        )
        runtime = util_models.RuntimeOptions()
        await self.client.report_task_succeeded_with_options_async(request, runtime)

    async def report_task_failed(
        self, task_token: str, error_message: Optional[str] = None
    ) -> None:
        request = fnf_20190315_models.ReportTaskFailedRequest(
            task_token=task_token,
            error=error_message,
        )
        runtime = util_models.RuntimeOptions()
        await self.client.report_task_failed_with_options_async(request, runtime)


if __name__ == "__main__":
    import asyncio

    client = AliyunWorkflowClient()
    execution_id = asyncio.run(
        client.start_execution(
            "report_generation",
            "test_1",
            {
                "gender": 0,
                "year": "1995",
                "month": "1",
                "day": "1",
                "hour": "1",
                "minute": "1",
                "result_type": "yinyuan",
            },
        )
    )
    print(execution_id)
