from typing import Optional, List
from src.models.database.orders import Order
from src.common.session import get_session
from uuid import uuid4
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession
from src.utils.constants import OrderStatus
from src.utils.log_util import logger


class OrderClient:
    _instance = None

    def __new__(cls) -> "OrderClient":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    # Order operations
    async def create_order(self, order: Order) -> Order:
        """Create a new order record"""
        async with get_session() as session:
            if not order.order_id:
                order.order_id = str(uuid4())
            session.add(order)
            await session.commit()
            await session.refresh(order)
            return order

    async def get_order_by_id(self, order_id: str) -> Optional[Order]:
        """Get an order by order_id"""
        async with get_session() as session:
            result = await session.execute(
                select(Order).filter(Order.order_id == order_id)
            )
            return result.scalars().first()

    async def get_order_by_id_for_update(self, order_id: str) -> Optional[Order]:
        """Get an order by order_id with row lock"""
        async with get_session() as session:
            result = await session.execute(
                select(Order).filter(Order.order_id == order_id).with_for_update()
            )
            return result.scalars().first()

    async def batch_get_orders_by_ids_for_update(
        self, order_ids: List[str]
    ) -> List[Order]:
        """Batch get orders by order_ids with row locks"""
        if not order_ids:
            return []

        async with get_session() as session:
            result = await session.execute(
                select(Order).filter(Order.order_id.in_(order_ids)).with_for_update()
            )
            return list(result.scalars().all())

    async def get_order_with_lock(
        self, session: AsyncSession, order_id: str, user_id: Optional[str] = None
    ) -> Optional[Order]:
        """
        查询并锁定订单（在现有事务中）

        Args:
            session: 数据库会话
            order_id: 订单ID
            user_id: 用户ID（可选，用于额外验证）

        Returns:
            Order: 订单对象或None
        """
        query = select(Order).where(Order.order_id == order_id).with_for_update()

        if user_id:
            query = query.where(Order.user_id == user_id)

        result = await session.execute(query)
        return result.scalars().first()

    async def update_order_status(self, order_id: str, status: OrderStatus) -> None:
        """
        更新订单状态

        Args:
            order_id: 订单ID
            status: 新状态
        """
        try:
            async with get_session() as session:
                order = await self.get_order_with_lock(session, order_id)
                if order:
                    order.status = status.value
                    await session.commit()
                    logger.info(f"Order {order_id} status updated to {status.value}")
        except Exception as e:
            logger.error(
                f"Failed to update order {order_id} status to {status.value}: {str(e)}"
            )

    async def get_orders_by_params(self, order_param: Order) -> List[Order]:
        """Get orders by query parameters, only filtering on non-null values"""
        async with get_session() as session:
            # Directly create a filter dict from the Order object
            filter_params = order_param.to_filter_dict()
            # result should be updated desc
            result = await session.execute(
                select(Order)
                .filter_by(**filter_params)
                .order_by(Order.updated_at.desc())
            )
            return list(result.scalars().all())

    async def update_order_by_order_id(self, order_id: str, order: Order) -> Order:
        """Update an order by order_id"""
        async with get_session() as session:
            order_filter = await session.execute(
                select(Order).filter(Order.order_id == order_id)
            )
            existing_order = order_filter.scalars().first()
            if not existing_order:
                raise Exception("order not found")
            existing_order.update(order.to_filter_dict())
            await session.commit()
            await session.refresh(existing_order)
            return existing_order

    async def batch_cancel_orders(self, order_ids: List[str]) -> int:
        """Batch cancel orders to CANCELED status"""
        if not order_ids:
            return 0

        async with get_session() as session:
            # Batch update orders to CANCELED status
            result = await session.execute(
                update(Order)
                .where(Order.order_id.in_(order_ids))
                .values(status=OrderStatus.CANCELED.value)
            )

            await session.commit()
            return result.rowcount


# Create singleton instance
order_client = OrderClient()
