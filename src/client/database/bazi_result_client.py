from typing import Optional, List

from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    before_sleep_log,
)
from src.models.database.bazi import Bazi<PERSON>esult
from src.utils.constants import BaziResultStatus
from src.utils.log_util import logger
from src.common.session import get_session
from uuid import uuid4
from sqlalchemy import select, update
import redis.asyncio as redis
from sqlalchemy.exc import IntegrityError
import logging
from src.client.redis_client import redis_client, RedisDistributedLock


class BaziResultClient:
    _instance = None

    def __new__(cls) -> "BaziResultClient":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def create(self, result: BaziResult) -> BaziResult:
        """Create a new Bazi result record"""
        async with get_session() as session:
            if not result.result_id:
                result.result_id = str(uuid4())
            session.add(result)
            await session.commit()
            return result

    def _get_bazi_attempt_lock_name(
        self, attempt_id: str, result_type: str, version: str
    ) -> str:
        """Generate a unique lock name for the given parameters"""
        return f"bazi_result_{attempt_id}_{result_type}_{version}"

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((redis.RedisError, IntegrityError)),
        before_sleep=before_sleep_log(logger, log_level=logging.INFO),
    )
    async def create_initial_bazi_result_idempotent(
        self, bazi_result: BaziResult
    ) -> BaziResult:
        """
        Create a new Bazi attempt record.
        Use Redis distributed lock to ensure exclusive access during the entire check-and-create process.
        """
        attempt_lock_name = self._get_bazi_attempt_lock_name(
            bazi_result.bazi_attempt_id, bazi_result.result_type, bazi_result.version
        )

        # Create Redis distributed lock with 20 second timeout
        distributed_lock = RedisDistributedLock(
            redis_client=redis_client,
            lock_key=attempt_lock_name,
            timeout_seconds=20,
            retry_interval=0.1,
            max_retries=200,  # 20 seconds total with 0.1s intervals
        )

        try:
            async with distributed_lock:
                logger.info(f"Acquired distributed lock: {attempt_lock_name}")

                async with get_session() as session:
                    # Check for existing record
                    existing_not_failed_result = await session.execute(
                        select(BaziResult)
                        .filter(
                            BaziResult.bazi_attempt_id == bazi_result.bazi_attempt_id,
                            BaziResult.result_type == bazi_result.result_type,
                            BaziResult.version == bazi_result.version,
                            BaziResult.status != BaziResultStatus.FAILED.value,
                        )
                        .order_by(BaziResult.created_at.desc())
                    )
                    existing_result = existing_not_failed_result.scalars().first()

                    if existing_result:
                        logger.info(
                            f"Found existing result under lock: attempt_id={existing_result.bazi_attempt_id}, "
                            f"result_type={existing_result.result_type}, version={existing_result.version}"
                        )
                        return existing_result

                    # No existing record found, create new one
                    if not bazi_result.result_id:
                        bazi_result.result_id = str(uuid4())

                    session.add(bazi_result)
                    await session.commit()

                    logger.info(
                        f"Created new result under lock: attempt_id={bazi_result.bazi_attempt_id}, "
                        f"result_type={bazi_result.result_type}, version={bazi_result.version}"
                    )
        except Exception as e:
            logger.error(
                f"Failed to create bazi_result: {str(e)}",
                exc_info=True,
            )
            raise e
        return bazi_result

    async def get_by_attempt_id(self, attempt_id: str) -> List[BaziResult]:
        """Get all Bazi results for a specific attempt"""
        async with get_session() as session:
            result = await session.execute(
                select(BaziResult).filter(BaziResult.bazi_attempt_id == attempt_id)
            )
            return list(result.scalars().all())

    async def get_latest_by_attempt_id_and_type(
        self, attempt_id: str, result_type: str, version: Optional[str] = None
    ) -> Optional[BaziResult]:
        """Get the latest Bazi result for a specific attempt"""
        async with get_session() as session:
            query = select(BaziResult).filter(
                BaziResult.bazi_attempt_id == attempt_id,
                BaziResult.result_type == result_type,
            )

            if version is not None:
                query = query.filter(BaziResult.version == version)

            result = await session.execute(query.order_by(BaziResult.created_at.desc()))
            return result.scalars().first()

    async def get_by_result_id(self, result_id: str) -> Optional[BaziResult]:
        """Get a Bazi result by its ID"""
        async with get_session() as session:
            result = await session.execute(
                select(BaziResult).filter(BaziResult.result_id == result_id)
            )
            existing_result = result.scalars().first()

            if not existing_result:
                logger.warning(f"No BaziResult found with result_id: {result_id}")
                return None

            return existing_result

    async def update_not_none_element(
        self, result_id: str, result: BaziResult
    ) -> Optional[BaziResult]:
        """Update a Bazi result record if it exists, except for created_at and updated_at"""
        async with get_session() as session:
            query_result = await session.execute(
                select(BaziResult).filter(BaziResult.result_id == result_id)
            )
            existing_result = query_result.scalars().first()

            if not existing_result:
                logger.warning(f"No BaziResult found with result_id: {result_id}")
                return None

            for key, value in result.__dict__.items():
                if value is not None and key not in [
                    "created_at",
                    "updated_at",
                    "_sa_instance_state",
                ]:
                    setattr(existing_result, key, value)

            await session.flush()
            return existing_result


async def mark_running_results_as_failed() -> int:
    """
    Mark all RUNNING bazi_result records as FAILED during worker shutdown.

    Returns:
        int: Number of records updated
    """
    try:
        async with get_session() as session:
            # First, get all RUNNING records to log them
            running_results = await session.execute(
                select(BaziResult).filter(
                    BaziResult.status == BaziResultStatus.RUNNING.value
                )
            )
            running_records = list(running_results.scalars().all())

            if not running_records:
                logger.info("No RUNNING bazi_result records found during shutdown")
                return 0

            # Log the records that will be marked as FAILED
            logger.info(
                f"Found {len(running_records)} RUNNING bazi_result records to mark as FAILED:"
            )
            for record in running_records:
                logger.info(
                    f"  - result_id: {record.result_id}, "
                    f"attempt_id: {record.bazi_attempt_id}, "
                    f"result_type: {record.result_type}, "
                    f"version: {record.version}"
                )

            # Update all RUNNING records to FAILED
            update_result = await session.execute(
                update(BaziResult)
                .where(BaziResult.status == BaziResultStatus.RUNNING.value)
                .values(status=BaziResultStatus.FAILED.value)
            )

            await session.commit()

            updated_count = update_result.rowcount
            logger.info(
                f"Successfully marked {updated_count} RUNNING bazi_result records as FAILED"
            )
            return updated_count

    except Exception as e:
        logger.error(
            f"Error marking RUNNING bazi_result records as FAILED: {str(e)}",
            exc_info=True,
        )
        # Don't raise the exception to avoid blocking the shutdown process
        return 0


# Create singleton instance
bazi_result_client = BaziResultClient()
