from typing import Optional, List
from src.models.database.users import User, UserProfile
from src.common.session import get_session
from uuid import uuid4
from sqlalchemy import select, delete, update
from sqlalchemy.ext.asyncio import AsyncSession
from src.utils.account_id_generator import account_id_generator
from src.utils.log_util import logger
from tenacity import (
    retry,
    retry_if_exception,
    stop_after_attempt,
    before_sleep_log,
)
from sqlalchemy.exc import IntegrityError
import logging


def is_account_id_duplicate_error(exception: BaseException) -> bool:
    """检查是否是account_id重复错误"""
    if isinstance(exception, IntegrityError):
        error_str = str(exception)
        return "account_id" in error_str and (
            "Duplicate" in error_str or "UNIQUE" in error_str
        )
    return False


class UserClient:
    _instance = None

    def __new__(cls) -> "UserClient":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @retry(
        stop=stop_after_attempt(5),
        retry=retry_if_exception(is_account_id_duplicate_error),
        before_sleep=before_sleep_log(logger, log_level=logging.INFO),
    )
    async def create_user(self, user: User) -> User:
        """Create a new user record"""
        async with get_session() as session:
            if not user.user_id:
                user.user_id = str(uuid4())

            # 每次重试都重新生成account_id（确保重试时使用新的ID）
            user.account_id = account_id_generator.generate()

            session.add(user)
            await session.commit()
            await session.refresh(user)
            return user

    @retry(
        stop=stop_after_attempt(5),
        retry=retry_if_exception(is_account_id_duplicate_error),
        before_sleep=before_sleep_log(logger, log_level=logging.INFO),
    )
    async def _update_user_account_id(self, session: AsyncSession, user_id: str) -> str:
        """更新单个用户的account_id（带重试）"""
        # 在方法内部生成account_id，这样每次重试都会生成新的ID
        account_id = account_id_generator.generate()

        await session.execute(
            update(User).filter(User.user_id == user_id).values(account_id=account_id)
        )
        await session.commit()
        return account_id

    async def get_by_account_id(self, account_id: str) -> Optional[User]:
        """通过account_id获取用户"""
        async with get_session() as session:
            result = await session.execute(
                select(User).filter(User.account_id == account_id)
            )
            return result.scalars().first()

    async def get_by_user_id(self, user_id: str) -> Optional[User]:
        """Get a user by user_id"""
        async with get_session() as session:
            result = await session.execute(select(User).filter(User.user_id == user_id))
            return result.scalars().first()

    async def get_by_user_id_with_session(
        self, session: AsyncSession, user_id: str
    ) -> Optional[User]:
        """Get a user by user_id with session"""
        result = await session.execute(select(User).filter(User.user_id == user_id))
        return result.scalars().first()

    async def get_by_phone_number(self, phone_number: str) -> Optional[User]:
        """Get a user by phone number"""
        async with get_session() as session:
            result = await session.execute(
                select(User).filter(User.phone_number == phone_number)
            )
            return result.scalars().first()

    async def get_by_apple_user_id(self, apple_user_id: str) -> Optional[User]:
        """Get a user by apple_user_id"""
        async with get_session() as session:
            result = await session.execute(
                select(User).filter(User.apple_user_id == apple_user_id)
            )
            return result.scalars().first()

    async def create_profile(self, profile: UserProfile) -> UserProfile:
        """Create a new user profile"""
        async with get_session() as session:
            if not profile.profile_id:
                profile.profile_id = str(uuid4())
            session.add(profile)
            await session.commit()
            await session.refresh(profile)
            return profile

    async def get_profiles_by_user_id(self, user_id: str) -> List[UserProfile]:
        """Get all profiles for a user"""
        async with get_session() as session:
            result = await session.execute(
                select(UserProfile).filter(UserProfile.user_id == user_id)
            )
            return list(result.scalars().all())

    async def get_profile_by_id(self, profile_id: str) -> Optional[UserProfile]:
        """Get a profile by profile_id"""
        async with get_session() as session:
            result = await session.execute(
                select(UserProfile).filter(UserProfile.profile_id == profile_id)
            )
            return result.scalars().first()

    async def get_profile_by_profile_params(
        self, profile_params: UserProfile
    ) -> List[UserProfile]:
        """Get a profile by profile parameters, only filtering on non-null values"""
        async with get_session() as session:
            # Filter out None values from the parameters
            filter_params = profile_params.to_filter_dict()

            result = await session.execute(
                select(UserProfile)
                .filter_by(**filter_params)
                .order_by(UserProfile.updated_at.desc())
            )

            profiles = result.scalars().all()
            return list(profiles)

    async def delete_profile(self, profile_id: str, user_id: str) -> None:
        """Delete a profile by profile_id and user_id"""
        async with get_session() as session:
            await session.execute(
                delete(UserProfile).filter(
                    UserProfile.profile_id == profile_id, UserProfile.user_id == user_id
                )
            )
            await session.commit()

    async def delete_profiles(self, profile_ids: List[str], user_id: str) -> None:
        """Batch delete profiles by profile_ids and user_id"""
        async with get_session() as session:
            await session.execute(
                delete(UserProfile).filter(
                    UserProfile.profile_id.in_(profile_ids),
                    UserProfile.user_id == user_id,
                )
            )
            await session.commit()

    async def delete_profiles_by_ids(
        self, session: AsyncSession, profile_ids: List[str]
    ) -> None:
        """Batch delete profiles by profile_ids only (no user_id filter)"""
        if not profile_ids:
            return

        await session.execute(
            delete(UserProfile).filter(UserProfile.profile_id.in_(profile_ids))
        )

    async def update_user(self, user: User) -> User:
        """Update an existing user record"""
        async with get_session() as session:
            session.add(user)  # SQLAlchemy will track changes to the 'user' object
            await session.commit()
            await session.refresh(user)
            return user

    async def delete_user(self, user_id: str) -> bool:
        """Delete a user record only (profiles should be deleted separately)"""
        async with get_session() as session:
            result = await session.execute(delete(User).filter(User.user_id == user_id))
            await session.commit()

            # Return True if user was deleted, False if user didn't exist
            return result.rowcount > 0

    async def update_profile(
        self, profile_id: str, user_id: str, name: str, role_group: str
    ) -> Optional[UserProfile]:
        """Update a profile's name and role_group by profile_id and user_id"""
        async with get_session() as session:
            result = await session.execute(
                select(UserProfile).filter(
                    UserProfile.profile_id == profile_id, UserProfile.user_id == user_id
                )
            )
            profile = result.scalars().first()
            if not profile:
                return None
            profile.name = name
            profile.role_group = role_group
            await session.refresh(profile)
            return profile


# Create singleton instance
user_client = UserClient()
