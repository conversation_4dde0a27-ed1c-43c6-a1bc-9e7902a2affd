from typing import Optional

from sqlalchemy import select, func
from src.common.session import get_session
from src.models.database.activity import Activity, ActivityStatus, ActivityType


class ActivityClient:
    _instance = None

    def __new__(cls) -> "ActivityClient":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def get_activity_by_type(
        self, activity_type: ActivityType
    ) -> Optional[Activity]:
        async with get_session() as session:
            result = await session.execute(
                select(Activity)
                .where(
                    Activity.activity_type == activity_type.value,
                    Activity.status == ActivityStatus.ACTIVE.value,
                    Activity.start_time <= func.now(),
                    Activity.end_time >= func.now(),
                )
                .order_by(Activity.start_time.desc())
            )
            return result.scalars().first()


activity_client = ActivityClient()
