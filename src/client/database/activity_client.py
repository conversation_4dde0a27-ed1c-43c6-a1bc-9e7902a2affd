from typing import Optional, List, Tuple
from datetime import datetime

from sqlalchemy import select, func, and_, or_, outerjoin
from sqlalchemy.ext.asyncio import AsyncSession
from src.common.session import get_session
from src.models.database.activity import Activity, ActivityStatus, ActivityType
from src.models.database.activity_record import ActivityRecord
from src.common.pagination import PaginationHelper


class ActivityClient:
    _instance = None

    def __new__(cls) -> "ActivityClient":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def get_activity_by_type(
        self, activity_type: ActivityType
    ) -> Optional[Activity]:
        async with get_session() as session:
            result = await session.execute(
                select(Activity)
                .where(
                    Activity.activity_type == activity_type.value,
                    Activity.status == ActivityStatus.ACTIVE.value,
                    Activity.start_time <= func.now(),
                    Activity.end_time >= func.now(),
                )
                .order_by(Activity.start_time.desc())
            )
            return result.scalars().first()

    async def get_activities_with_user_records(
        self,
        user_id: str,
        activity_status: Optional[str] = None,
        page: int = 1,
        limit: int = 20,
        session: Optional[AsyncSession] = None
    ) -> Tuple[List[Tuple[Activity, Optional[ActivityRecord]]], int]:
        """
        Get activities with user participation records, with pagination

        Args:
            user_id: User ID to get records for
            activity_status: Optional filter by activity status
            page: Page number (1-based)
            limit: Items per page
            session: Optional database session

        Returns:
            Tuple of (list of (Activity, ActivityRecord) tuples, total_count)
        """
        async def _execute_query(db_session: AsyncSession) -> Tuple[List[Tuple[Activity, Optional[ActivityRecord]]], int]:
            # Build base query with left join
            base_query = (
                select(Activity, ActivityRecord)
                .outerjoin(
                    ActivityRecord,
                    and_(
                        Activity.activity_id == ActivityRecord.activity_id,
                        ActivityRecord.user_id == user_id
                    )
                )
            )

            # Apply activity status filter if provided
            if activity_status:
                base_query = base_query.where(Activity.status == activity_status)

            # Filter for active/valid activities (not expired, within time range)
            now = datetime.now()
            base_query = base_query.where(
                and_(
                    Activity.status.in_(['ACTIVE', 'PLANNED']),
                    Activity.start_time <= now,
                    or_(Activity.end_time.is_(None), Activity.end_time >= now)
                )
            )

            # Order by activity start time descending
            base_query = base_query.order_by(Activity.start_time.desc())

            # Get total count
            count_query = select(func.count(Activity.id)).select_from(
                base_query.subquery()
            )
            total_result = await db_session.execute(count_query)
            total = total_result.scalar() or 0

            # Apply pagination
            offset = (page - 1) * limit
            paginated_query = base_query.offset(offset).limit(limit)

            # Execute query
            result = await db_session.execute(paginated_query)
            rows = result.all()

            # Convert to list of tuples
            activities_with_records = [(row[0], row[1]) for row in rows]

            return activities_with_records, total

        if session:
            return await _execute_query(session)
        else:
            async with get_session() as db_session:
                return await _execute_query(db_session)

    async def get_active_activities(
        self,
        activity_status: Optional[str] = None,
        session: Optional[AsyncSession] = None
    ) -> List[Activity]:
        """
        Get all active/valid activities

        Args:
            activity_status: Optional filter by activity status
            session: Optional database session

        Returns:
            List of Activity objects
        """
        async def _execute_query(db_session: AsyncSession) -> List[Activity]:
            query = select(Activity)

            # Apply activity status filter if provided
            if activity_status:
                query = query.where(Activity.status == activity_status)
            else:
                # Default to active activities
                query = query.where(Activity.status.in_(['ACTIVE', 'PLANNED']))

            # Filter for activities within time range
            now = datetime.now()
            query = query.where(
                and_(
                    Activity.start_time <= now,
                    or_(Activity.end_time.is_(None), Activity.end_time >= now)
                )
            )

            # Order by start time descending
            query = query.order_by(Activity.start_time.desc())

            result = await db_session.execute(query)
            return result.scalars().all()

        if session:
            return await _execute_query(session)
        else:
            async with get_session() as db_session:
                return await _execute_query(db_session)


activity_client = ActivityClient()
