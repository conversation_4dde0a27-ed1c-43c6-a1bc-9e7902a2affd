from typing import Optional
from datetime import datetime
from src.models.database.verification_codes import Verification<PERSON>ode
from src.common.session import get_session
from uuid import uuid4
from sqlalchemy import select, update


class VerificationCodeClient:
    _instance = None

    def __new__(cls) -> "VerificationCodeClient":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def create_verification_code(
        self, verification_code: VerificationCode
    ) -> VerificationCode:
        """Create a new verification code record"""
        async with get_session() as session:
            if not verification_code.verification_id:
                verification_code.verification_id = str(uuid4())

            session.add(verification_code)
            await session.commit()
            await session.refresh(verification_code)
            return verification_code

    async def get_valid_verification_code_by_phone_number(
        self, phone_number: str
    ) -> Optional[VerificationCode]:
        """Get the most recent non-expired verification code for a phone number"""
        async with get_session() as session:
            current_time = datetime.now()
            result = await session.execute(
                select(VerificationCode)
                .filter(
                    VerificationCode.phone_number == phone_number,
                    VerificationCode.expires_at > current_time,
                )
                .order_by(VerificationCode.created_at.desc())
            )
            return result.scalars().first()

    async def get_valid_verification_code_by_verification_id(
        self, verification_id: str
    ) -> Optional[VerificationCode]:
        """Get a verification code by verification_id (non-expired only)"""
        async with get_session() as session:
            current_time = datetime.now()
            result = await session.execute(
                select(VerificationCode).filter(
                    VerificationCode.verification_id == verification_id,
                    VerificationCode.expires_at > current_time,
                )
            )
            return result.scalars().first()

    async def update_verification_code(
        self, verification_code: VerificationCode
    ) -> VerificationCode:
        """Update an existing verification code record"""
        async with get_session() as session:
            session.add(verification_code)
            await session.commit()
            await session.refresh(verification_code)
            return verification_code

    async def mark_as_used(self, verification_id: str) -> bool:
        """Mark a verification code as used (only for non-expired codes)"""
        async with get_session() as session:
            result = await session.execute(
                update(VerificationCode)
                .filter(VerificationCode.verification_id == verification_id)
                .values(is_used=True)
            )
            await session.commit()
            return result.rowcount > 0


# Create singleton instance
verification_code_client = VerificationCodeClient()
