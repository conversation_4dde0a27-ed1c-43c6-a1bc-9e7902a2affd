from typing import List, Optional
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession
from src.models.database.activity_record import ActivityRecord
from src.common.session import get_session


class ActivityRecordClient:
    """Database client for activity_record table operations"""
    
    _instance = None

    def __new__(cls) -> "ActivityRecordClient":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def get_user_activity_records(
        self, 
        user_id: str,
        activity_ids: Optional[List[str]] = None,
        session: Optional[AsyncSession] = None
    ) -> List[ActivityRecord]:
        """
        Get user activity records, optionally filtered by activity IDs
        
        Args:
            user_id: User ID to filter by
            activity_ids: Optional list of activity IDs to filter by
            session: Optional database session
            
        Returns:
            List of ActivityRecord objects
        """
        async def _execute_query(db_session: AsyncSession) -> List[ActivityRecord]:
            query = select(ActivityRecord).where(ActivityRecord.user_id == user_id)
            
            if activity_ids:
                query = query.where(ActivityRecord.activity_id.in_(activity_ids))
            
            result = await db_session.execute(query)
            return result.scalars().all()
        
        if session:
            return await _execute_query(session)
        else:
            async with get_session() as db_session:
                return await _execute_query(db_session)

    async def get_activity_record_by_user_and_activity(
        self,
        user_id: str,
        activity_id: str,
        session: Optional[AsyncSession] = None
    ) -> Optional[ActivityRecord]:
        """
        Get a specific activity record by user ID and activity ID
        
        Args:
            user_id: User ID
            activity_id: Activity ID
            session: Optional database session
            
        Returns:
            ActivityRecord object or None if not found
        """
        async def _execute_query(db_session: AsyncSession) -> Optional[ActivityRecord]:
            query = select(ActivityRecord).where(
                and_(
                    ActivityRecord.user_id == user_id,
                    ActivityRecord.activity_id == activity_id
                )
            )
            result = await db_session.execute(query)
            return result.scalars().first()
        
        if session:
            return await _execute_query(session)
        else:
            async with get_session() as db_session:
                return await _execute_query(db_session)

    async def create_activity_record(
        self,
        activity_record: ActivityRecord,
        session: Optional[AsyncSession] = None
    ) -> ActivityRecord:
        """
        Create a new activity record
        
        Args:
            activity_record: ActivityRecord object to create
            session: Optional database session
            
        Returns:
            Created ActivityRecord object
        """
        async def _execute_query(db_session: AsyncSession) -> ActivityRecord:
            db_session.add(activity_record)
            await db_session.commit()
            await db_session.refresh(activity_record)
            return activity_record
        
        if session:
            return await _execute_query(session)
        else:
            async with get_session() as db_session:
                return await _execute_query(db_session)


# Singleton instance
activity_record_client = ActivityRecordClient()
