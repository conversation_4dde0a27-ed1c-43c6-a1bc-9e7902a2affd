from typing import Optional, List
from datetime import date
from sqlalchemy import select, func
from src.models.database.user_query import UserQuery
from src.common.session import get_session


class UserQueryClient:
    _instance = None

    def __new__(cls) -> "UserQueryClient":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def create_query(self, user_id: str, query: str) -> UserQuery:
        """Create a new user query record"""
        async with get_session() as session:
            user_query = UserQuery(user_id=user_id, query=query)
            session.add(user_query)
            await session.commit()
            await session.refresh(user_query)
            return user_query

    async def get_today_query_count(self, user_id: str) -> int:
        """Get today's query count for a specific user"""
        today = date.today()
        async with get_session() as session:
            result = await session.execute(
                select(func.count(UserQuery.id)).filter(
                    UserQuery.user_id == user_id,
                    func.date(UserQuery.created_time) == today,
                )
            )
            return result.scalar() or 0

    async def get_user_queries(
        self, user_id: str, limit: int = 100, offset: int = 0
    ) -> List[UserQuery]:
        """Get user queries with pagination"""
        async with get_session() as session:
            result = await session.execute(
                select(UserQuery)
                .filter(UserQuery.user_id == user_id)
                .order_by(UserQuery.created_time.desc())
                .limit(limit)
                .offset(offset)
            )
            return list(result.scalars().all())

    async def get_query_by_id(self, query_id: int) -> Optional[UserQuery]:
        """Get a query by id"""
        async with get_session() as session:
            result = await session.execute(
                select(UserQuery).filter(UserQuery.id == query_id)
            )
            return result.scalars().first()


# Create singleton instance
user_query_client = UserQueryClient()
