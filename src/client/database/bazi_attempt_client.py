from typing import Optional

from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    before_sleep_log,
)
from src.models.database.bazi import BaziAttempt
from src.common.session import get_session
from uuid import uuid4
from sqlalchemy import select
from src.utils.log_util import logger
from src.client.redis_client import redis_client, RedisDistributedLock
import redis.asyncio as redis
from sqlalchemy.exc import IntegrityError
import logging


class BaziAttemptClient:
    _instance = None

    def __new__(cls) -> "BaziAttemptClient":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def _get_lock_name(
        self, year: int, month: int, day: int, hour: int, gender: int
    ) -> str:
        """Generate a unique lock name for the given parameters"""
        return f"bazi_attempt_{year}_{month}_{day}_{hour}_{gender}"

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((redis.RedisError, IntegrityError)),
        before_sleep=before_sleep_log(logger, log_level=logging.INFO),
    )
    async def create_attempt_idempotent(self, attempt: BaziAttempt) -> BaziAttempt:
        """
        Create a new Bazi attempt record.
        Use Redis distributed lock to ensure exclusive access during the entire check-and-create process.
        """
        lock_name = self._get_lock_name(
            attempt.year, attempt.month, attempt.day, attempt.hour, attempt.gender
        )

        # Create Redis distributed lock with 20 second timeout
        distributed_lock = RedisDistributedLock(
            redis_client=redis_client,
            lock_key=lock_name,
            timeout_seconds=20,
            retry_interval=0.1,
            max_retries=200,  # 20 seconds total with 0.1s intervals
        )

        try:
            async with distributed_lock:
                logger.info(f"Acquired distributed lock: {lock_name}")

                async with get_session() as session:
                    # Check for existing record
                    existing_result = await session.execute(
                        select(BaziAttempt).filter(
                            BaziAttempt.year == attempt.year,
                            BaziAttempt.month == attempt.month,
                            BaziAttempt.day == attempt.day,
                            BaziAttempt.hour == attempt.hour,
                            BaziAttempt.gender == attempt.gender,
                        )
                    )
                    existing_attempt = existing_result.scalars().first()

                    if existing_attempt:
                        logger.info(
                            f"Found existing attempt under lock: attempt_id={existing_attempt.attempt_id}, "
                            f"year={attempt.year}, month={attempt.month}, day={attempt.day}, "
                            f"hour={attempt.hour}, gender={attempt.gender}"
                        )
                        return existing_attempt

                    # No existing record found, create new one
                    if not attempt.attempt_id:
                        attempt.attempt_id = str(uuid4())

                    session.add(attempt)
                    await session.commit()

                    logger.info(
                        f"Created new attempt under lock: attempt_id={attempt.attempt_id}, "
                        f"year={attempt.year}, month={attempt.month}, day={attempt.day}, "
                        f"hour={attempt.hour}, gender={attempt.gender}"
                    )
        except Exception as e:
            logger.error(
                f"Failed to create attempt: {str(e)}",
                exc_info=True,
            )
            raise e
        return attempt

    async def get_by_attempt_id(self, attempt_id: str) -> Optional[BaziAttempt]:
        """Get a Bazi attempt by attempt_id"""
        async with get_session() as session:
            result = await session.execute(
                select(BaziAttempt).filter(BaziAttempt.attempt_id == attempt_id)
            )
            return result.scalars().first()

    async def get_by_date_and_gender(
        self, year: int, month: int, day: int, hour: int, gender: int
    ) -> Optional[BaziAttempt]:
        """Get a Bazi attempt by date and gender"""
        async with get_session() as session:
            result = await session.execute(
                select(BaziAttempt).filter(
                    BaziAttempt.year == year,
                    BaziAttempt.month == month,
                    BaziAttempt.day == day,
                    BaziAttempt.hour == hour,
                    BaziAttempt.gender == gender,
                )
            )
            return result.scalars().first()


# Create singleton instance
bazi_attempt_client = BaziAttemptClient()
