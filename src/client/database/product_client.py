from src.models.database.products import Product
from src.common.session import get_session
from sqlalchemy import select, update
from typing import Optional, List, Dict, Any

from src.models.product_model import ProductGroup


class ProductClient:
    _instance = None

    def __new__(cls) -> "ProductClient":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def get_active_product_by_product_id(
        self, product_id: str
    ) -> Optional[Product]:
        async with get_session() as session:
            result = await session.execute(
                select(Product).filter(
                    Product.product_id == product_id, Product.is_active == 1
                )
            )
            return result.scalars().first()

    async def get_product_by_product_id(self, product_id: str) -> Optional[Product]:
        """
        获取产品

        Args:
            product_id: 产品ID

        Returns:
            Product: 产品对象
        """
        async with get_session() as session:
            result = await session.execute(
                select(Product).where(Product.product_id == product_id)
            )
            return result.scalars().first()

    async def get_all_products(
        self, product_group: Optional[ProductGroup] = None
    ) -> List[Product]:
        """Get all products from the database"""
        async with get_session() as session:
            if product_group:
                result = await session.execute(
                    select(Product)
                    .where(Product.product_group == product_group.value)
                    .where(Product.is_active == 1)
                    .order_by(Product.created_at.asc())
                )
            else:
                result = await session.execute(select(Product))
            return list(result.scalars().all())

    async def get_active_products(self) -> List[Product]:
        """Get all active products from the database"""
        async with get_session() as session:
            result = await session.execute(
                select(Product).filter(Product.is_active == 1)
            )
            return list(result.scalars().all())

    async def update_product_by_product_id(
        self, product_id: str, update_data: Dict[str, Any]
    ) -> bool:
        """Update a product by product_id"""
        async with get_session() as session:
            # Remove None values and fields that shouldn't be updated
            filtered_data = {
                k: v
                for k, v in update_data.items()
                if v is not None and k not in ["id", "created_at"]
            }

            if not filtered_data:
                return False

            result = await session.execute(
                update(Product)
                .filter(Product.product_id == product_id)
                .values(**filtered_data)
            )
            await session.commit()
            return result.rowcount > 0

    async def create_product(self, product: Product) -> Product:
        """Create a new product"""
        async with get_session() as session:
            session.add(product)
            await session.commit()
            await session.refresh(product)
            return product


product_client = ProductClient()
