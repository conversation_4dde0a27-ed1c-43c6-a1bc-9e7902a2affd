from typing import Optional
from src.models.database.user_wallet import UserWallet
from src.models.database.yuxucoin_transaction import YuxuCoinTransaction
from src.common.session import get_session
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
import uuid


class UserWalletClient:
    _instance = None

    def __new__(cls) -> "UserWalletClient":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def get_by_user_id(self, user_id: str) -> Optional[UserWallet]:
        """
        根据用户ID获取用户钱包

        Args:
            user_id: 用户ID

        Returns:
            UserWallet: 用户钱包对象或None
        """
        async with get_session() as session:
            result = await session.execute(
                select(UserWallet).where(UserWallet.user_id == user_id)
            )
            return result.scalars().first()

    async def get_user_wallet_with_lock(
        self, session: AsyncSession, user_id: str
    ) -> Optional[UserWallet]:
        """
        获取用户钱包并锁定（在现有事务中）

        Args:
            session: 数据库会话
            user_id: 用户ID

        Returns:
            UserWallet: 用户钱包对象

        """
        result = await session.execute(
            select(UserWallet).where(UserWallet.user_id == user_id).with_for_update()
        )
        return result.scalars().first()

    async def create_user_wallet(self, user_wallet: UserWallet) -> UserWallet:
        """
        创建用户钱包

        Args:
            user_wallet: 用户钱包对象

        Returns:
            UserWallet: 创建的用户钱包对象
        """
        async with get_session() as session:
            session.add(user_wallet)
            await session.commit()
            await session.refresh(user_wallet)
            return user_wallet

    async def update_user_wallet_balance(
        self, session: AsyncSession, user_id: str, new_balance: int
    ) -> None:
        """
        更新用户钱包余额（在现有事务中）

        Args:
            session: 数据库会话
            user_id: 用户ID
            new_balance: 新余额

        Raises:
            ValueError: 当用户钱包不存在时
        """
        user_wallet = await self.get_user_wallet_with_lock(session, user_id)
        if user_wallet is None:
            raise ValueError(f"User wallet not found for user {user_id}")
        user_wallet.balance = new_balance

    async def update_wallet_balance_with_transaction(
        self,
        session: AsyncSession,
        user_id: str,
        amount: int,
        transaction_type: str,
        related_entity_type: str,
        related_entity_id: Optional[str],
        description: Optional[str],
    ) -> tuple[UserWallet, YuxuCoinTransaction]:
        """
        更新用户钱包余额并创建交易记录（在现有事务中）

        Args:
            session: 数据库会话
            user_id: 用户ID
            amount: 交易金额（正数为增加，负数为减少）
            transaction_type: 交易类型
            related_entity_type: 关联实体类型
            related_entity_id: 关联实体ID
            description: 交易描述

        Returns:
            tuple[UserWallet, YuxuCoinTransaction]: 更新后的钱包对象和创建的交易记录

        Raises:
            ValueError: 当用户钱包不存在时
        """
        # 获取用户钱包并加锁
        user_wallet = await self.get_user_wallet_with_lock(session, user_id)
        if not user_wallet:
            # 如果钱包不存在，先创建钱包
            user_wallet = UserWallet(
                user_id=user_id,
                balance=0,
                wallet_id=str(uuid.uuid4()),
            )
            session.add(user_wallet)
            await session.flush()  # 刷新以获取ID

        # 计算新的余额
        balance_before = user_wallet.balance
        balance_after = balance_before + amount

        # 创建交易记录
        yuxu_coin_transaction = YuxuCoinTransaction(
            transaction_log_id=str(uuid.uuid4()),
            wallet_id=user_wallet.wallet_id,
            user_id=user_id,
            amount=amount,
            balance_before=balance_before,
            balance_after=balance_after,
            transaction_type=transaction_type,
            related_entity_type=related_entity_type,
            related_entity_id=related_entity_id,
            description=description,
        )

        # 更新钱包余额
        user_wallet.balance = balance_after

        # 添加到会话
        session.add(yuxu_coin_transaction)
        session.add(user_wallet)

        return user_wallet, yuxu_coin_transaction


# Create singleton instance
user_wallet_client = UserWalletClient()
