from src.models.database.workflow import WorkflowExecution, WorkflowOrder
from src.common.session import get_session
from uuid import uuid4
from sqlalchemy import select
from typing import Optional, Tuple, List
from src.models.database.orders import Order


class WorkflowClient:
    _instance = None

    def __new__(cls) -> "WorkflowClient":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def create_workflow(self, workflow: WorkflowExecution) -> WorkflowExecution:
        async with get_session() as session:
            if not workflow.workflow_execution_id:
                workflow.workflow_execution_id = str(uuid4())
            session.add(workflow)
            await session.commit()
            await session.refresh(workflow)
            return workflow

    async def get_workflow_by_execution_id(
        self, execution_id: str
    ) -> Optional[WorkflowExecution]:
        async with get_session() as session:
            result = await session.execute(
                select(WorkflowExecution).filter(
                    WorkflowExecution.workflow_execution_id == execution_id
                )
            )
            return result.scalars().first()

    async def update_workflow_by_execution_id(
        self, execution_id: str, workflow: WorkflowExecution
    ) -> WorkflowExecution:
        async with get_session() as session:
            result = await session.execute(
                select(WorkflowExecution).filter(
                    WorkflowExecution.workflow_execution_id == execution_id
                )
            )
            workflow_execution = result.scalars().first()
            if not workflow_execution:
                raise ValueError("Workflow execution not found")
            workflow_execution.update(workflow.to_filter_dict())
            await session.commit()
            await session.refresh(workflow_execution)
            return workflow_execution

    async def bind_order_with_workflow(
        self, workflow_execution_id: str, order_id: str
    ) -> WorkflowOrder:
        async with get_session() as session:
            workflow_order = WorkflowOrder(
                workflow_execution_id=workflow_execution_id, order_id=order_id
            )
            session.add(workflow_order)
            await session.commit()
            await session.refresh(workflow_order)
            return workflow_order

    async def get_workflow_with_order(
        self, workflow_execution_id: str
    ) -> Tuple[Optional[WorkflowExecution], Optional[List[Order]]]:
        async with get_session() as session:
            # First get the workflow_order relationship
            workflow_order_query = select(WorkflowOrder).filter(
                WorkflowOrder.workflow_execution_id == workflow_execution_id
            )
            result = await session.execute(workflow_order_query)
            workflow_order = result.scalars().all()
            order_ids = [order.order_id for order in workflow_order]

            # Get the workflow execution
            workflow_query = select(WorkflowExecution).filter(
                WorkflowExecution.workflow_execution_id == workflow_execution_id
            )
            workflow_result = await session.execute(workflow_query)
            workflow_execution = workflow_result.scalars().first()

            # If there's no relation, return just the workflow
            if not workflow_order:
                return workflow_execution, None

            # Get all order using order_id from the relation
            order_query = (
                select(Order)
                .filter(Order.order_id.in_(order_ids))
                .order_by(Order.created_at.desc())
            )
            order_result = await session.execute(order_query)
            orders = order_result.scalars().all()

            return workflow_execution, list(orders)
