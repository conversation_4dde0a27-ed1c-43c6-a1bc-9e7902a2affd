from typing import Dict, Any
import asyncio
import requests

from src.utils.log_util import logger


class SpugSMSClient:
    _instance = None

    def __new__(cls) -> "SpugSMSClient":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self) -> None:
        # Spug push URL is fixed
        self.push_url = "https://push.spug.cc/send/v3aDVjBl1M8JeX9M"

    async def send_verification_code(
        self, phone_number: str, code: str
    ) -> Dict[str, Any]:
        """
        Send verification code SMS using Spug push service

        Args:
            phone_number: The phone number to send to
            code: The verification code

        Returns:
            Dict[str, Any]: The response from Spug
        """
        # Fixed template parameters
        template_params = {
            "key1": "玉虚宫",  # Fixed app name
            "key2": code,  # Verification code
            "key3": "5",  # Valid for 5 minutes
        }

        logger.info(f"Sending verification code {code} to {phone_number}")
        return await self._send_request(phone_number, template_params)

    async def _send_request(
        self, phone_number: str, params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Send SMS through Spug push service

        Args:
            phone_number: The phone number to send to
            params: The template parameters

        Returns:
            Dict[str, Any]: The response from Spug
        """
        # Create request payload for Spug API
        payload = {"targets": phone_number, **params}

        # Run in a thread pool to avoid blocking
        loop = asyncio.get_running_loop()
        try:
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(
                    self.push_url,
                    json=payload,
                    headers={"Content-Type": "application/json"},
                ),
            )

            response_data: Dict[str, Any] = response.json()

            if response.status_code == 200 and response_data.get("code") != 204:
                logger.info(f"Successfully sent SMS to {phone_number}")
            else:
                error_msg = response_data.get("msg", "Unknown error")
                logger.error(f"Failed to send SMS: {error_msg}")

            return response_data
        except Exception as e:
            logger.error(f"Error sending SMS: {str(e)}")
            raise


# Create singleton instance
spug_sms_client = SpugSMSClient()

if __name__ == "__main__":
    asyncio.run(spug_sms_client.send_verification_code("17751781262", "123456"))
