version: '3.8'

networks:
  app-network:
    driver: overlay # 将 'bridge' 修改为 'overlay'
    attachable: true   # 建议加上这个，允许独立容器连接到此网络，方便调试


services:
  # 你的应用服务
  yuxugong:
    image: crpi-2794svloicid00on-vpc.cn-beijing.personal.cr.aliyuncs.com/ai-smart-thing/yuxugong:${TAG}
    restart: unless-stopped
    environment:
      - env=${ENV}
      - ENV=${ENV}
      - TZ=Asia/Shanghai
    volumes:
      - ./logs:/app/logs
    command: bash /app/deploy/start.sh
    # 关键：定义部署和更新策略
    healthcheck:
      # test: 用来检查健康的命令。必须在容器内部执行。
      # curl --fail 会在 HTTP 响应码为 4xx 或 5xx 时返回错误 (exit code 22)，从而标记检查失败。
      # 5000 是你容器内部应用监听的端口。
      test: ["CMD", "curl", "--fail", "http://localhost:5000/v1/app/health"]
      interval: 15s       # 每 15 秒检查一次
      timeout: 5s         # 每次检查的超时时间为 5 秒
      retries: 10        # 连续 3 次失败后，将容器标记为 "unhealthy"
      start_period: 600s   # 启动宽限期：容器启动后的 600 秒内，即使检查失败也不会计入重试次数。
                          # 这对于启动慢的应用至关重要！
    networks:
      - app-network

  nginx:
    image: crpi-2794svloicid00on-vpc.cn-beijing.personal.cr.aliyuncs.com/ai-smart-thing/nginx:latest
    container_name: nginx_proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      # 挂载 SSL 证书
      - /u/certs:/etc/nginx/certs:ro
      - ./logs/nginx:/var/log/nginx
    restart: unless-stopped
    depends_on:
      - yuxugong
    networks:
      - app-network

  arq-ui:
    image: crpi-2794svloicid00on-vpc.cn-beijing.personal.cr.aliyuncs.com/ai-smart-thing/arq-ui:latest
    restart: unless-stopped
    # No external port mapping - accessed through nginx reverse proxy
    healthcheck:
      test: ["CMD", "true"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - app-network
