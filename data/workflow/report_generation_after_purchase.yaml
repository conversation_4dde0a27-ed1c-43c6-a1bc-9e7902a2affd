Type: StateMachine
Name: report_generation_after_purchase
SpecVersion: v1
StartAt: Parallel
States:
  - Type: Parallel
    Name: Parallel
    Branches:
      - StartAt: get_report_type_by_product_id
        States:
          - Type: Task
            Name: get_report_type_by_product_id
            Action: HTTP:Request
            TaskMode: RequestComplete
            Parameters:
              timeout: 60
              method: GET
              url.$: >-
                format("http://39.106.93.51:5000/v1/products/{}/report-types",
                $Input.product_id)
            OutputConstructor:
              data.$: stringTo<PERSON>son($Output.Body)
            End: true
      - StartAt: get_user_profile
        States:
          - Type: Task
            Name: get_user_profile
            Action: HTTP:Request
            TaskMode: RequestComplete
            Parameters:
              timeout: 60
              method: GET
              headers:
                Authorization.$: format("Bearer {}",$Input.jwt_token)
              query:
                profile_id.$: $Input.profile_id
              url.$: >-
                format("http://39.106.93.51:5000/v1/users/profiles/{}",
                $Input.profile_id)
            OutputConstructor:
              data.$: stringToJson($Output.Body)
            End: true
    Next: basic_report_generate
    OutputConstructor:
      data.$: jsonMerge($Output.Branch1.data, $Output.Branch0.data)
  - Type: Task
    Name: basic_report_generate
    Action: FNF:StartExecution
    TaskMode: WaitForSystemCallback
    Parameters:
      resourceArn: acs:fnf:cn-beijing:1919451260043932:flow/report_generation
      input:
        gender.$: $Input.data.gender
        year.$: $Input.data.birth_year
        month.$: $Input.data.birth_month
        day.$: $Input.data.birth_day
        hour.$: $Input.data.birth_hour
        minute.$: $Input.data.birth_minutes
        result_type: basic
    Next: is_basic_only
    OutputConstructor:
      report_types.$: $Input.data.report_types
      gender.$: $Input.data.gender
      year.$: $Input.data.birth_year
      month.$: $Input.data.birth_month
      day.$: $Input.data.birth_day
      hour.$: $Input.data.birth_hour
      minute.$: $Input.data.birth_minutes
      basic_report.$: $Output.data
  - Type: Choice
    Name: is_basic_only
    Branches:
      - Condition: $Input.report_types[0]=="basic"
        Next: Succeed
    Default: dayun_report_generate
  - Type: Task
    Name: dayun_report_generate
    Action: FNF:StartExecution
    TaskMode: WaitForSystemCallback
    Parameters:
      resourceArn: acs:fnf:cn-beijing:1919451260043932:flow/report_generation
      input:
        gender.$: $Input.gender
        year.$: $Input.year
        month.$: $Input.month
        day.$: $Input.day
        hour.$: $Input.hour
        minute.$: $Input.minute
        result_type: dayun
    Next: is_dayun_only
    OutputConstructor:
      report_types.$: $Input.report_types
      gender.$: $Input.gender
      year.$: $Input.year
      month.$: $Input.month
      day.$: $Input.day
      hour.$: $Input.hour
      minute.$: $Input.minute
      basic_report.$: $Input.basic_report
      dayun_report.$: $Output.data
  - Type: Choice
    Name: is_dayun_only
    Branches:
      - Condition: $Input.report_types[0]=="dayun"
        Next: Succeed
    Default: Map
  - Type: Map
    Name: Map
    ProcessorConfig:
      ExecutionMode: Standard
    Processor:
      StartAt: advance_report_generate
      States:
        - Type: Task
          Name: advance_report_generate
          Action: FNF:StartExecution
          TaskMode: WaitForSystemCallback
          Parameters:
            resourceArn: acs:fnf:cn-beijing:1919451260043932:flow/report_generation
            input:
              gender.$: $Input.gender
              year.$: $Input.year
              month.$: $Input.month
              day.$: $Input.day
              hour.$: $Input.hour
              minute.$: $Input.minute
              result_type.$: $Input.result_type
          End: true
          OutputConstructor:
            $: $Output.data
    ItemsPath: $Input.report_types
    ItemConstructor:
      gender.$: $Input.gender
      year.$: $Input.year
      month.$: $Input.month
      day.$: $Input.day
      hour.$: $Input.hour
      minute.$: $Input.minute
      result_type.$: $Item
    OutputConstructor:
      report_types.$: $Input.report_types
      gender.$: $Input.gender
      year.$: $Input.year
      month.$: $Input.month
      day.$: $Input.day
      hour.$: $Input.hour
      minute.$: $Input.minute
      basic_report.$: $Input.basic_report
      dayun_report.$: $Input.dayun_report
      advanced_reports.$: $Output.Items
    Next: Succeed
  - Type: Succeed
    Name: Succeed
    End: true
