Type: StateMachine
Name: report_generation
SpecVersion: v1
StartAt: StartJob
States:
  - Type: Task
    Name: StartJob
    Action: HTTP:Request
    TaskMode: RequestComplete
    Parameters:
      method: POST
      url.$: >-
        format("http://39.106.93.51:5000/v1/bazi/report/{}/async",
        $Input.result_type)
      body:
        year.$: $Context.Execution.Input.year
        month.$: $Context.Execution.Input.month
        day.$: $Context.Execution.Input.day
        hour.$: $Context.Execution.Input.hour
        minute.$: $Context.Execution.Input.minute
        gender.$: $Context.Execution.Input.gender
      headers:
        Content-Type: application/json
    Next: Choice
    OutputConstructor:
      data.$: stringToJson($Output.Body)
  - Type: Wait
    Name: Wait X Seconds
    Next: get_job_status
    Seconds: 30
    OutputConstructor:
      $: $Input
  - Type: Task
    Name: get_job_status
    Action: HTTP:Request
    TaskMode: RequestComplete
    Parameters:
      method: GET
      url.$: >-
        format("http://39.106.93.51:5000/v1/bazi/report/{}",
        $Input.data.result_id)
    Next: Job Complete?
    OutputConstructor:
      data.$: stringToJson($Output.Body)
  - Type: Choice
    Name: Job Complete?
    Branches:
      - Condition: $Input.data.status=="SUCCESS"
        Next: Succeed
      - Condition: $Input.data.status=="PENDING" || $Input.data.status=="RUNNING"
        Next: Wait X Seconds
    Default: Fail
  - Type: Fail
    Name: Fail
    Code: failed
    End: true
  - Type: Succeed
    Name: Succeed
    End: true
  - Type: Choice
    Name: Choice
    Branches:
      - Condition: $Input.data.status == "SUCCESS"
        Next: get_job_status
    Default: Wait X Seconds
