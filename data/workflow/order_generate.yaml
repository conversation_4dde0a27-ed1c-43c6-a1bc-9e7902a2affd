Type: StateMachine
Name: order_generate
SpecVersion: v1
StartAt: generate_report_after_purchase
States:
  - Type: Task
    Name: generate_report_after_purchase
    Action: FNF:StartExecution
    TaskMode: WaitForSystemCallback
    Parameters:
      resourceArn: >-
        acs:fnf:cn-beijing:1919451260043932:flow/report_generation_after_purchase
      input:
        profile_id.$: $Context.Execution.Input.profile_id
        product_id.$: $Context.Execution.Input.product_id
        jwt_token.$: $Context.Execution.Input.jwt_token
    Timeout: 1200
    Retry:
      - Errors:
          - FnF.ALL
        Description: '重试策略 #1'
        MaxAttempts: 3
        IntervalSeconds: 10
        BackoffRate: 2
    Catch:
      - Errors:
          - FnF.ALL
        Description: '错误捕获规则 #1'
        OutputConstructor:
          order_id.$: $Input.order_id
        Next: update_order_failed
    Next: update_order_success
  - Type: Task
    Name: update_order_failed
    Action: HTTP:Request
    TaskMode: RequestComplete
    Parameters:
      timeout: 300
      method: PUT
      url.$: >-
        format("http://************:5000/v1/order_info/{}",
        $Context.Execution.Input.order_id)
      headers:
        Authorization.$: format("Bearer {}",$Context.Execution.Input.jwt_token)
        Content-Type: application/json
      body:
        status: FAILED
    Next: Fail
  - Type: Task
    Name: update_order_success
    Action: HTTP:Request
    TaskMode: RequestComplete
    Parameters:
      timeout: 300
      method: PUT
      url.$: >-
        format("http://************:5000/v1/order_info/{}",
        $Context.Execution.Input.order_id)
      headers:
        Authorization.$: format("Bearer {}",$Context.Execution.Input.jwt_token)
        Content-Type: application/json
      body:
        status: SUCCESS
    Next: Succeed
  - Type: Succeed
    Name: Succeed
    End: true
  - Type: Fail
    Name: Fail
    Code: generate_report_after_purchase_failed
    Detail: 生成报告失败
    End: true
