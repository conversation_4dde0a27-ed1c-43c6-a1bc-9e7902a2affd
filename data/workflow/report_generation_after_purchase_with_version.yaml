Type: StateMachine
Name: report_generation_after_purchase_with_version
SpecVersion: v1
StartAt: Parallel
States:
  - Type: Parallel
    Name: Parallel
    Branches:
      - StartAt: get_report_type_by_product_id
        States:
          - Type: Task
            Name: get_report_type_by_product_id
            Action: HTTP:Request
            TaskMode: RequestComplete
            Parameters:
              timeout: 60
              method: GET
              url.$: >-
                format("https://yuxugong.ai-smart-thing.cloud/v1/products/{}/report-types?report_version={}",
                $Input.product_id, $Context.Execution.Input.report_version)
            OutputConstructor:
              data.$: stringTo<PERSON>son($Output.Body)
            End: true
      - StartAt: get_user_profile
        States:
          - Type: Task
            Name: get_user_profile
            Action: HTTP:Request
            TaskMode: RequestComplete
            Parameters:
              timeout: 60
              method: GET
              headers:
                Authorization.$: format("Bearer {}",$Input.jwt_token)
              query:
                profile_id.$: $Input.profile_id
              url.$: >-
                format("https://yuxugong.ai-smart-thing.cloud/v1/users/profiles/{}",
                $Input.profile_id)
            OutputConstructor:
              data.$: stringTo<PERSON>son($Output.Body)
            End: true
    Next: Map
    OutputConstructor:
      data.$: jsonMerge($Output.Branch1.data, $Output.Branch0.data)
  - Type: Map
    Name: Map
    ProcessorConfig:
      ExecutionMode: Standard
    Processor:
      StartAt: report_generate_with_version
      States:
        - Type: Task
          Name: report_generate_with_version
          Action: FNF:StartExecution
          TaskMode: WaitForSystemCallback
          Parameters:
            resourceArn: acs:fnf:cn-beijing:1919451260043932:flow/report_generation_with_version
            input:
              gender.$: $Input.gender
              year.$: $Input.year
              month.$: $Input.month
              day.$: $Input.day
              hour.$: $Input.hour
              minute.$: $Input.minute
              result_type.$: $Input.result_type
              report_version.$: $Input.report_version
          End: true
          OutputConstructor:
            $: $Output.data
    ItemsPath: $Input.data.report_types
    ItemConstructor:
      gender.$: $Input.data.gender
      year.$: $Input.data.birth_year
      month.$: $Input.data.birth_month
      day.$: $Input.data.birth_day
      hour.$: $Input.data.birth_hour
      minute.$: $Input.data.birth_minutes
      result_type.$: $Item
      report_version.$: $Context.Execution.Input.report_version
    OutputConstructor: 
      report_types.$: $Input.data.report_types
      gender.$: $Input.data.gender
      year.$: $Input.data.birth_year
      month.$: $Input.data.birth_month
      day.$: $Input.data.birth_day
      hour.$: $Input.data.birth_hour
      minute.$: $Input.data.birth_minutes
      report_version.$: $Context.Execution.Input.report_version
      advanced_reports.$: $Output.Items
    Next: Succeed
  - Type: Succeed
    Name: Succeed
    End: true
