Type: StateMachine
Name: order_generate_with_version
SpecVersion: v1
StartAt: generate_report_after_purchase
States:
  - Type: Task
    Name: generate_report_after_purchase
    Action: FNF:StartExecution
    TaskMode: WaitForSystemCallback
    Parameters:
      resourceArn: >-
        acs:fnf:cn-beijing:1919451260043932:flow/report_generation_after_purchase_with_version
      input:
        profile_id.$: $Context.Execution.Input.profile_id
        product_id.$: $Context.Execution.Input.product_id
        jwt_token.$: $Context.Execution.Input.jwt_token
        report_version.$: $Context.Execution.Input.report_version
    Timeout: 1800
    Retry:
      - Errors:
          - FnF.ALL
        Description: '重试策略 #1'
        MaxAttempts: 3
        IntervalSeconds: 20
        BackoffRate: 2
    Catch:
      - Errors:
          - FnF.ALL
        Description: '错误捕获规则 #1'
        OutputConstructor:
          order_id.$: $Input.order_id
        Next: update_order_failed
    Next: update_order_success
  - Type: Task
    Name: update_order_failed
    Action: HTTP:Request
    TaskMode: RequestComplete
    Parameters:
      timeout: 300
      method: PUT
      url.$: >-
        format("https://yuxugong.ai-smart-thing.cloud/v1/order_info/{}",
        $Context.Execution.Input.order_id)
      headers:
        Authorization.$: format("Bearer {}",$Context.Execution.Input.jwt_token)
        Content-Type: application/json
      body:
        status: FAILED
    Timeout: 600
    Retry:
      - Errors:
          - FnF.ALL
        Description: 'http request failed, most likely due to server restart'
        MaxAttempts: 5
        IntervalSeconds: 20
        BackoffRate: 2
    Next: Fail
  - Type: Task
    Name: update_order_success
    Action: HTTP:Request
    TaskMode: RequestComplete
    Parameters:
      timeout: 300
      method: PUT
      url.$: >-
        format("https://yuxugong.ai-smart-thing.cloud/v1/order_info/{}",
        $Context.Execution.Input.order_id)
      headers:
        Authorization.$: format("Bearer {}",$Context.Execution.Input.jwt_token)
        Content-Type: application/json
      body:
        status: SUCCESS
    Timeout: 600
    Retry:
      - Errors:
          - FnF.ALL
        Description: 'http request failed, most likely due to server restart'
        MaxAttempts: 5
        IntervalSeconds: 20
        BackoffRate: 2
    Next: Succeed
  - Type: Succeed
    Name: Succeed
    End: true
  - Type: Fail
    Name: Fail
    Code: generate_report_after_purchase_failed
    Detail: 生成报告失败
    End: true
