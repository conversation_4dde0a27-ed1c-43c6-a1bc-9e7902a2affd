Type: StateMachine
Name: report_generation_with_version
SpecVersion: v1
StartAt: StartJob
States:
  - Type: Task
    Name: StartJob
    Action: HTTP:Request
    TaskMode: RequestComplete
    Parameters:
      method: POST
      url.$: >-
        format("https://yuxugong.ai-smart-thing.cloud/v1/bazi/report/{}/async",
        $Input.result_type)
      body:
        year.$: $Context.Execution.Input.year
        month.$: $Context.Execution.Input.month
        day.$: $Context.Execution.Input.day
        hour.$: $Context.Execution.Input.hour
        minute.$: $Context.Execution.Input.minute
        gender.$: $Context.Execution.Input.gender
        report_version.$: $Context.Execution.Input.report _version
      headers:
        Content-Type: application/json
    Next: CheckInitialStatus
    OutputConstructor:
      data.$: stringToJson($Output.Body)
  - Type: Wait
    Name: Wait X Seconds
    Next: get_job_status
    Seconds: 30
    OutputConstructor:
      $: $Input
  - Type: Task
    Name: get_job_status
    Action: HTTP:Request
    TaskMode: RequestComplete
    Parameters:
      method: GET
      url.$: >-
        format("https://yuxugong.ai-smart-thing.cloud/v1/bazi/report/{}",
        $Input.data.result_id)
    Retry:
      - Errors:
          - FnF.ALL
        MaxAttempts: 3
        IntervalSeconds: 10
        BackoffRate: 2.0
    Next: Job Complete?
    OutputConstructor:
      data.$: stringToJson($Output.Body)
  - Type: Choice
    Name: Job Complete?
    Branches:
      - Condition: $Input.data.status=="SUCCESS"
        Next: Succeed
      - Condition: $Input.data.status=="PENDING" || $Input.data.status=="RUNNING"
        Next: Wait X Seconds
    Default: Fail
  - Type: Fail
    Name: Fail
    Code: failed
    End: true
  - Type: Succeed
    Name: Succeed
    End: true
  - Type: Choice
    Name: CheckInitialStatus
    Branches:
      - Condition: $Input.data.status == "SUCCESS"
        Next: get_job_status
    Default: Wait X Seconds
