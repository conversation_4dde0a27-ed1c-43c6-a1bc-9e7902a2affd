# Use uv based Python 3.10 as base image
FROM ghcr.io/astral-sh/uv:python3.10-bookworm-slim

# Set working directory in container
WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    TZ=Asia/Shanghai

# Install system dependencies and configure timezone
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        gcc \
        python3-dev \
        tzdata \
        curl \
        procps \
    && ln -snf /usr/share/zoneinfo/$TZ /etc/localtime \
    && echo $TZ > /etc/timezone \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY pyproject.toml .
COPY README.md .
COPY uv.lock .

RUN mkdir -p /app/logs /app/logs/nginx

# Install Python dependencies using uv
RUN uv pip install . --system

# Copy the rest of the application
COPY . .

# Make start scripts executable
RUN chmod +x /app/deploy/start.sh /app/deploy/start_arq_worker.sh

# Expose the port the app runs on
EXPOSE 5000

# Command to run the application
CMD ["/app/deploy/start.sh"]
