# 玉虚宫 (Yu<PERSON>uGong) - 算命 Backend

玉虚宫 (YuXuGong) is a modern divination backend service built with FastAPI, designed to provide fortune-telling and divination services through AI-powered interfaces.

## 🌟 Features

- RESTful API endpoints for divination services
- Integration with LLM providers (currently supports DeepSeek)
- Async request handling for better performance
- Docker support for easy deployment
- Environment-based configuration

## 🚀 Quick Start

### Prerequisites

- Python 3.10.12 or higher
- Docker (optional)

### Local Development

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/YuXuGong.git
   cd YuXuGong
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows, use `venv\Scripts\activate`
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

5. Run the development server:
   ```bash
   uvicorn app.main:app --reload
   # or
   uvicorn main:app --workers 4
   ```

### Local ARQ Worker

1. Run the ARQ worker:
   ```bash
   ./deploy/start_arq_worker.sh
   ```

2. Run ARQ tasks:
   ```bash
   # Tasks are enqueued through the API or programmatically
   # Example: python -c "import asyncio; from src.arq_app import create_redis_pool; asyncio.run(create_redis_pool().enqueue_job('test_task', 'Hello World'))"
   ```

### Docker Deployment

1. Build the Docker image:
   ```bash
   docker build -t yuxugong .
   ```

2. Run the container:
   ```bash
   docker run -p 8000:8000 --env-file .env yuxugong
   ```

### Run lint
```bash
make lint
```

### Run format
```bash
make format
```

## 📚 API Documentation

Once the server is running, you can access:
- API documentation: `http://localhost:8000/docs`
- Alternative API documentation: `http://localhost:8000/redoc`

### Example API Request
```python
import requests
response = requests.post(
"http://localhost:8000/v1/chat/completions",
json={
"messages": [
{"role": "user", "content": "请为我算一卦"}
],
"temperature": 0.7,
"provider": "deepseek"
}
)
print(response.json())
```
