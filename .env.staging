DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_API_BASE=https://api.deepseek.com/v1 

# database
DB_HOST=rm-2ze7k6r0s03x2t8sl.mysql.rds.aliyuncs.com
DB_PORT=3306
DB_USER=yuxugong
DB_PASSWORD=yxg!20250215
DB_NAME=yuxugong_staging

KONGWANG_AGENT_ID=bot-20250213150655-4l85z
YONGSHEN_AGENT_ID=bot-20250213150746-nfdl2
SHISHEN_AGENT_ID=bot-20250213150831-gwmpw
GEJV_AGENT_ID=bot-20250213150903-jmlkm
SHENSHA_AGENT_ID=bot-20250213150930-4842s
BAZI_AGENT_ID=bot-20250213152419-dcr5r
FIX_JSON_AGENT_ID=bot-20250215182559-gsdv7
DAYUN_AGENT_ID=bot-20250213201446-hfmfg
SHIYE_AGENT_ID=bot-20250212175700-w68gt
YINYUAN_AGENT_ID=bot-20250214165710-vr8j6
XUEYE_AGENT_ID=bot-20250214155438-k57ll
CAIYUN_AGENT_ID=bot-20250214151155-lfxbh
JIANKANG_AGENT_ID=bot-20250222131335-dxtql

ARK_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
ARK_API_KEY =5f19718d-2906-4213-8c3c-f44710edb1c6

ALIYUN_ACCESS_KEY_ID=LTAI5t5vyjmYniJ4RsLQ49Jd
ALIYUN_ACCESS_KEY_SECRET=i6ChDrV8Y6HUoGraYF4wOiY2VM22qY
ALIYUN_BUCKET_NAME=ai-smart-thing
ALIYUN_OSS_ENDPOINT=oss-cn-beijing-internal.aliyuncs.com
ALIYUN_BUCKET_ENV=staging

# Apple Store Config
APPLE_KEY_ID=2DV5F5KA83
APPLE_ISSUER_ID=04dabf93-cf35-4e77-9c45-b8515952316f
APPLE_BUNDLE_ID=com.aismartthing.suanming

JWT_SECRET_KEY=8d1f23a5e7b9c4f6h8j0k2m4n6p8r1t3v5x7z9
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=10080

ALIYUN_WORKFLOW_ENDPOINT=cn-beijing.fnf.aliyuncs.com

BASIC_MODEL_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
BASIC_MODEL_NAME=doubao-1-5-pro-256k-250115
BASIC_MODEL_KEY=5f19718d-2906-4213-8c3c-f44710edb1c6

REASON_MODEL_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
REASON_MODEL_NAME=doubao-seed-1-6-250615
REASON_MODEL_KEY=5f19718d-2906-4213-8c3c-f44710edb1c6

BASIC_MODEL_TEMPERATURE=0.5
BASIC_MODEL_TOP_P=0.7
BASIC_MODEL_MAX_TOKENS=12288
BASIC_MODEL_FREQUENCY_PENALTY=0.0
BASIC_MODEL_PRESENCE_PENALTY=0.0

REASON_MODEL_TEMPERATURE=0.3
REASON_MODEL_TOP_P=0.7
REASON_MODEL_MAX_TOKENS=16000
REASON_MODEL_FREQUENCY_PENALTY=0.0
REASON_MODEL_PRESENCE_PENALTY=0.0

DAILY_CHAT_LIMIT=30

# Redis配置
REDIS_HOST=r-2zes07dtcbmyl6yq9a.redis.rds.aliyuncs.com
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_BROKER_DB=1
REDIS_LOCK_DB=6

# Arq 配置
ARQ_QUEUE_NAME=staging_queue
ARQ_WORKER_CONCURRENCY=100

# WHETHER USE ARQ
# TODO: remove this after ARQ is fully launched
USE_ARQ_TO_GENERATE_REPORT=true
