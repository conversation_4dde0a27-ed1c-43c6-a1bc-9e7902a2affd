name: Build and Push Docker Image

on:
  push:
    branches: [ "main", "develop" ]
  workflow_dispatch:
    inputs:
      tag:
        description: 'Docker image tag (default: latest)'
        required: false
        default: ''
        type: string

jobs:
  build-image:
    runs-on: ubuntu-latest
    outputs:
      tag: ${{ steps.set-tag.outputs.tag }}
      env: ${{ steps.set-env.outputs.env }}
    env:
      REGISTRY_PUBLIC: crpi-2794svloicid00on.cn-beijing.personal.cr.aliyuncs.com
      REGISTRY_PRIVATE: crpi-2794svloicid00on-vpc.cn-beijing.personal.cr.aliyuncs.com
      REGISTRY_USERNAME: liuxing@1919451260043932
      REGISTRY_PASSWORD: zLZtWTskAstYFgdbG8_S%_S2I42eg%Vw
      IMAGE_NAME: ai-smart-thing/yuxugong
    steps:
      - name: Set environment and tag
        id: set-env
        run: |
          SHORT_SHA=${GITHUB_SHA::7}
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "env=prod" >> $GITHUB_OUTPUT
            echo "tag=prod-${SHORT_SHA}" >> $GITHUB_OUTPUT
          elif [ "${{ github.ref }}" = "refs/heads/develop" ]; then
            echo "env=staging" >> $GITHUB_OUTPUT
            echo "tag=staging-${SHORT_SHA}" >> $GITHUB_OUTPUT
          else
            echo "env=staging" >> $GITHUB_OUTPUT
            echo "tag=$(echo "${{ github.event.inputs.tag || github.ref_name }}" | tr '/' '-')-${SHORT_SHA}" >> $GITHUB_OUTPUT
          fi

      - name: Set tag output
        id: set-tag
        run: |
          echo "tag=${{ steps.set-env.outputs.tag }}" >> $GITHUB_OUTPUT

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: |
            image=moby/buildkit:v0.10.6

      - name: Calculate Docker cache key
        id: docker-cache-key
        run: |
          echo "key=docker-cache-${{ hashFiles('Dockerfile', 'requirements.txt') }}-${{ github.ref }}" >> $GITHUB_OUTPUT

      - name: Cache Docker layers
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ steps.docker-cache-key.outputs.key }}
          restore-keys: |
            docker-cache-${{ hashFiles('Dockerfile', 'requirements.txt') }}-
            docker-cache-

      - name: Login to Aliyun Container Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY_PUBLIC }}
          username: ${{ env.REGISTRY_USERNAME }}
          password: ${{ env.REGISTRY_PASSWORD }}

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ env.REGISTRY_PUBLIC }}/${{ env.IMAGE_NAME }}:${{ steps.set-env.outputs.tag }}
          platforms: linux/amd64
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max

      # Temporary fix for https://github.com/docker/build-push-action/issues/252
      # Move cache to avoid it growing indefinitely
      - name: Move cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache

      - name: Pull and push nginx image
        if: github.event_name != 'pull_request'
        run: |
          # Check if nginx image already exists in Aliyun registry (without downloading)
          if docker manifest inspect ${{ env.REGISTRY_PUBLIC }}/ai-smart-thing/nginx:latest >/dev/null 2>&1; then
            echo "nginx image already exists in Aliyun registry, skipping push"
          else
            echo "nginx image not found in Aliyun registry, pulling and pushing"
            # Pull official nginx:latest image
            docker pull nginx:latest

            # Tag it with Aliyun registry
            docker tag nginx:latest ${{ env.REGISTRY_PUBLIC }}/ai-smart-thing/nginx:latest

            # Push to Aliyun registry
            docker push ${{ env.REGISTRY_PUBLIC }}/ai-smart-thing/nginx:latest
          fi

      - name: Pull and push arq-ui image
        if: github.event_name != 'pull_request'
        run: |
          # Check if arq-ui image already exists in Aliyun registry (without downloading)
          if docker manifest inspect ${{ env.REGISTRY_PUBLIC }}/ai-smart-thing/arq-ui:latest >/dev/null 2>&1; then
            echo "arq-ui image already exists in Aliyun registry, skipping push"
          else
            echo "arq-ui image not found in Aliyun registry, pulling and pushing"
            # Pull official arq-ui:latest image
            docker pull antonk0/arq-ui:latest

            # Tag it with Aliyun registry
            docker tag antonk0/arq-ui:latest ${{ env.REGISTRY_PUBLIC }}/ai-smart-thing/arq-ui:latest

            # Push to Aliyun registry
            docker push ${{ env.REGISTRY_PUBLIC }}/ai-smart-thing/arq-ui:latest
          fi

  deploy-main-app:
    runs-on: ubuntu-latest
    needs: build-image
    if: github.event_name != 'pull_request'
    env:
      REGISTRY_PRIVATE: crpi-2794svloicid00on-vpc.cn-beijing.personal.cr.aliyuncs.com
      REGISTRY_USERNAME: liuxing@1919451260043932
      REGISTRY_PASSWORD: zLZtWTskAstYFgdbG8_S%_S2I42eg%Vw
      IMAGE_NAME: ai-smart-thing/yuxugong
      SSH_PRIVATE_KEY: |
            -----BEGIN OPENSSH PRIVATE KEY-----
            b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW
            QyNTUxOQAAACCNf1lDoguFW5R1LbS3seNxgBI3edSp+e5Ws+zL3CQDRQAAAJiQ7f1lkO39
            ZQAAAAtzc2gtZWQyNTUxOQAAACCNf1lDoguFW5R1LbS3seNxgBI3edSp+e5Ws+zL3CQDRQ
            AAAECYsflWQerKoSdQoH4DBGKPcLsL5Z9mNblLHkfdiTL3I41/WUOiC4VblHUttLex43GA
            Ejd51Kn57laz7MvcJANFAAAADmdpdGh1Yi1hY3Rpb25zAQIDBAUGBw==
            -----END OPENSSH PRIVATE KEY-----
      SSH_PROD_HOST: ************
      SSH_STAGING_HOST: ************
      SSH_USER: dev
    steps:
      - name: Set deployment variables
        run: |
          if [ "${{ needs.build-image.outputs.env }}" = "prod" ]; then
            echo "SSH_HOST=${{ env.SSH_PROD_HOST }}" >> $GITHUB_ENV
          else
            echo "SSH_HOST=${{ env.SSH_STAGING_HOST }}" >> $GITHUB_ENV
          fi

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy Main Application to Server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ env.SSH_HOST }}
          username: ${{ env.SSH_USER }}
          key: ${{ env.SSH_PRIVATE_KEY }}
          script: |
            cd /u/app/YuXuGong
            git fetch
            git checkout ${{ github.ref_name }}
            git fetch origin
            git reset --hard origin/${{ github.ref_name }}

            # mkdir for nginx log
            mkdir -p /u/app/YuXuGong/logs/nginx

            # docker login and pull image
            docker login --username ${{ env.REGISTRY_USERNAME }} --password ${{ env.REGISTRY_PASSWORD }} ${{ env.REGISTRY_PRIVATE }}
            docker pull ${{ env.REGISTRY_PRIVATE }}/${{ env.IMAGE_NAME }}:${{ needs.build-image.outputs.tag }}

            # set environment variables for docker-compose
            export TAG=${{ needs.build-image.outputs.tag }}
            export ENV=${{ needs.build-image.outputs.env }}

            # Run docker-compose
            docker stack deploy --prune -c docker-compose.yml -c docker-compose.${{ needs.build-image.outputs.env }}.yml yuxugong-app --with-registry-auth

            # 600 秒超时
            # ---------- 等待 ----------
            wait_stack () {
              local stack=$1 timeout=$2 interval=5 elapsed=0

              echo "⏳ 等待 stack '$stack' 副本到位并全部 Running…"

              while true; do
                sleep $interval; ((elapsed+=interval))
                # 统计 service 副本
                local replicas_ok=true
                echo "—— Service 副本状态 ——"
                docker stack services "$stack" --format '{{.Name}} {{.Replicas}}' |
                while read svc rep; do
                  echo "  $svc  ->  $rep"
                  # rep = "x/y"
                  local cur=${rep%%/*} tot=${rep##*/}
                  if (( cur < tot )); then replicas_ok=false; fi
                done

                # 如果副本未全部到位
                if [[ $replicas_ok == false ]]; then
                  echo "❗ 副本不足，继续等待…"
                else
                  # 检查 task 状态
                  local bad_cnt
                  bad_cnt=$(docker service ps $(docker stack services "$stack" -q) \
                              --filter 'desired-state=running' \
                              --format '{{.CurrentState}}' | \
                            grep -vcE 'Running' || true)
                  echo "—— Task state 检查 ——"
                  echo "  非 Running/Starting 的 task 数量：$bad_cnt"

                  [[ $bad_cnt -eq 0 ]] && { echo "✅ 全部就绪"; return 0; }
                fi

                # 到这里说明还没就绪
                if (( elapsed >= timeout )); then
                  echo "❌ 超时 $timeout 秒仍未就绪"; return 1
                fi
              done
            }

            wait_stack yuxugong-app 600 || { echo "Stack 未就绪，退出"; exit 1; }

  deploy-arq-workers:
    runs-on: ubuntu-latest
    needs: build-image
    if: github.event_name != 'pull_request'
    env:
      REGISTRY_PRIVATE: crpi-2794svloicid00on-vpc.cn-beijing.personal.cr.aliyuncs.com
      REGISTRY_USERNAME: liuxing@1919451260043932
      REGISTRY_PASSWORD: zLZtWTskAstYFgdbG8_S%_S2I42eg%Vw
      IMAGE_NAME: ai-smart-thing/yuxugong
      SSH_PRIVATE_KEY: |
            -----BEGIN OPENSSH PRIVATE KEY-----
            b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW
            QyNTUxOQAAACCNf1lDoguFW5R1LbS3seNxgBI3edSp+e5Ws+zL3CQDRQAAAJiQ7f1lkO39
            ZQAAAAtzc2gtZWQyNTUxOQAAACCNf1lDoguFW5R1LbS3seNxgBI3edSp+e5Ws+zL3CQDRQ
            AAAECYsflWQerKoSdQoH4DBGKPcLsL5Z9mNblLHkfdiTL3I41/WUOiC4VblHUttLex43GA
            Ejd51Kn57laz7MvcJANFAAAADmdpdGh1Yi1hY3Rpb25zAQIDBAUGBw==
            -----END OPENSSH PRIVATE KEY-----
      SSH_PROD_HOST: ************
      SSH_STAGING_HOST: **************  # ARQ workers use different staging server
      SSH_USER: dev
    steps:
      - name: Set deployment variables for ARQ workers
        run: |
          if [ "${{ needs.build-image.outputs.env }}" = "prod" ]; then
            echo "SSH_HOST=${{ env.SSH_PROD_HOST }}" >> $GITHUB_ENV
          else
            echo "SSH_HOST=${{ env.SSH_STAGING_HOST }}" >> $GITHUB_ENV
          fi

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy ARQ Workers to Server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ env.SSH_HOST }}
          username: ${{ env.SSH_USER }}
          key: ${{ env.SSH_PRIVATE_KEY }}
          script: |
            cd /u/app/YuXuGong
            git fetch
            git checkout ${{ github.ref_name }}
            git fetch origin
            git reset --hard origin/${{ github.ref_name }}

            # mkdir for logs
            mkdir -p /u/app/YuXuGong/logs

            # docker login and pull image
            docker login --username ${{ env.REGISTRY_USERNAME }} --password ${{ env.REGISTRY_PASSWORD }} ${{ env.REGISTRY_PRIVATE }}
            docker pull ${{ env.REGISTRY_PRIVATE }}/${{ env.IMAGE_NAME }}:${{ needs.build-image.outputs.tag }}

            # set environment variables for docker-compose
            export TAG=${{ needs.build-image.outputs.tag }}
            export ENV=${{ needs.build-image.outputs.env }}

            # Deploy ARQ workers using Docker Compose (not Swarm)
            if [ "${{ needs.build-image.outputs.env }}" = "staging" ]; then
              # Staging: 2 ARQ worker instances
              docker compose -f docker-compose.arq-worker.yml -f docker-compose.arq-worker.staging.yml up -d --scale arq-worker=1
            else
              # Production: 3 ARQ worker instances
              docker compose -f docker-compose.arq-worker.yml -f docker-compose.arq-worker.prod.yml up -d --scale arq-worker=1
            fi

            # Wait for ARQ workers to be healthy
            echo "⏳ 等待 ARQ workers 启动并健康检查通过..."
            timeout=300
            elapsed=0
            interval=10

            while [ $elapsed -lt $timeout ]; do
              sleep $interval
              elapsed=$((elapsed + interval))

              # Check if all ARQ worker containers are healthy
              unhealthy_count=$(docker compose -f docker-compose.arq-worker.yml -f docker-compose.arq-worker.${{ needs.build-image.outputs.env }}.yml ps | grep -c "unhealthy\|starting" || true)

              if [ "$unhealthy_count" -eq 0 ]; then
                echo "✅ 所有 ARQ workers 已启动并通过健康检查"
                break
              else
                echo "❗ 还有 $unhealthy_count 个 ARQ worker 未就绪，继续等待..."
              fi

              if [ $elapsed -ge $timeout ]; then
                echo "❌ 超时 $timeout 秒，ARQ workers 未全部就绪"
                docker compose -f docker-compose.arq-worker.yml -f docker-compose.arq-worker.${{ needs.build-image.outputs.env }}.yml ps
                exit 1
              fi
            done

  cleanup-docker:
    runs-on: ubuntu-latest
    needs: [build-image, deploy-main-app, deploy-arq-workers]
    if: github.event_name != 'pull_request' && always() && (needs.deploy-main-app.result == 'success' || needs.deploy-arq-workers.result == 'success')
    env:
      SSH_PRIVATE_KEY: |
            -----BEGIN OPENSSH PRIVATE KEY-----
            b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW
            QyNTUxOQAAACCNf1lDoguFW5R1LbS3seNxgBI3edSp+e5Ws+zL3CQDRQAAAJiQ7f1lkO39
            ZQAAAAtzc2gtZWQyNTUxOQAAACCNf1lDoguFW5R1LbS3seNxgBI3edSp+e5Ws+zL3CQDRQ
            AAAECYsflWQerKoSdQoH4DBGKPcLsL5Z9mNblLHkfdiTL3I41/WUOiC4VblHUttLex43GA
            Ejd51Kn57laz7MvcJANFAAAADmdpdGh1Yi1hY3Rpb25zAQIDBAUGBw==
            -----END OPENSSH PRIVATE KEY-----
      SSH_PROD_HOST: ************
      SSH_STAGING_HOST_MAIN: ************
      SSH_STAGING_HOST_ARQ: **************
      SSH_USER: dev
    steps:
      - name: Set cleanup variables
        run: |
          if [ "${{ needs.build-image.outputs.env }}" = "prod" ]; then
            echo "MAIN_HOST=${{ env.SSH_PROD_HOST }}" >> $GITHUB_ENV
            echo "ARQ_HOST=${{ env.SSH_PROD_HOST }}" >> $GITHUB_ENV
          else
            echo "MAIN_HOST=${{ env.SSH_STAGING_HOST_MAIN }}" >> $GITHUB_ENV
            echo "ARQ_HOST=${{ env.SSH_STAGING_HOST_ARQ }}" >> $GITHUB_ENV
          fi

      - name: Cleanup Docker on Main App Server
        if: needs.deploy-main-app.result == 'success'
        uses: appleboy/ssh-action@master
        with:
          host: ${{ env.MAIN_HOST }}
          username: ${{ env.SSH_USER }}
          key: ${{ env.SSH_PRIVATE_KEY }}
          script: |
            echo "🧹 开始清理主应用服务器 Docker 资源..."
            
            # 清理未使用的镜像、容器、网络和构建缓存
            docker system prune -a -f
            
            # 清理未使用的卷
            docker volume prune -f
            
            # 显示清理后的磁盘使用情况
            echo "主应用服务器清理完成，当前 Docker 磁盘使用情况："
            docker system df

      - name: Cleanup Docker on ARQ Workers Server
        if: needs.deploy-arq-workers.result == 'success'
        uses: appleboy/ssh-action@master
        with:
          host: ${{ env.ARQ_HOST }}
          username: ${{ env.SSH_USER }}
          key: ${{ env.SSH_PRIVATE_KEY }}
          script: |
            echo "🧹 开始清理 ARQ Workers 服务器 Docker 资源..."
            
            # 清理未使用的镜像、容器、网络和构建缓存
            docker system prune -a -f
            
            # 清理未使用的卷
            docker volume prune -f
            
            # 显示清理后的磁盘使用情况
            echo "ARQ Workers 服务器清理完成，当前 Docker 磁盘使用情况："
            docker system df
            