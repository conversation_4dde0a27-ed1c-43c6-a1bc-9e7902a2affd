# docker-compose.staging.yml (Staging 环境专用)
version: '3.8'

services:
  yuxugong:
    deploy:
      replicas: 1 # Staging 环境只跑 1 个实例
      update_config:
        parallelism: 1
        order: stop-first # 先停止旧容器，再启动新容器
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 512M

  nginx:
    volumes:
      # 挂载staging环境的 nginx 配置
      - ./nginx/nginx.staging.conf:/etc/nginx/nginx.conf:ro

  arq-ui:
    environment:
      - REDIS_HOST=r-2zes07dtcbmyl6yq9a.redis.rds.aliyuncs.com
      - REDIS_PASSWORD=
      - REDIS_PORT=6379
      - REDIS_SSL=False
      - REDIS_DB=1
      - QUEUE_NAME=staging_queue
      - TZ=Asia/Shanghai
    deploy:
      replicas: 1 # Staging 环境运行 1 个 arq-ui 实例
      update_config:
        parallelism: 1
        order: stop-first # 先停止旧容器，再启动新容器
      resources:
        limits:
          memory: 512M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'
        